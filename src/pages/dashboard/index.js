import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip as ChartTooltip, // Renamed to avoid conflict with React Tooltip
    Legend as ChartLegend, // Renamed to avoid conflict with React Legend
    ArcElement, // For Pie charts
} from 'chart.js';
import { Bar, Pie } from 'react-chartjs-2'; // Use Doughnut for pie charts
import { format, isSameMonth, isSameWeek, isSameDay, subMonths, subDays, formatDistanceToNow } from 'date-fns';
import config from "../../../config.json"; // Assuming your config is correctly linked
import { ChevronDownIcon } from '@heroicons/react/20/solid'; // For dropdown icons
import LoadingModal from '../../components/Loading';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    ChartTooltip,
    ChartLegend,
    ArcElement // Register ArcElement for Doughnut charts
);

// Custom tooltip for Doughnut charts to show value and percentage
const doughnutTooltip = {
    callbacks: {
        label: function (context) {
            let label = context.label || '';
            if (label) {
                label += ': ';
            }
            if (context.parsed !== null) {
                const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                const value = context.parsed;
                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) + '%' : '0%';
                label += `${value.toLocaleString('en-US')} QAR (${percentage})`;
            }
            return label;
        }
    }
};

export default function DashboardPage() {
    const [kycUsers, setKycUsers] = useState([]);
    const [creditLines, setCreditLines] = useState([]);
    const [lenderDetails, setLenderDetails] = useState(null);
    const [error, setError] = useState(null);
    const [loading, setLoading] = useState(true);
    const [exportDropdownOpen, setExportDropdownOpen] = useState(false);
    const [yearDropdownOpen, setYearDropdownOpen] = useState(false);
    const [selectedPeriod, setSelectedPeriod] = useState('This Year'); // Default to 'This Year'
    console.log(lenderDetails);

    // Simulate invoice data (replace with actual API calls if available)
    const [invoiceData] = useState({
        totalInvoicesReceived: 7,
        totalInvoicesDiscounted: 3,
        invoicesDisbursed: 2,
        totalDisbursedAmount: 3150, // QAR
        totalRepaymentDue: 850, // QAR
    });

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            setError(null);

            try {
                // Fetch KYC submissions
                const usersResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`);
                if (!usersResponse.ok) {
                    throw new Error(`Failed to fetch KYC submissions: ${usersResponse.status}`);
                }
                const usersData = await usersResponse.json();

                // Fetch lender details
                const lenderId = localStorage.getItem('userId');
                const lenderResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/lenders/${lenderId}`);
                if (!lenderResponse.ok) {
                    throw new Error(`Failed to fetch lender: ${lenderResponse.status}`);
                }
                const lenderData = await lenderResponse.json();
                setLenderDetails(lenderData);

                // Fetch credit lines
                const creditLinesResponse = await fetch(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`);
                if (!creditLinesResponse.ok) {
                    throw new Error(`Failed to fetch credit lines: ${creditLinesResponse.status}`);
                }
                const creditLinesData = await creditLinesResponse.json();

                if (usersData?.success && usersData.kycs?.length > 0) {
                    // Enrich KYC users with credit line status for the table
                    const usersWithCreditLines = usersData.kycs.map((user) => {
                        const userCreditLine = creditLinesData && Array.isArray(creditLinesData)
                            ? creditLinesData.find((cl) => cl.userId === user.userId)
                            : null;
                        const creditLineStatus = userCreditLine ? userCreditLine.creditLineStatus : 'UNDER_REVIEW'; // Default for new apps
                        return {
                            ...user,
                            creditLineStatus: creditLineStatus,
                        };
                    });
                    setKycUsers(usersWithCreditLines);
                } else {
                    setKycUsers([]);
                }
                setCreditLines(creditLinesData || []);

            } catch (err) {
                console.error("Error fetching dashboard data:", err);
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const filterDataByPeriod = (data, dateKey, period) => {
        const now = new Date();
        return data.filter(item => {
            const itemDate = new Date(item[dateKey]);
            switch (period) {
                case 'This Month':
                    return isSameMonth(itemDate, now);
                case 'This Week':
                    return isSameWeek(itemDate, now);
                case 'Today':
                    return isSameDay(itemDate, now);
                case 'This Year':
                default:
                    return itemDate.getFullYear() === now.getFullYear();
            }
        });
    };

    const filteredKycUsers = useMemo(() => filterDataByPeriod(kycUsers, 'insertedOn', selectedPeriod), [kycUsers, selectedPeriod]);
    const filteredCreditLines = useMemo(() => filterDataByPeriod(creditLines, 'applicationDate', selectedPeriod), [creditLines, selectedPeriod]);

    // Dashboard Metric Calculations
    const totalApplications = filteredKycUsers.length;
    const newApplications = filteredCreditLines.filter(line => line.creditLineStatus === 'UNDER_REVIEW').length;
    const offerAccepted = filteredCreditLines.filter(line => line.offerAccepted).length;
    const applicationActivated = filteredCreditLines.filter(line => line.creditLineStatus === 'ACTIVE').length;
    const totalCreditLineActivated = filteredCreditLines
        .filter(line => line.creditLineStatus === 'ACTIVE')
        .reduce((sum, line) => sum + (line.creditLimit || 0), 0);

    // Chart Data Preparation
    const creditLineStatusData = {
        labels: ['Active', 'Under Review', 'Rejected'],
        datasets: [
            {
                label: 'Credit Line Status',
                data: [
                    filteredCreditLines.filter(line => line.creditLineStatus === 'ACTIVE').length,
                    filteredCreditLines.filter(line => line.creditLineStatus === 'UNDER_REVIEW').length,
                    filteredCreditLines.filter(line => line.creditLineStatus === 'REJECTED').length,
                ],
                backgroundColor: ['#014e20', '#FACC15', '#EF4444'], // Design colors: green, yellow, red
                borderColor: ['#014e20', '#FACC15', '#EF4444'],
                borderWidth: 1,
            },
        ],
    };

    const totalAvailableBalance = filteredCreditLines.reduce((sum, line) => sum + (line.availableBalance || 0), 0);
    const totalUtilizedAmount = filteredCreditLines.reduce((sum, line) => sum + (line.utilizedAmount || 0), 0);

    const utilizationData = {
        labels: ['Utilized', 'Available'],
        datasets: [
            {
                label: 'Credit Line Utilization',
                data: [totalUtilizedAmount, totalAvailableBalance],
                backgroundColor: ['#2563EB', '#014d20'], // Design colors: blue, gray
                borderColor: ['#2563EB', '#D1D5DB'],
                borderWidth: 1,
            },
        ],
    };

    // Bar Chart: Application Received over time
    const getApplicationBarChartData = () => {
        const dataMap = new Map();
        const now = new Date();

        filteredCreditLines.forEach(line => {
            const date = new Date(line.applicationDate);
            let key;
            if (selectedPeriod === 'This Month') {
                key = format(date, 'MMM dd');
            } else if (selectedPeriod === 'This Week') {
                key = format(date, 'EEE'); // Mon, Tue, etc.
            } else if (selectedPeriod === 'Today') {
                key = format(date, 'HH:00'); // Hourly
            } else { // This Year (default)
                key = format(date, 'MMM'); // Jan, Feb, etc.
            }
            dataMap.set(key, (dataMap.get(key) || 0) + 1);
        });

        // Generate all labels for the period, even if no data
        let labels = [];
        if (selectedPeriod === 'This Month') {
            const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
            for (let i = 1; i <= daysInMonth; i++) {
                labels.push(format(new Date(now.getFullYear(), now.getMonth(), i), 'MMM dd'));
            }
        } else if (selectedPeriod === 'This Week') {
            const startOfWeek = subDays(now, now.getDay()); // Sunday as start of week
            for (let i = 0; i < 7; i++) {
                labels.push(format(subDays(startOfWeek, -i), 'EEE'));
            }
        } else if (selectedPeriod === 'Today') {
            for (let i = 0; i < 24; i++) {
                labels.push(`${i.toString().padStart(2, '0')}:00`);
            }
        } else { // This Year
            for (let i = 0; i < 12; i++) {
                labels.push(format(subMonths(now, -i), 'MMM'));
            }
        }

        const data = labels.map(label => dataMap.get(label) || 0);

        return {
            labels,
            datasets: [
                {
                    label: 'Applications',
                    data: data,
                    backgroundColor: '#014e20', // Green bar color
                    borderRadius: 4, // Rounded corners for bars
                },
            ],
        };
    };

    const barChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false,
            },
            title: {
                display: false,
            },
        },
        scales: {
            x: {
                grid: {
                    display: false,
                },
                ticks: {
                    // Adjust font size for x-axis labels on smaller screens
                    font: {
                        size: 10, // Default font size for x-axis
                    },
                    autoSkip: true, // Automatically skip labels to prevent overlap
                    maxRotation: 45,
                    minRotation: 0,
                },
            },
            y: {
                beginAtZero: true,
                ticks: {
                    precision: 0,
                    // Adjust font size for y-axis labels on smaller screens
                    font: {
                        size: 10, // Default font size for y-axis
                    },
                },
            },
        },
    };

    const pieChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'right', // Place legend on the right
                labels: {
                    boxWidth: 12,
                    boxHeight: 12,
                    padding: 20,
                    font: {
                        size: 10, // Smaller font size for legend on mobile
                    },
                    usePointStyle: true, // Use circle for legend markers
                },
            },
            tooltip: doughnutTooltip, // Apply custom tooltip
        },
    };

    // New Applications Table Data
    const latestNewApplications = filteredKycUsers
        .filter(user => user.creditLineStatus === 'UNDER_REVIEW' || user.verificationStatus === 'PENDING') // Filter for new applications
        .sort((a, b) => new Date(b.insertedOn) - new Date(a.insertedOn)) // Sort by most recent
        .slice(0, 5); // Limit to 5

    if (loading) {
        return <LoadingModal />;
    }

    return (
        <div className="p-4 md:p-6 lg:p-8 space-y-6 bg-gray-50 min-h-screen text-gray-800">
            {/* Top Section: Title and Dropdowns */}
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-4 md:mb-6 gap-3">
                <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-800">Lender Dashboard</h1>
                <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                    <div className="relative w-full sm:w-auto">
                        <button
                            className="flex items-center justify-between px-3 py-2 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full"
                            onClick={() => setExportDropdownOpen(!exportDropdownOpen)}
                        >
                            Export
                            <ChevronDownIcon className="ml-2 h-4 w-4" />
                        </button>
                        {exportDropdownOpen && (
                            <div className="absolute right-0 mt-2 w-full sm:w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                                <ul className="py-1">
                                    <li className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Export PDF</li>
                                    <li className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer">Export CSV</li>
                                </ul>
                            </div>
                        )}
                    </div>
                    <div className="relative w-full sm:w-auto">
                        <button
                            className="flex items-center justify-between px-3 py-2 text-sm border border-gray-300 rounded-md bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full"
                            onClick={() => setYearDropdownOpen(!yearDropdownOpen)}
                        >
                            {selectedPeriod}
                            <ChevronDownIcon className="ml-2 h-4 w-4" />
                        </button>
                        {yearDropdownOpen && (
                            <div className="absolute right-0 mt-2 w-full sm:w-40 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                                <ul className="py-1">
                                    {['This Year', 'This Month', 'This Week', 'Today'].map(period => (
                                        <li
                                            key={period}
                                            className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                                            onClick={() => {
                                                setSelectedPeriod(period);
                                                setYearDropdownOpen(false);
                                            }}
                                        >
                                            {period}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {loading && (
                <div className="flex justify-center items-center h-40">
                    <div className="animate-spin h-10 w-10 border-4 border-gray-500 border-t-transparent rounded-full"></div>
                </div>
            )}

            {error && <div className="text-red-600 bg-red-100 p-4 rounded mb-4 text-sm">Error: {error}</div>}

            {/* Metric Cards Section (2 rows of 5 on desktop, 2 per row on mobile) */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-6 mb-8">
                {/* Row 1 Metrics */}
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Total Applications</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">{totalApplications}</p>
                </div>
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">New Applications</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">{newApplications}</p>
                </div>
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Offer Accepted</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">{offerAccepted}</p>
                </div>
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Application Activated</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">{applicationActivated}</p>
                </div>
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Total Credit Line Activated</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">
                        {totalCreditLineActivated.toLocaleString('en-US', { style: 'currency', currency: 'QAR' })}
                    </p>
                </div>

                {/* Row 2 Metrics (Simulated from static data) */}
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Total Invoices Received</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">{invoiceData.totalInvoicesReceived}</p>
                </div>
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Total Invoices Discounted</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">{invoiceData.totalInvoicesDiscounted}</p>
                </div>
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Invoices Disbursed</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">{invoiceData.invoicesDisbursed}</p>
                </div>
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Total Disbursed Amount</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">
                        {invoiceData.totalDisbursedAmount.toLocaleString('en-US', { style: 'currency', currency: 'QAR' })}
                    </p>
                </div>
                <div className="bg-white shadow rounded-lg p-3 sm:p-4 flex flex-col items-start justify-center">
                    <p className="text-xs sm:text-sm text-gray-600">Total Repayment Due</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">
                        {invoiceData.totalRepaymentDue.toLocaleString('en-US', { style: 'currency', currency: 'QAR' })}
                    </p>
                </div>
            </div>

            {/* Charts Section */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {/* Application Received Bar Chart (60%) */}
                <div className="lg:col-span-2 bg-white shadow rounded-lg p-4 sm:p-6">
                    <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4">Application Received</h3>
                    <div style={{ height: '300px' }}> {/* Fixed height for responsiveness */}
                        <Bar data={getApplicationBarChartData()} options={barChartOptions} />
                    </div>
                </div>


                <div className="lg:col-span-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-6">
                    <div className="bg-white shadow rounded-lg p-4 sm:p-6 flex flex-col items-center">
                        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4">Credit Line Utilization</h3>
                        <div className="relative h-40 w-40 sm:h-48 sm:w-48 mx-auto"> {/* Adjusted size for smaller screens */}
                            {/* Changed Doughnut to Pie */}
                            <Pie data={utilizationData} options={pieChartOptions} />
                            <div className="absolute inset-0 flex items-center justify-center">
                                {/* You can add a total here if needed, e.g., totalCreditLimit.toLocaleString() */}
                            </div>
                        </div>
                    </div>
                    <div className="bg-white shadow rounded-lg p-4 sm:p-6 flex flex-col items-center">
                        <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4">Credit Line Status</h3>
                        <div className="relative h-40 w-40 sm:h-48 sm:w-48 mx-auto"> {/* Adjusted size for smaller screens */}
                            {/* Changed Doughnut to Pie */}
                            <Pie data={creditLineStatusData} options={pieChartOptions} />
                        </div>
                    </div>
                </div>
            </div>

            {/* New Applications Table */}
            <div className="bg-white shadow rounded-lg p-4 sm:p-6">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg sm:text-xl font-semibold text-gray-800">New Application</h3>
                    <Link href="/creditAssessment" className="text-blue-600 hover:underline text-sm font-medium">
                        View All
                    </Link>
                </div>
                <div className="overflow-x-auto"> {/* This makes the table horizontally scrollable on small screens */}
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th scope="col" className="px-4 py-2 sm:px-6 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Business Name
                                </th>
                                <th scope="col" className="px-4 py-2 sm:px-6 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Borrower
                                </th>
                                <th scope="col" className="px-4 py-2 sm:px-6 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Phone
                                </th>
                                <th scope="col" className="px-4 py-2 sm:px-6 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Applied
                                </th>
                                <th scope="col" className="px-4 py-2 sm:px-6 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Application Age
                                </th>
                                <th scope="col" className="px-4 py-2 sm:px-6 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    KYC Status
                                </th>
                                <th scope="col" className="px-4 py-2 sm:px-6 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Credit Report
                                </th>
                                <th scope="col" className="px-4 py-2 sm:px-6 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Details
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {latestNewApplications.length > 0 ? (
                                latestNewApplications.map((user) => (
                                    <tr key={user._id}>
                                        <td className="px-4 py-3 sm:px-6 sm:py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {user.businessName || 'N/A'}
                                        </td>
                                        <td className="px-4 py-3 sm:px-6 sm:py-4 whitespace-nowrap text-sm text-gray-500">
                                            {user.firstName} {user.lastName}<br />
                                            <a href={`mailto:${user.email}`} className="text-blue-600 hover:underline">{user.email}</a>
                                        </td>
                                        <td className="px-4 py-3 sm:px-6 sm:py-4 whitespace-nowrap text-sm text-gray-500">
                                            {user.phone || 'N/A'}
                                        </td>
                                        <td className="px-4 py-3 sm:px-6 sm:py-4 whitespace-nowrap text-sm text-gray-500">
                                            {user.insertedOn ? format(new Date(user.insertedOn), 'dd MMM, HH:mm') : 'N/A'}
                                        </td>
                                        <td className="px-4 py-3 sm:px-6 sm:py-4 whitespace-nowrap text-sm text-gray-500">
                                            {user.insertedOn ? formatDistanceToNow(new Date(user.insertedOn), { addSuffix: true }) : 'N/A'}
                                        </td>
                                        <td className="px-4 py-3 sm:px-6 sm:py-4 whitespace-nowrap text-sm">
                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.verificationStatus === 'APPROVED' ? 'bg-green-100 text-green-800' :
                                                user.verificationStatus === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-gray-100 text-gray-800'
                                                }`}>
                                                {user.verificationStatus || 'N/A'}
                                            </span>
                                        </td>
                                        <td className="px-4 py-3 sm:px-6 sm:py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer">
                                            <a
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                href={user?.camFile?.signedUrl}
                                            >
                                                View ↗
                                            </a>
                                        </td>
                                        <Link href="/creditAssessment" className="text-blue-600 hover:underline text-sm font-medium">
                                            KYC Details
                                        </Link>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan="9" className="px-4 py-3 sm:px-6 sm:py-4 text-center text-sm text-gray-500">
                                        No new applications found for the selected period.
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
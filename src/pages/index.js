import { useState } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import config from "../../config.json"; // Ensure this path is correct for your project structure

export default function Login() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/superadmin/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password, role: "lenderAdmin" }),
      });

      const data = await response.json();

      if (response.ok) {
        console.log("Login response:", data);
        // Store user ID in localStorage
        localStorage.setItem('userId', data.superadmin._id);

        // Set the token - use the token from response or create a session token
        const token = data.token || data.superadmin._id; // Fallback to user ID if no token
        Cookies.set('token', token, {
          expires: 7,
          path: '/', // Make cookie available across all paths
          sameSite: 'strict'
        });

        console.log("Token set:", token);
        router.push('/dashboard');
      } else {
        setError(data.message || 'Login failed');
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Failed to connect to the server');
    }
  };

  return (
    // This parent flex container ensures the content inside (login form) is centered
    // vertically and horizontally on smaller screens.
    <div className="flex flex-col md:flex-row min-h-screen bg-gray-100 font-sans antialiased">
      {/* Left Side: Login Form */}
      {/* This flex container will handle centering its content on all screens */}
      <div className="w-full md:w-1/2 flex flex-col items-center justify-center p-6 sm:p-8 lg:p-12">
        {/* Login Form Container */}
        <div className="bg-white p-8 sm:p-10 rounded-2xl shadow-xl w-full max-w-md transform transition-all duration-300 hover:shadow-2xl">
          <h1 className="text-4xl font-extrabold mb-8 text-center text-gray-800 tracking-tight">Lender Login</h1>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="username" className="block text-gray-700 text-sm font-semibold mb-2">
                Username
              </label>
              <input
                type="text"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-green-200 focus:border-green-500 outline-none transition-all duration-200 text-gray-800 placeholder-gray-400"
                placeholder="Enter your username"
                required
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-gray-700 text-sm font-semibold mb-2">
                Password
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-green-200 focus:border-green-500 outline-none transition-all duration-200 text-gray-800 placeholder-gray-400"
                placeholder="Enter your password"
                required
              />
            </div>
            {error && <p className="text-red-600 text-sm mt-2 text-center font-medium animate-pulse">{error}</p>}
            <button
              type="submit"
              className="w-full bg-green-600 text-white p-3 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-300 focus:ring-opacity-75 transition-all duration-200 font-bold text-lg tracking-wide"
            >
              Login
            </button>
          </form>
        </div>
      </div>

      {/* Right Side: Image */}
      {/* Changed 'md:flex' to 'md:block' to ensure it's visible on larger screens */}
      {/* Also added 'items-center' and 'justify-center' to the main container */}
      {/* so the single form div is centered when the image is hidden */}
      <div
        className="hidden md:block md:w-1/2 bg-cover bg-center bg-no-repeat relative overflow-hidden rounded-bl-[50px] md:rounded-bl-none md:rounded-tl-[50px]"
        style={{ backgroundImage: "url('/backdrop.png')" }}
      >
        <div className="absolute inset-0 bg-gradient-to-t from-green-800/40 via-transparent to-transparent"></div>
      </div>
    </div>
  );
}
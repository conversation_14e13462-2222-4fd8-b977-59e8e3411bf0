import React, { useState } from 'react';
import StatusBadge from '../../components/StatusBadge';

export default function Approvals() {
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [editedInvoiceData, setEditedInvoiceData] = useState(null);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);

  // Lender approval statuses
  const lenderStatuses = [
    "Approval in progress",
    "Approved",
    "Rejected",
    "More info required/on hold",
    "Discounting in progress",
    "Closed"
  ];

  // Initial data with invoice discounting applications
  const [pendingApprovals, setPendingApprovals] = useState([
    {
      id: 1,
      company: "GLOBAL OILS FACTORY",
      type: "Invoice Discounting",
      invoiceNumber: '7295',
      amount: "QAR 87,000.00",
      madadScore: 82,
      status: "Approval in progress",
      invoiceDate: '2024-12-16',
      dueDate: '2025-01-15',
      description: 'Raw Material Supply',
      reference: 'PO-2024-001',
      supplier: 'GLOBAL OILS FACTORY',
      customer: 'BIG TRADERS',
      customerAddress: 'BIG TRADERS',
      billingAddress: 'N/A',
      gstin: '27AAPFU0939F1ZV',
      signedUrl: '/assets/invoice.pdf'
    }
  ]);

  // const STATUS_STYLES = {
  //   'Approval in progress': 'bg-blue-100 text-blue-800',
  //   'Approved': 'bg-green-100 text-green-800',
  //   'Rejected': 'bg-red-100 text-red-800',
  //   'More info required/on hold': 'bg-yellow-100 text-yellow-800',
  //   'Discounting in progress': 'bg-indigo-100 text-indigo-800',
  //   'Closed': 'bg-gray-100 text-gray-800'
  // };

  const getStatusColorDot = (status) => {
    const colors = {
      'Approval in progress': 'bg-blue-500',
      'Approved': 'bg-green-500',
      'Rejected': 'bg-red-500',
      'More info required/on hold': 'bg-yellow-500',
      'Discounting in progress': 'bg-indigo-500',
      'Closed': 'bg-gray-500'
    };
    return colors[status] || 'bg-gray-500';
  };

  const handleReview = (invoice) => {
    setSelectedInvoice(invoice);
    setEditedInvoiceData({ ...invoice });
    setShowReviewModal(true);
  };

  const handleStatusSelect = (newStatus) => {
    if (selectedInvoice) {
      const updatedApprovals = pendingApprovals.map(approval =>
        approval.id === selectedInvoice.id
          ? { ...approval, status: newStatus }
          : approval
      );

      setPendingApprovals(updatedApprovals);
      setSelectedInvoice({ ...selectedInvoice, status: newStatus });
      setEditedInvoiceData({ ...editedInvoiceData, status: newStatus });
      setShowStatusDropdown(false);
    }
  };

  const handleToggleStatusDropdown = (e) => {
    e.stopPropagation();
    setShowStatusDropdown(!showStatusDropdown);
  };

  // Handle field changes in invoice details
  const handleInvoiceFieldChange = (field, value) => {
    setEditedInvoiceData(prevData => ({
      ...prevData,
      [field]: value
    }));
  };

  // Save edited invoice data
  const handleSaveInvoiceData = () => {
    const updatedApprovals = pendingApprovals.map(approval =>
      approval.id === selectedInvoice.id
        ? { ...editedInvoiceData }
        : approval
    );

    setPendingApprovals(updatedApprovals);
    setSelectedInvoice(editedInvoiceData);
    alert("Invoice details updated successfully!");
  };

  // Close dropdown when clicking outside
  const handleClickOutside = () => {
    if (showStatusDropdown) {
      setShowStatusDropdown(false);
    }
  };

  return (
    <div className="p-6" onClick={handleClickOutside}>
      <h1 className="text-2xl font-bold mb-6">Invoice Discounting Approvals</h1>

      {/* Status Legend */}
      <div className="flex flex-wrap gap-4 bg-white p-4 rounded-lg shadow-md mb-6">
        <h3 className="w-full text-sm font-medium mb-2">Lender Status Legend:</h3>
        {lenderStatuses.map(status => (
          <div key={status} className="flex items-center space-x-2">
            <div className={`w-3 h-3 ${getStatusColorDot(status)} rounded-full`}></div>
            <span className="text-sm">{status}</span>
          </div>
        ))}
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Pending Approvals</h2>
          <div className="space-x-2">
            <select className="border rounded-md p-2">
              <option>All Status</option>
              {lenderStatuses.map(status => (
                <option key={status}>{status}</option>
              ))}
            </select>
          </div>
        </div>

        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="p-4 text-left">Company</th>
              <th className="p-4 text-left">Invoice Number</th>
              <th className="p-4 text-left">Due Date</th>
              <th className="p-4 text-left">Amount</th>
              <th className="p-4 text-left">Madad Score</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {pendingApprovals.map((item) => (
              <tr key={item.id} className="border-t">
                <td className="p-4">{item.company}</td>
                <td className="p-4">{item.invoiceNumber}</td>
                <td className="p-4">{item.dueDate}</td>
                <td className="p-4">{item.amount}</td>
                <td className="p-4">{item.madadScore}</td>
                <td className="p-4">
                  <StatusBadge status={item.status} />
                </td>
                <td className="p-4">
                  <button
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    onClick={() => handleReview(item)}
                  >
                    Review
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Review Modal */}
      {showReviewModal && selectedInvoice && editedInvoiceData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-7xl h-[90vh] flex">
            {/* Left side - PDF Preview */}
            <div className="flex-1 pr-6">
              <h3 className="text-lg font-semibold mb-4">Invoice Preview</h3>
              <iframe
                src={selectedInvoice.signedUrl}
                className="w-full h-[calc(100%-2rem)] border rounded"
                title="Invoice PDF"
              />
            </div>

            {/* Right side - Invoice Details */}
            <div className="w-96 flex flex-col">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold">Invoice Details</h3>
                <button
                  onClick={() => setShowReviewModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="flex-1 space-y-4 overflow-y-auto">
                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-semibold mb-2">Basic Information</h4>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Invoice Number:</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.invoiceNumber}
                      onChange={(e) => handleInvoiceFieldChange('invoiceNumber', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Invoice Date:</label>
                    <input
                      type="date"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.invoiceDate}
                      onChange={(e) => handleInvoiceFieldChange('invoiceDate', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Due Date:</label>
                    <input
                      type="date"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.dueDate}
                      onChange={(e) => handleInvoiceFieldChange('dueDate', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Description:</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.description}
                      onChange={(e) => handleInvoiceFieldChange('description', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Reference:</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.reference}
                      onChange={(e) => handleInvoiceFieldChange('reference', e.target.value)}
                    />
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-semibold mb-2">Amount Details</h4>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Total Amount:</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.amount}
                      onChange={(e) => handleInvoiceFieldChange('amount', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Madad Score:</label>
                    <input
                      type="number"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.madadScore}
                      onChange={(e) => handleInvoiceFieldChange('madadScore', parseInt(e.target.value) || 0)}
                      min="0"
                      max="100"
                    />
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-semibold mb-2">Party Information</h4>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Company:</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.company}
                      onChange={(e) => handleInvoiceFieldChange('company', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Buyer:</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.supplier}
                      onChange={(e) => handleInvoiceFieldChange('supplier', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Customer:</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.customer}
                      onChange={(e) => handleInvoiceFieldChange('customer', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">GSTIN:</label>
                    <input
                      type="text"
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.gstin}
                      onChange={(e) => handleInvoiceFieldChange('gstin', e.target.value)}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Billing Address:</label>
                    <textarea
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.billingAddress}
                      onChange={(e) => handleInvoiceFieldChange('billingAddress', e.target.value)}
                      rows={2}
                    />
                  </div>
                  <div className="mb-2">
                    <label className="block text-gray-600 text-sm mb-1">Customer Address:</label>
                    <textarea
                      className="w-full p-2 border rounded"
                      value={editedInvoiceData.customerAddress}
                      onChange={(e) => handleInvoiceFieldChange('customerAddress', e.target.value)}
                      rows={2}
                    />
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded">
                  <h4 className="font-semibold mb-2">Current Status</h4>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 ${getStatusColorDot(editedInvoiceData.status)} rounded-full`}></div>
                    <StatusBadge status={editedInvoiceData.status} />
                  </div>
                </div>

                <button
                  onClick={handleSaveInvoiceData}
                  className="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 mt-2"
                >
                  Save Changes
                </button>
              </div>

              <div className="mt-6 pt-6 border-t relative">
                <div className="relative w-full">
                  <button
                    onClick={handleToggleStatusDropdown}
                    className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex items-center justify-between"
                  >
                    <span>Update Status</span>
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  {showStatusDropdown && (
                    <div className="absolute z-10 bottom-full mb-1 w-full rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                      <div className="py-1" role="menu" aria-orientation="vertical">
                        {lenderStatuses.map((status) => (
                          <button
                            key={status}
                            onClick={() => handleStatusSelect(status)}
                            className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${editedInvoiceData.status === status ? 'bg-blue-50 font-medium' : ''
                              }`}
                            role="menuitem"
                          >
                            <div className="flex items-center">
                              <div className={`w-2 h-2 ${getStatusColorDot(status)} rounded-full mr-2`}></div>
                              {status}
                              {editedInvoiceData.status === status && (
                                <svg className="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              )}
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
// src/pages/invoices/index.js
import { useState, useEffect } from 'react';
import { mockInvoices } from '@/data/mockData';
import StatusBadge from '../../components/StatusBadge';

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState(mockInvoices);
  const [lastAction, setLastAction] = useState(null);
  const [showNotification, setShowNotification] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedInvoices = localStorage.getItem('invoices');
      if (savedInvoices) {
        setInvoices(JSON.parse(savedInvoices));
      }
    }
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('invoices', JSON.stringify(invoices));
    }
    if (lastAction) {
      setShowNotification(true);
      const timer = setTimeout(() => {
        setShowNotification(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [invoices, lastAction]);

  const handleStatusUpdate = (invoiceId, newStatus) => {
    setInvoices(prevInvoices =>
      prevInvoices.map(invoice =>
        invoice.id === invoiceId
          ? { ...invoice, status: newStatus, updatedAt: new Date().toISOString() }
          : invoice
      )
    );
    setLastAction({
      type: newStatus,
      invoiceId: invoiceId
    });
  };

  // const getStatusStyle = (status) => {
  //   const styles = {
  //     'PENDING_APPROVAL': 'bg-yellow-100 text-yellow-800',
  //     'APPROVED': 'bg-green-100 text-green-800',
  //     'REJECTED': 'bg-red-100 text-red-800',
  //     'IN_REVIEW': 'bg-blue-100 text-blue-800'
  //   };
  //   return `px-2 py-1 rounded-full text-sm ${styles[status] || 'bg-gray-100 text-gray-800'}`;
  // };

  const getRowStyle = (status) => {
    if (status === 'APPROVED') return 'bg-green-50';
    if (status === 'REJECTED') return 'bg-red-50';
    return 'hover:bg-gray-50';
  };

  return (
    <div className="p-6 relative">
      {/* Notification Toast */}
      {showNotification && lastAction && (
        <div className="absolute top-4 right-4 p-4 rounded-lg shadow-lg text-white transition-opacity duration-300 ease-in-out"
          style={{
            backgroundColor: lastAction.type === 'APPROVED' ? '#10B981' : '#EF4444',
            opacity: showNotification ? 1 : 0,
          }}
        >
          <p>Invoice #{lastAction.invoiceId} has been {lastAction.type.toLowerCase()}</p>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Invoices</h1>
        <div className="flex space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-sm">Approved</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span className="text-sm">Rejected</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span className="text-sm">Pending</span>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full bg-white shadow-md rounded-lg">
          <thead className="bg-gray-50">
            <tr>
              <th className="p-4 text-left">Invoice Number</th>
              <th className="p-4 text-left">Date</th>
              <th className="p-4 text-left">Due Date</th>
              <th className="p-4 text-left">Customer</th>
              <th className="p-4 text-left">Amount</th>
              <th className="p-4 text-left">Status</th>
              <th className="p-4 text-left">Last Updated</th>
              <th className="p-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {invoices.map((invoice) => (
              <tr
                key={invoice.id}
                className={`border-t transition-all duration-300 ${getRowStyle(invoice.status)}`}
              >
                <td className="p-4">
                  <button className="text-blue-500 hover:text-blue-700 hover:underline">
                    {invoice.invoiceNumber}
                  </button>
                </td>
                <td className="p-4">{invoice.date}</td>
                <td className="p-4">{invoice.dueDate}</td>
                <td className="p-4">{invoice.customerName}</td>
                <td className="p-4">{invoice.amount}</td>
                <td className="p-4">
                  <StatusBadge status={invoice.status} />
                </td>
                <td className="p-4 text-sm text-gray-600">
                  {invoice.updatedAt ? new Date(invoice.updatedAt).toLocaleString() : '-'}
                </td>
                <td className="p-4">
                  <div className="flex space-x-2">
                    {invoice.status !== 'APPROVED' && invoice.status !== 'REJECTED' && (
                      <>
                        <button
                          onClick={() => handleStatusUpdate(invoice.id, 'APPROVED')}
                          className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 transition-colors"
                          aria-label={`Approve invoice ${invoice.invoiceNumber}`}
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleStatusUpdate(invoice.id, 'REJECTED')}
                          className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 transition-colors"
                          aria-label={`Reject invoice ${invoice.invoiceNumber}`}
                        >
                          Reject
                        </button>
                      </>
                    )}
                    <button
                      className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition-colors"
                      aria-label={`View invoice ${invoice.invoiceNumber}`}
                    >
                      View
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
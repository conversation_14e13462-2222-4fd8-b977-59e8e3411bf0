import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router'; // Assuming Next.js router as per "router.push"
import axios from 'axios';
import config from "../../../config.json"; // Adjust path if necessary

// Icons (ensure these are installed and imported correctly)
import { ArrowLeftIcon, EyeIcon, ArrowDownTrayIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

const STATUS_STYLES = {
    'VERIFIED_ANCHOR': { bg: 'bg-[#4dce83]', text: 'text-white' },
    'VERIFICATION_PENDING_LENDER': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
    'ACCEPTED_LENDER': { bg: 'bg-white border border-[#4dce83]', text: 'text-[#4dce83]' },
    'REJECTED_LENDER': { bg: 'bg-red-500', text: 'text-white' },
    'MORE_INFO_NEEDED_LENDER': { bg: 'bg-orange-100', text: 'text-orange-800' },
    'DISBURSED': { bg: 'bg-[#189e51]', text: 'text-white' },
    'READY_FOR_DISBURSAL': { bg: 'bg-[#46a5ef]', text: 'text-white' },
    'LOAN_IN_PROGRESS': { bg: 'bg-purple-100', text: 'text-purple-800' },
    'DEFAULT': { bg: 'bg-black', text: 'text-white' },
    'LOAN_CANCELLED': { bg: 'bg-gray-400', text: 'text-white' },
    'WRITTEN_OFF_PAID': { bg: 'bg-blue-300', text: 'text-black' },
     // Add other statuses from your main file if needed
    'VERIFICATION_PENDING_ANCHOR': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
    'MORE_INFO_NEEDED_ANCHOR': { bg: 'bg-orange-100', text: 'text-orange-800' },
    'REJECTED_ANCHOR': { bg: 'bg-red-500', text: 'text-white' },

};

const STATUS_DISPLAY_NAMES = {
    'VERIFIED_ANCHOR': 'Verified (Buyer)',
    'VERIFICATION_PENDING_LENDER': 'Pending Verification',
    'ACCEPTED_LENDER': 'Accepted',
    'REJECTED_LENDER': 'Rejected',
    'MORE_INFO_NEEDED_LENDER': 'More Info Needed',
    'READY_FOR_DISBURSAL': 'Ready For Disbursal',
    'DISBURSED': 'Disbursed',
    'LOAN_IN_PROGRESS': 'Loan In Progress',
    'DEFAULT': 'Default',
    'LOAN_CANCELLED': 'Loan Cancelled',
    'WRITTEN_OFF_PAID': 'Written Off (Paid)',
    // Add other statuses
    'VERIFICATION_PENDING_ANCHOR': 'Pending Buyer Verification',
    'MORE_INFO_NEEDED_ANCHOR': 'More Info Needed (Buyer)',
    'REJECTED_ANCHOR': 'Rejected (Buyer)',
};


// --- Loader Component ---
const Loader = () => (
    <div className="flex items-center justify-center">
        <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
    </div>
);


const formatDate = (dateString) => {
    if (!dateString || dateString === 'N/A' || dateString === undefined) return 'N/A';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime()) || date.getFullYear() <= 1970) return 'N/A';
        return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return 'Invalid Date';
    }
};

const formatAmount = (amount) => {
    if (amount === null || amount === undefined) return 'N/A';
    if (typeof amount === 'string' && amount.startsWith('QAR')) { return amount; }
    const numAmount = Number(String(amount).replace(/[^0-9.-]+/g, ""));
    if (isNaN(numAmount)) { return 'N/A'; }
    return `QAR ${numAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

const getFilenameFromPath = (path) => {
    if (!path) return 'document';
    try {
        const url = new URL(path); // Handles full URLs
        const pathnameParts = url.pathname.split('/');
        return decodeURIComponent(pathnameParts[pathnameParts.length - 1] || 'document');
    } catch (e) {
      console.log(e)
        // If not a valid URL, treat as a simple path
        const pathParts = path.split('/');
        return pathParts[pathParts.length - 1] || 'document';
    }
};

const getStatusStyle = (status) => {
    const style = STATUS_STYLES[status] || { bg: 'bg-gray-200', text: 'text-gray-800' }; // Fallback style
    return `${style.bg} ${style.text}`;
};

const getStatusDisplay = (status) => {
    return STATUS_DISPLAY_NAMES[status] || status?.replace(/_/g, ' ') || 'UNKNOWN';
};

// Simplified status display for the header
const HeaderStatusDisplay = ({ status }) => {
    const styleClass = getStatusStyle(status);
    const displayText = getStatusDisplay(status);
    return (
        <span className={`ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${styleClass} capitalize`}>
            {displayText}
        </span>
    );
};


const InvoiceDiscountingDetailPage = () => {
    const router = useRouter();
    const [currentInvoice, setCurrentInvoice] = useState(null);
    const [pdfUrl, setPdfUrl] = useState('');
    const [verificationComments, setVerificationComments] = useState('');

    const [isLoading, setIsLoading] = useState(false);
    const [isFetchingCreditLine, setIsFetchingCreditLine] = useState(false);
    const [creditLineFetchError, setCreditLineFetchError] = useState(null);
    const [apiError, setApiError] = useState(null); // For general API errors on this page

    const [showAcceptModal, setShowAcceptModal] = useState(false);
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    
    const [interestRate, setInterestRate] = useState(''); // Added from InvoiceApprovals
    const [acceptOfferDetails, setAcceptOfferDetails] = useState({
        invoiceDiscountingPercentage: '',
        processingFeeType: 'percentage',
        processingFeeValue: '',
        tenureDays: '',
        emiRepaymentFrequency: 'MONTHLY',
    });

    useEffect(() => {
        // Attempt to parse invoice data from router query
        if (router.isReady && router.query.invoiceData) {
            try {
                const invoice = JSON.parse(router.query.invoiceData);
                setCurrentInvoice(invoice);
                setPdfUrl(invoice.signedUrl || ''); // Assuming signedUrl is for the main invoice PDF
                setVerificationComments(invoice.verificationComments || '');
            } catch (error) {
                console.error("Error parsing invoice data from query:", error);
                setApiError("Failed to load invoice details.");
                // Optionally redirect or show a more prominent error
            }
        } else if (router.isReady && !router.query.invoiceData) {
            // Handle case where data is not passed, maybe fetch by ID if an ID was passed
            setApiError("Invoice data not found. Please go back and try again.");
        }
    }, [router.isReady, router.query]);
    
    // --- Modal and Offer Logic (Copied and adapted from InvoiceApprovals) ---

    const handleDiscountPercentageChange = (value) => {
        if (isNaN(value)) {
            setAcceptOfferDetails({ ...acceptOfferDetails, invoiceDiscountingPercentage: '' });
        } else {
            const newValue = Math.min(100, Math.max(0, value)); // Assuming 0-100 range
            setAcceptOfferDetails({ ...acceptOfferDetails, invoiceDiscountingPercentage: newValue });
        }
    };

    const handleProcessingFeeValueChange = (e) => {
        const value = parseFloat(e.target.value);
        if (isNaN(value)) {
            setAcceptOfferDetails({ ...acceptOfferDetails, processingFeeValue: '' });
        } else {
            let newValue = value;
            if (acceptOfferDetails.processingFeeType === 'flat') {
                newValue = Math.min(10000, Math.max(0, value)); // Example limits
            } else { // percentage
                newValue = Math.min(10, Math.max(0, value)); // Example limits
            }
            setAcceptOfferDetails({ ...acceptOfferDetails, processingFeeValue: newValue });
        }
    };
    
    const handleTenureDaysChange = (e) => {
        const value = parseInt(e.target.value, 10);
        if (isNaN(value)) {
            setAcceptOfferDetails({ ...acceptOfferDetails, tenureDays: '' });
        } else {
            const newValue = Math.max(1, value); // Tenure must be at least 1 day
            setAcceptOfferDetails({ ...acceptOfferDetails, tenureDays: newValue });
        }
    };

    const handleEmiRepaymentFrequencyChange = (e) => {
        setAcceptOfferDetails({ ...acceptOfferDetails, emiRepaymentFrequency: e.target.value });
    };

    const calculateDiscountedAmount = () => {
        if (!currentInvoice || !currentInvoice.totalAmount || acceptOfferDetails.invoiceDiscountingPercentage === '' || acceptOfferDetails.invoiceDiscountingPercentage === null) return 'N/A';
        try {
            const totalAmount = parseFloat(String(currentInvoice.totalAmount).replace(/[^0-9.-]+/g, ""));
            const discountPercentage = parseFloat(acceptOfferDetails.invoiceDiscountingPercentage);
            if (isNaN(totalAmount) || isNaN(discountPercentage)) return 'N/A';
            const discountedAmount = totalAmount * (discountPercentage / 100);
            return formatAmount(discountedAmount);
        } catch (e) { console.error("Error calculating amount:", e); return "Error"; }
    };

    const isFormValid = () => {
        const discountValid = acceptOfferDetails.invoiceDiscountingPercentage !== '' && !isNaN(parseFloat(acceptOfferDetails.invoiceDiscountingPercentage)) && parseFloat(acceptOfferDetails.invoiceDiscountingPercentage) > 0 && parseFloat(acceptOfferDetails.invoiceDiscountingPercentage) <= 100;
        const feeValid = acceptOfferDetails.processingFeeValue !== '' && !isNaN(parseFloat(acceptOfferDetails.processingFeeValue)) && parseFloat(acceptOfferDetails.processingFeeValue) >= 0;
        const tenureValid = acceptOfferDetails.tenureDays !== '' && !isNaN(parseInt(acceptOfferDetails.tenureDays, 10)) && parseInt(acceptOfferDetails.tenureDays, 10) >= 1;
        return discountValid && feeValid && tenureValid;
    };

    const handleOpenAcceptModal = useCallback(async () => {
        if (!currentInvoice || !currentInvoice.userId) {
            alert("Cannot proceed: Missing critical invoice information.");
            return;
        }

        setShowAcceptModal(true);
        setIsFetchingCreditLine(true);
        setCreditLineFetchError(null);
        // Reset form, pre-fill fixed values
        setAcceptOfferDetails({
            invoiceDiscountingPercentage: 80, // Fixed at 80% as per original logic
            processingFeeType: 'percentage',
            processingFeeValue: '',
            tenureDays: '',
            emiRepaymentFrequency: 'MONTHLY', // Fixed
        });

        try {
            const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`);
            let userCreditLine = null;
            if (response.data && Array.isArray(response.data)) {
                userCreditLine = response.data.find(cl => cl.userId === currentInvoice.userId && cl.creditLineStatus === 'ACTIVE')
                    || response.data.find(cl => cl.userId === currentInvoice.userId);
            }

            if (userCreditLine) {
                setInterestRate(userCreditLine.interestRate); // Store interest rate
                setAcceptOfferDetails(prev => ({
                    ...prev, // Keep fixed 80% and MONTHLY
                    tenureDays: userCreditLine.tenure !== undefined ? userCreditLine.tenure : '',
                    processingFeeType: userCreditLine.processingFeeType || 'percentage',
                    processingFeeValue: userCreditLine.processingFee !== undefined ? userCreditLine.processingFee : '',
                }));
            } else {
                // No specific credit line, keep defaults (80%, MONTHLY, empty fee/tenure)
                 setCreditLineFetchError("No active credit line found. Using default values where applicable. Please verify all fields.");
            }
        } catch (error) {
            const errorMsg = error.response?.data?.message || error.message || 'Failed to fetch credit line details.';
            setCreditLineFetchError(errorMsg);
            // Keep modal open, show error, form has defaults
        } finally {
            setIsFetchingCreditLine(false);
        }
    }, [currentInvoice]);

    const handleAcceptInvoice = async () => {
        if (!currentInvoice || !currentInvoice._id || !currentInvoice.userId || !currentInvoice.totalAmount) {
            alert("Cannot proceed: Missing critical invoice details for submission.");
            return;
        }
        setIsLoading(true);
        setApiError(null);

        try {
            const lenderId = localStorage.getItem('userId');
            if (!lenderId) throw new Error("Lender session expired or not found.");

            await axios.post(`${config.apiUrl}/ops/invoiceFinancing/createOffer`, {
                invoiceId: currentInvoice._id,
                lenderId: lenderId,
                invoiceDiscountingPercentage: acceptOfferDetails.invoiceDiscountingPercentage,
                processingFee: {
                    type: acceptOfferDetails.processingFeeType,
                    value: acceptOfferDetails.processingFeeValue || 0,
                },
                tenureDays: acceptOfferDetails.tenureDays,
                emiRepaymentFrequency: acceptOfferDetails.emiRepaymentFrequency,
                creditLimit: currentInvoice.totalAmount, // Or appropriate limit
                merchantId: currentInvoice.userId,
                offerType: "invoiceDiscountingOffer",
                 // Add verificationComments if your backend accepts them here or in the updateInvoice call
                // verificationComments: verificationComments, 
            });

            await axios.put(`${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${currentInvoice._id}`, {
                status: 'ACCEPTED_LENDER',
                 verificationComments: verificationComments, // Send comments during status update
                 verifiedBy: lenderId // Optional: if backend tracks who verified/accepted
            });

            alert('Invoice accepted and offer created successfully!');
            setShowAcceptModal(false);
            setShowConfirmationModal(false);
            router.push('/invoiceDiscounting'); // Navigate back
        } catch (error) {
            const errorMsg = error.response?.data?.message || error.message || 'Failed to accept invoice / create offer.';
            setApiError(errorMsg); // Show error on the page or in modal
            alert(errorMsg); // Also alert
        } finally {
            setIsLoading(false);
        }
    };


    if (!currentInvoice && !apiError) {
        return <div className="p-6 min-h-screen flex justify-center items-center"><Loader /> <p className="ml-4">Loading invoice details...</p></div>;
    }
    if (apiError && !currentInvoice) {
         return (
            <div className="p-6 min-h-screen flex flex-col justify-center items-center">
                <p className="text-red-500 text-xl mb-4">{apiError}</p>
                <button
                    onClick={() => router.push('/invoiceDiscounting')}
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
                >
                    <ArrowLeftIcon className="w-5 h-5 mr-2" />
                    Back to Invoices
                </button>
            </div>
        );
    }
    if (!currentInvoice) { // Should be caught by apiError already but as a safeguard
        return <div className="p-6">Error: Invoice data could not be loaded.</div>;
    }


    return (
        <div className="min-h-screen bg-gray-100">
            {/* Top Bar */}
            <div className="bg-white shadow-sm py-3 px-6">
                <button
                    onClick={() => router.push('/invoiceDiscounting')}
                    className="text-gray-700 hover:text-gray-900 flex items-center text-sm font-medium"
                >
                    <ArrowLeftIcon className="w-5 h-5 mr-2" />
                    Back to Invoices
                </button>
            </div>

            {/* Main Content Area */}
            <div className="p-4 md:p-6">
                {/* Header Section */}
                <div className="bg-white border border-gray-300 rounded-t-lg p-4 flex items-center">
                    <h1 className="text-xl md:text-2xl font-bold text-gray-800">Invoice Detail</h1>
                    {currentInvoice && <HeaderStatusDisplay status={currentInvoice.status} />}
                </div>

                {/* Body Section */}
                <div className="bg-gray-200 border border-t-0 border-gray-300 rounded-b-lg p-4 md:p-6">
                    <div className="flex flex-col lg:flex-row gap-4 md:gap-6">
                        {/* Left Column: PDF Viewer */}
                        <div className="w-full lg:w-1/2 xl:w-3/5 bg-white border border-gray-300 rounded-md shadow-sm overflow-hidden" style={{ minHeight: 'calc(100vh - 250px)' }}>
                            {pdfUrl ? (
                                <iframe
                                    src={pdfUrl}
                                    className="w-full h-full border-0"
                                    title="Invoice PDF Preview"
                                    style={{ minHeight: '600px' }} // Ensure iframe has substantial height
                                />
                            ) : (
                                <div className="w-full h-full flex items-center justify-center text-gray-500 p-10" style={{ minHeight: '600px' }}>
                                    Invoice PDF preview is not available.
                                </div>
                            )}
                        </div>

                        {/* Right Column: Details */}
                        <div className="w-full lg:w-1/2 xl:w-2/5 space-y-4 md:space-y-6">
                            {/* Section 1: Invoice Info */}
                            <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300">
                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                    <div>
                                        <p className="text-xs text-gray-500">Invoice Number</p>
                                        <p className="text-sm font-medium text-gray-800">{currentInvoice.invoiceNumber || 'N/A'}</p>
                                    </div>
                                    <div>
                                        <p className="text-xs text-gray-500">Invoice Date</p>
                                        <p className="text-sm font-medium text-gray-800">{formatDate(currentInvoice.invoiceDate)}</p>
                                    </div>
                                    <div>
                                        <p className="text-xs text-gray-500">Due Date</p>
                                        <p className="text-sm font-medium text-gray-800">{formatDate(currentInvoice.dueDate)}</p>
                                    </div>
                                </div>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <p className="text-xs text-gray-500">Trade Name / Legal Entity Name</p>
                                        <p className="text-sm font-medium text-gray-800 break-words">{currentInvoice.supplierName || 'N/A'}</p>
                                    </div>
                                    <div>
                                        <p className="text-xs text-gray-500">Buyer Name</p>
                                        <p className="text-sm font-medium text-gray-800 break-words">{currentInvoice.customerName || 'N/A'}</p>
                                    </div>
                                </div>

                                {/* Supporting Documents */}
                                {(currentInvoice.additionalInvoiceDocuments && currentInvoice.additionalInvoiceDocuments.length > 0) && (
                                    <div className="bg-[#eef6f8] p-3 rounded-md mt-4">
                                        <h3 className="text-sm font-semibold text-gray-700 mb-2">Supporting Documents</h3>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                                            {currentInvoice.additionalInvoiceDocuments.map((doc, index) => (
                                                <div key={doc._id || index} className="bg-white p-2.5 border border-gray-200 rounded shadow-sm">
                                                    <p className="text-xs font-semibold text-gray-600 mb-1">Document {index + 1}</p>
                                                    <p className="text-xs text-gray-700 truncate mb-1.5" title={getFilenameFromPath(doc.filePath)}>
                                                        {getFilenameFromPath(doc.filePath)}
                                                    </p>
                                                    <div className="flex justify-between items-center text-xs">
                                                        <a
                                                            href={doc.signedUrl}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            // The color #c3ffe4 is very light and might have contrast issues on white.
                                                            // Consider a darker, more accessible color if this is problematic.
                                                            className="flex items-center text-[#00825c] hover:text-[#005c40] font-medium" // Using a darker shade for better visibility
                                                            title={`View Document ${index+1}`}
                                                        >
                                                            <EyeIcon className="w-3.5 h-3.5 mr-1" /> View
                                                        </a>
                                                        <a
                                                            href={doc.signedUrl}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            download={getFilenameFromPath(doc.filePath)}
                                                            className="flex items-center text-[#00825c] hover:text-[#005c40] font-medium" // Using a darker shade for better visibility
                                                            title={`Download Document ${index+1}`}
                                                        >
                                                            <ArrowDownTrayIcon className="w-3.5 h-3.5 mr-1" /> Download
                                                        </a>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {/* Total Amount Due */}
                                <div className="bg-[#eef6f8] p-4 rounded-md mt-4">
                                    <p className="text-sm font-medium text-gray-700">Total Amount Due</p>
                                    <p className="text-3xl font-bold">{formatAmount(currentInvoice.totalAmount)}</p>
                                </div>
                            </div>

                            {/* Section 2: Add/Edit Comments */}
                            <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300">
                                <label htmlFor="verificationComments" className="block text-sm font-semibold text-gray-700 mb-1">Add/Edit Comments</label>
                                <textarea
                                    id="verificationComments"
                                    value={verificationComments}
                                    onChange={(e) => setVerificationComments(e.target.value)}
                                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition text-sm"
                                    rows={3}
                                    placeholder="Enter comments for verification..."
                                    disabled={['ACCEPTED_LENDER', 'DISBURSED', 'LOAN_IN_PROGRESS'].includes(currentInvoice.status)}
                                />
                                 {apiError && <p className="text-red-500 text-xs mt-1">{apiError}</p>}
                            </div>

                            {/* Section 3: Accept Invoice Button */}
                            <div className="bg-white p-4 rounded-md shadow-sm border border-gray-300">
                                <button
                                    onClick={handleOpenAcceptModal}
                                    className={`w-full px-4 py-3 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition font-medium text-base flex items-center justify-center shadow-sm hover:shadow-md ${
                                        ['VERIFICATION_PENDING_LENDER', 'VERIFIED_ANCHOR', 'MORE_INFO_NEEDED_LENDER'].includes(currentInvoice.status)
                                        ? 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
                                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                    }`}
                                    disabled={!['VERIFICATION_PENDING_LENDER', 'VERIFIED_ANCHOR', 'MORE_INFO_NEEDED_LENDER'].includes(currentInvoice.status)}
                                    title={!['VERIFICATION_PENDING_LENDER', 'VERIFIED_ANCHOR', 'MORE_INFO_NEEDED_LENDER'].includes(currentInvoice.status) ? `Cannot accept invoice with status: ${getStatusDisplay(currentInvoice.status)}` : 'Accept Invoice for Discounting'}
                                >
                                    <CheckCircleIcon className="w-5 h-5 mr-2" />
                                    Accept Invoice for Discounting
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Accept Offer Modal */}
            {showAcceptModal && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4 overflow-y-auto">
                    <div className="bg-white p-5 md:p-6 rounded-lg shadow-xl w-full max-w-lg">
                        <div className="flex justify-between items-center mb-4 pb-3 border-b">
                            <h3 className="text-lg font-semibold text-gray-800">Accept Invoice for Discounting</h3>
                            <button onClick={() => setShowAcceptModal(false)} className="text-gray-400 hover:text-gray-600">
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                            </button>
                        </div>
                        
                        {isFetchingCreditLine ? (
                            <div className="py-10"><Loader /><p className="text-center text-gray-500 mt-2">Fetching credit line details...</p></div>
                        ) : (
                            <div className="space-y-4">
                                {creditLineFetchError && <p className="text-sm text-orange-600 bg-orange-50 p-3 rounded-md border border-orange-200">{creditLineFetchError}</p>}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Lender Payout Percentage (%)</label>
                                    <input type="number" value={acceptOfferDetails.invoiceDiscountingPercentage} onChange={(e) => handleDiscountPercentageChange(parseFloat(e.target.value))} className="mt-1 w-full p-2 border border-gray-300 rounded-md" required min="0" max="100" placeholder="e.g., 80"/>
                                    {acceptOfferDetails.invoiceDiscountingPercentage === '' && (<p className="mt-1 text-xs text-red-500">Required</p>)}
                                    {currentInvoice && (<p className="mt-1 text-xs text-gray-500">Lender Payout Amount: {calculateDiscountedAmount()}</p>)}
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Processing Fee Type</label>
                                    <select value={acceptOfferDetails.processingFeeType} onChange={(e) => setAcceptOfferDetails({ ...acceptOfferDetails, processingFeeType: e.target.value, processingFeeValue: '' })} className="mt-1 w-full p-2 border border-gray-300 rounded-md" required>
                                        <option value="percentage">Percentage (%)</option>
                                        <option value="flat">Flat (QAR)</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Processing Fee Value</label>
                                    <input type="number" value={acceptOfferDetails.processingFeeValue} onChange={handleProcessingFeeValueChange} className="mt-1 w-full p-2 border border-gray-300 rounded-md" min="0" step={acceptOfferDetails.processingFeeType === 'percentage' ? '0.01' : '1'} />
                                    <p className="text-xs text-gray-500 mt-1">{acceptOfferDetails.processingFeeType === 'percentage' ? 'Max 10%' : 'Max QAR 10,000'}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Tenure (Days)</label>
                                    <input type="number" value={acceptOfferDetails.tenureDays} onChange={handleTenureDaysChange} className="mt-1 w-full p-2 border border-gray-300 rounded-md" min="1" placeholder="Days until repayment" />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Repayment Frequency</label>
                                    <select value={acceptOfferDetails.emiRepaymentFrequency} onChange={handleEmiRepaymentFrequencyChange} className="mt-1 w-full p-2 border border-gray-300 rounded-md">
                                        <option value="MONTHLY">Monthly</option>
                                        <option value="WEEKLY">Weekly</option>
                                        <option value="DAILY">Daily</option>
                                    </select>
                                </div>
                                <div className="flex justify-end space-x-3 pt-4 border-t mt-3">
                                    <button type="button" onClick={() => setShowAcceptModal(false)} className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                                    <button type="button" onClick={() => { if (isFormValid()) { setShowConfirmationModal(true); setShowAcceptModal(false); } else { alert("Please fill all required fields correctly."); } }} className="px-4 py-2 text-sm font-medium bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-300" disabled={isLoading || !isFormValid()}> {isLoading ? 'Processing...' : 'Next'} </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Confirmation Modal */}
            {showConfirmationModal && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4 overflow-y-auto">
                    <div className="bg-white p-5 md:p-6 rounded-lg shadow-xl w-full max-w-lg">
                         <div className="flex justify-between items-center mb-4 pb-3 border-b">
                            <h3 className="text-lg font-semibold text-gray-800">Confirm Invoice Discounting Offer</h3>
                             <button onClick={() => {setShowConfirmationModal(false); setShowAcceptModal(true);}} className="text-gray-400 hover:text-gray-600" disabled={isLoading}>
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                            </button>
                        </div>
                        <p className="mb-3 text-sm text-gray-600">Review the details before confirming:</p>
                        <div className="space-y-1.5 bg-gray-50 p-3 rounded border text-sm mb-5">
                            <p><strong>Lender Payout (%):</strong> {acceptOfferDetails.invoiceDiscountingPercentage}%</p>
                            <p><strong>Lender Payout Amount:</strong> {calculateDiscountedAmount()}</p>
                            <p><strong>Service Fee (%):</strong> {interestRate || 'Inherited from credit line'}</p>
                            <hr className="my-1.5" />
                            <p><strong>Processing Fee Type:</strong> {acceptOfferDetails.processingFeeType}</p>
                            <p><strong>Processing Fee Value:</strong> {acceptOfferDetails.processingFeeValue}{acceptOfferDetails.processingFeeType === 'percentage' ? '%' : ' QAR'}</p>
                            <hr className="my-1.5" />
                            <p><strong>Tenure:</strong> {acceptOfferDetails.tenureDays} Days</p>
                            <p><strong>Repayment Frequency:</strong> {acceptOfferDetails.emiRepaymentFrequency}</p>
                            {verificationComments && <><hr className="my-1.5" /><p><strong>Comments:</strong> {verificationComments}</p></>}
                        </div>
                        <div className="flex justify-end space-x-3 pt-4 border-t">
                            <button onClick={() => {setShowConfirmationModal(false); setShowAcceptModal(true);}} className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200" disabled={isLoading}>Back</button>
                            <button onClick={handleAcceptInvoice} className="px-4 py-2 text-sm font-medium bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50" disabled={isLoading}>{isLoading ? 'Confirming...' : 'Confirm & Create Offer'}</button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default InvoiceDiscountingDetailPage;
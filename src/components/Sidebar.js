import React, {useEffect, useState} from 'react'; // Removed useState as it will be passed down
import Link from 'next/link';
import Image from 'next/image';
import {
    DocumentTextIcon,
    HomeIcon,
    CheckCircleIcon,
    ChatBubbleLeftIcon, // For Help & Support
    XMarkIcon // Close icon for mobile menu
} from '@heroicons/react/24/outline';
import config from "../../config.json";
import axios from 'axios';

const Sidebar = ({ activePage, isMobileMenuOpen, setIsMobileMenuOpen }) => { // Accept props
    // State for both counts
    const [creditApplicationCount, setCreditApplicationCount] = useState(0);
    const [pendingActivationsCount, setPendingActivationsCount] = useState(0); 

    const menuItems = [
        {
            name: 'Dashboard',
            icon: HomeIcon,
            path: '/dashboard',
            badge: null,
        },
        {
            name: 'Credit Applications',
            icon: DocumentTextIcon,
            path: '/creditAssessment',
            badge: creditApplicationCount,
        },
        {
            name: 'Credit Line Activations',
            icon: CheckCircleIcon,
            path: '/creditLineActivation',
            badge: pendingActivationsCount,
        },
        {
            name: 'Invoice Discounting',
            icon: DocumentTextIcon,
            path: '/invoiceDiscounting',
            badge: null,
        },
        {
            name: 'Payments',
            icon: DocumentTextIcon,
            path: '/payments',
            badge: null,
        },
    ];
    
    // Helper function to safely access nested properties
const getNested = (obj, path, defaultValue = undefined) => {
    try {
        if (!path || typeof path !== 'string' || !obj) {
            return defaultValue;
        }
        const value = path.split('.').reduce((o, k) => (o && o[k] !== undefined && o[k] !== null) ? o[k] : undefined, obj);
        return (value === undefined) ? defaultValue : value;
    } catch (e) {
        console.error("Error in getNested:", e, "Path:", path);
        return defaultValue;
    }
};

    // This single useEffect fetches data once and calculates all counts
    useEffect(() => {
        const fetchSidebarCounts = async () => {
            try {
                const loggedInLenderId = localStorage.getItem('userId');
                if (!loggedInLenderId) {
                    throw new Error("Lender ID not found in localStorage.");
                }

                // 1. Fetch all necessary data in parallel
                const [clResult, userResult, offerResult] = await Promise.allSettled([
                    fetch(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`),
                    fetch(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`),
                    axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers?offerType=creditLineOffer`)
                ]);

                // 2. Process all responses
                const allFetchedCreditLines = (clResult.status === 'fulfilled' && clResult.value.ok) ? await clResult.value.json() || [] : [];

                const userDetailsMap = (userResult.status === 'fulfilled' && userResult.value.ok)
                    ? new Map((await userResult.value.json()).kycs.map(user => [user._id, user]))
                    : new Map();
                
                const allFetchedOffers = (offerResult.status === 'fulfilled' && offerResult.value.data?.success) ? offerResult.value.data.offers || [] : [];
                
                let applicationsCount = 0;
                let pendingActivations = 0;
                const loggedInLenderStr = String(loggedInLenderId);

                // 3. Loop ONCE to calculate all counts
                allFetchedCreditLines.forEach(cl => {
                    const userDetails = userDetailsMap.get(cl.userId);
                    if (!userDetails) return; // Skip if no matching user

                    const clStatus = String(cl.creditLineStatus || '').toUpperCase();
                    
                    // Logic for Pending Activations Count
                    const isCorrectLenderForActivation = String(getNested(cl, 'lenderId')) === loggedInLenderStr;
                    if (isCorrectLenderForActivation && cl.offerAccepted === true) {
                        if (clStatus === 'APPROVED' || clStatus === 'SUSPENDED') {
                            pendingActivations++;
                        }
                    }

                    // Logic for Credit Applications Count
                    const kycStatus = String(getNested(userDetails, 'kyc.verificationStatus', 'N/A')).toUpperCase();
                    const userOffers = allFetchedOffers.filter(offer => String(offer.merchantId) === String(userDetails._id));
                    const thisLenderOffer = userOffers.find(offer => String(offer.lenderId) === loggedInLenderStr);
                    const hasAcceptedOffer = userOffers.some(offer =>
                        ['ACCEPTED', 'LOAN_CONTRACT_ACCEPTED', 'INITIATED_FUND_TRANSFER', 'READY_FOR_DISBURSAL', 'LOAN_IN_PROGRESS', 'PAID', 'OVERDUE', 'DEFAULTED']
                        .includes(String(getNested(offer, 'status', '')).toUpperCase())
                    );

                    const lastReviewByThisLender = Array.isArray(cl.reviewHistory)
                        ? [...cl.reviewHistory]
                              .filter(h => String(h.reviewedBy) === loggedInLenderStr)
                              .sort((a, b) => new Date(b.reviewDate || 0) - new Date(a.reviewDate || 0))[0]
                        : null;
                    
                    const isSuspendedByThisLender = lastReviewByThisLender?.status === 'SUSPENDED';
                    const isRejectedByThisLender = lastReviewByThisLender?.status === 'REJECTED';

                    const isEligibleForApplicationTab =
                        clStatus === 'UNDER_REVIEW' &&
                        kycStatus === 'APPROVED' &&
                        !hasAcceptedOffer &&
                        !thisLenderOffer &&
                        !isSuspendedByThisLender &&
                        !isRejectedByThisLender;

                    if (isEligibleForApplicationTab) {
                        applicationsCount++;
                    }
                });

                // 4. Set both state variables
                setCreditApplicationCount(applicationsCount);
                setPendingActivationsCount(pendingActivations);

            } catch (error) {
                console.error("Error fetching sidebar counts:", error);
                setCreditApplicationCount(0); // Reset on error
                setPendingActivationsCount(0); // Reset on error
            }
        };

        fetchSidebarCounts();
    }, []);

    return (
        <>
            {/* Sidebar content */}
            <div
                className={`fixed inset-y-0 left-0 transform ${
                    isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
                } md:relative md:translate-x-0 transition-transform duration-200 ease-in-out
                w-64 bg-[#eef6f8] text-gray-800 flex flex-col pt-4 pb-4 overflow-y-auto md:shadow-none shadow-xl z-20`}
            >
                {/* Close button for mobile menu */}
                <div className="md:hidden flex justify-end pr-4 pt-2">
                    <button
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="text-gray-800 focus:outline-none"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                {/* Madad Logo for Sidebar */}
                <div className="flex justify-left mb-6 px-4">
                    <Image
                        src={'/logo.jpg'} // Assuming you have a madad_logo.png
                        alt="Madad Logo"
                        width={80} // Adjust size as needed
                        height={40} // Adjust size as needed
                        className="object-contain"
                    />
                </div>
                <nav className="flex-1 px-4 space-y-2">
                    {menuItems.map((item) => {
                        const isActive = activePage === item.path;
                        const IconComponent = item.icon;

                        return (
                            <Link
                                key={item.name}
                                href={item.path}
                                className={`flex items-center px-4 py-3 text-sm font-medium rounded-md group
                                    ${isActive
                                        ? 'bg-[#014e20] text-white font-semibold' // Active tab background: #014e20, text white
                                        : 'text-gray-700 hover:bg-gray-200 hover:text-gray-900' // Inactive: default text, light hover
                                    }`}
                                onClick={() => setIsMobileMenuOpen(false)} // Close menu on item click
                            >
                                <IconComponent
                                    className={`mr-3 h-5 w-5 ${isActive ? 'text-white' : 'text-gray-600 group-hover:text-gray-700'}`} // Icon color changes based on active
                                    aria-hidden="true"
                                />
                                {item.name}
                                {item.badge !== null && (
                                    <span className="ml-auto bg-white text-gray-800 py-0.5 px-2 rounded-full text-xs">
                                        {item.badge}
                                    </span>
                                )}
                            </Link>
                        );
                    })}
                </nav>

                {/* Help & Support */}
                <div className="mt-auto px-4 pb-4 border-t border-gray-200 pt-4">
                    <Link
                        href="/help-support" // Adjust path as needed
                        className="flex items-center px-4 py-3 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-200 hover:text-gray-900"
                        onClick={() => setIsMobileMenuOpen(false)}
                    >
                        <ChatBubbleLeftIcon className="mr-3 h-5 w-5 text-gray-600" aria-hidden="true" />
                        Help & Support
                    </Link>
                </div>
            </div>

            {/* Overlay for mobile menu */}
            {isMobileMenuOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden"
                    onClick={() => setIsMobileMenuOpen(false)}
                ></div>
            )}
        </>
    );
};

export default Sidebar;
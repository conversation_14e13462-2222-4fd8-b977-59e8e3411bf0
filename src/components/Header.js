import React, { useState, useEffect } from 'react';
import { UserCircleIcon, BellIcon, Bars3Icon } from '@heroicons/react/24/outline'; // Import Bars3Icon
import Image from 'next/image';
import Cookies from 'js-cookie';
import config from "../../config.json";

const Header = ({ setIsMobileMenuOpen }) => { // Accept setIsMobileMenuOpen as prop
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [lenderDetails, setLenderDetails] = useState(null);

    const handleLogout = () => {
        localStorage.removeItem('userId');
        Cookies.remove('token', { path: '/' });
        setDropdownOpen(false);
        window.location.href = '/';
    };

    useEffect(() => {
        const fetchLenderDetails = async () => {
            try {
                const lenderId = localStorage.getItem('userId');
                if (!lenderId) return;

                const response = await fetch(`${config.apiUrl}/ops/invoiceFinancing/lenders/${lenderId}`);
                if (!response.ok) throw new Error('Failed to fetch lender details');

                const data = await response.json();
                setLenderDetails(data);
            } catch (error) {
                console.error('Error fetching lender details:', error);
            }
        };

        fetchLenderDetails();
    }, []);

    return (
        <header className="bg-white shadow p-4 md:p-6 flex items-center justify-between relative z-10">
            {/* Hamburger menu for mobile - Aligned to the very left */}
            <div className="flex items-center">
                <button
                    onClick={() => setIsMobileMenuOpen(true)} // Open sidebar on click
                    className="md:hidden text-gray-800 focus:outline-none mr-3" // Only visible on mobile, slightly less margin
                >
                    <Bars3Icon className="h-6 w-6" />
                </button>
                {/* Welcome Text - Aligned next to hamburger or to the left on desktop */}
                {lenderDetails?.lenderName && (
                    <span className="text-gray-800 font-semibold text-base sm:text-lg"> {/* Adjusted text size */}
                        Welcome, {lenderDetails.lenderName}
                    </span>
                )}
            </div>

            {/* Icons and User Dropdown - Aligned to the right */}
            <div className="flex items-center space-x-3 sm:space-x-4"> {/* Adjusted space-x for mobile */}
                {/* Notification Bell Icon */}
                <BellIcon className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600 cursor-pointer" /> {/* Adjusted icon size */}

                {/* User Dropdown */}
                <div className="relative">
                    <div
                        className="flex items-center space-x-2 sm:space-x-3 cursor-pointer" // Adjusted space-x for mobile
                        onClick={() => setDropdownOpen(!dropdownOpen)}
                    >
                        {lenderDetails?.logoUrl ? (
                            <Image
                                unoptimized
                                src={lenderDetails.logoUrl}
                                alt="Lender Logo"
                                width={60} // Smaller default width for mobile
                                height={24} // Smaller default height for mobile
                                className="rounded-full w-auto h-6 sm:h-8" // Auto width, set height for responsiveness
                            />
                        ) : (
                            <UserCircleIcon className="h-7 w-7 sm:h-8 sm:w-8 text-gray-600" /> 
                        )}
                    </div>

                    {dropdownOpen && (
                        <div className="absolute right-0 mt-2 w-36 sm:w-48 bg-white shadow-lg rounded-lg z-50 text-sm"> {/* Adjusted width and text size */}
                            <ul className="py-1">
                                <li className="px-3 py-2 sm:px-4 sm:py-2 hover:bg-gray-100 cursor-pointer">Profile</li>
                                <li
                                    className="px-3 py-2 sm:px-4 sm:py-2 hover:bg-gray-100 cursor-pointer"
                                    onClick={handleLogout}
                                >
                                    Logout
                                </li>
                            </ul>
                        </div>
                    )}
                </div>
            </div>
        </header>
    );
};

export default Header;
import Lottie from "lottie-react";
import loadingAnimation from "../assets/loading.json"

const LoadingModal = ({ title = "Please Wait!", message = "Please wait a moment." }) => {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[9999]">
            <div className="bg-white p-10 rounded-lg shadow-xl text-center w-full max-w-md mx-4">
                <Lottie
                    animationData={loadingAnimation}
                    loop
                    className="w-36 h-36 md:w-42 md:h-42 mx-auto"
                />
                <p className="text-xl font-semibold mt-4 mb-2 text-gray-800">{title}</p> {/* Added mt-4 */}
                <p className="text-sm text-gray-600">
                    {message}
                </p>
            </div>
        </div>
    );
};

export default LoadingModal
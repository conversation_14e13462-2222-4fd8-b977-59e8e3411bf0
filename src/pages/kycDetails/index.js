// KycDetailsPage.js

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import J<PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import { PDFDocument, StandardFonts, rgb, degrees } from 'pdf-lib';
import axios from 'axios'; // Added for API calls
import config from '../../../config.json'; // Assuming path is correct relative to this file or globally configured

import {
    ChevronLeftIcon,
    ArrowDownOnSquareIcon,
    CreditCardIcon, // Added for Create Offer button
    ChevronDownIcon,
    DocumentTextIcon,
} from '@heroicons/react/24/outline';

// --- Constants for Processing Fee ---
const MAX_PROCESSING_FEE_FLAT = 500; // From CreditAssessmentPage
const MAX_PROCESSING_FEE_PERCENTAGE = 5; // 5% (From CreditAssessmentPage)


// --- Helper Functions (getNested, formatDate) ---
const getNested = (obj, path, defaultValue = undefined) => {
    const properties = Array.isArray(path) ? path : path.split('.');
    return properties.reduce((acc, key) => (acc && typeof acc === 'object' && acc[key] !== undefined && acc[key] !== null ? acc[key] : defaultValue), obj);
};

const formatDate = (dateString, includeTime = true) => {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'Invalid Date';
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            ...(includeTime && { hour: '2-digit', minute: '2-digit' }),
        };
        return date.toLocaleDateString('en-GB', options);
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return 'Invalid Date';
    }
};

// --- StatusBadge Component ---
const StatusBadge = ({ status }) => {
    if (!status || status === 'N/A') {
        return <span className="px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-700">N/A</span>;
    }
    status = String(status).toUpperCase();
    let colorClasses = 'bg-gray-100 text-gray-700'; // Default
    if (['APPROVED', 'VERIFIED', 'ACTIVE', 'CLEAR'].includes(status)) {
        colorClasses = 'bg-green-100 text-green-700';
    } else if (['REJECTED', 'EXPIRED', 'SUSPENDED', 'DEFAULTED'].includes(status)) {
        colorClasses = 'bg-red-100 text-red-700';
    } else if (['PENDING', 'UNDER_REVIEW', 'INFO_NEEDED', 'SUBMITTED', 'INITIATED', 'REVIEW', 'ON_HOLD'].includes(status)) {
        colorClasses = 'bg-yellow-100 text-yellow-700';
    } else if (['DRAFT', 'PROCESSING', 'LOAN_CONTRACT_ACCEPTED', 'INITIATED_FUND_TRANSFER', 'READY_FOR_DISBURSAL', 'LOAN_IN_PROGRESS', 'PAID', 'OVERDUE'].includes(status)) {
        colorClasses = 'bg-blue-100 text-blue-700';
    }


    return (
        <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${colorClasses}`}>
            {status.replace(/_/g, ' ')}
        </span>
    );
};

// --- RenderInfoItemViewOnly Component ---
const RenderInfoItemViewOnly = ({ label, value, isFullWidth = false }) => {
    const displayValue = (val) => {
        if (val === null || val === undefined || val === '') {
            return <span className="text-gray-500 italic">N/A</span>;
        }
        if (typeof val === 'boolean') {
            return val ? 'Yes' : 'No'; // Or use StatusBadge here too if needed for booleans
        }
        // Add this check:
        if (React.isValidElement(val)) {
            return val; // Render React elements directly
        }
        return String(val);
    };

    return (
        <div className={`py-1.5 ${isFullWidth ? 'sm:col-span-2 md:col-span-3' : ''} flex flex-row items-baseline`}>
            <dt className="text-sm font-medium text-gray-500 whitespace-nowrap">{label}:&nbsp;</dt>
            <dd className="text-sm text-gray-900 break-words">{displayValue(value)}</dd>
        </div>
    );
};

// --- RenderDocumentDisplayViewOnly Component ---
const RenderDocumentDisplayViewOnly = ({ documentData, label }) => {
    if (!documentData || !documentData.verificationStatus) {
        return (
            <div className="bg-gray-50 p-3 rounded-md border border-gray-200 h-full flex flex-col justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">{label}</p>
                    <p className="text-xs text-gray-500 italic">Not Submitted</p>
                </div>
            </div>
        );
    }

    const status = String(documentData.verificationStatus || 'N/A').toUpperCase();

    return (
        <div className="bg-white p-3.5 rounded-lg shadow-sm border border-gray-200 h-full flex flex-col justify-between hover:shadow-md transition-shadow">
            <div>
                <div className="flex justify-between items-start mb-1.5">
                    <p className="text-sm font-semibold text-gray-800 flex-1 mr-2">{label}</p>
                    <StatusBadge status={status} />
                </div>
                {documentData.signedUrl ? (
                    <a
                        href={documentData.signedUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-indigo-600 hover:text-indigo-800 text-xs font-medium break-all underline mb-1"
                        title={documentData.filePath || 'View Document'}
                    >
                        <DocumentTextIcon className="w-4 h-4 mr-1" />
                        View Document ({documentData.mimeType || 'PDF'})
                    </a>
                ) : documentData.filePath ? (
                    <p className="text-xs text-gray-500 italic mb-1">File path present but no view URL.</p>
                ) : (status !== 'NOT_SUBMITTED' && status !== 'N/A') && (
                    <p className="text-xs text-gray-500 italic mb-1">Document submitted, URL pending.</p>
                )}
                {documentData.uploadedOn && (
                    <p className="text-xs text-gray-500">Uploaded: {formatDate(documentData.uploadedOn)}</p>
                )}
                {documentData.verifiedOrRejectedOn && (
                    <p className="text-xs text-gray-500">Processed: {formatDate(documentData.verifiedOrRejectedOn)}</p>
                )}
            </div>
            {documentData.verificationNotes && (
                <p className="text-xs mt-2 pt-1.5 border-t border-gray-200 border-dashed text-gray-600">
                    <span className="font-medium">Notes:</span> {documentData.verificationNotes}
                </p>
            )}
        </div>
    );
};

// --- CollapsibleSection Component ---
const CollapsibleSection = ({ title, children, onToggle, isOpen, bg = 'white' }) => {
    return (
        <div className={`bg-white shadow rounded-lg mb-6`}>
            <button
                onClick={onToggle}
                className="w-full flex justify-between items-center p-4 border-b border-gray-200 focus:outline-none"
            >
                <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
                <ChevronDownIcon className={`w-6 h-6 text-gray-600 transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
            </button>
            <div
                className={`transition-all duration-300 ease-in-out overflow-hidden ${isOpen ? 'max-h-[5000px] opacity-100' : 'max-h-0 opacity-0'}`}
            >
                <div className={`p-4 bg-${bg}`}>
                    {children}
                </div>
            </div>
        </div>
    );
};


// --- Main KYC Details Page Component ---
export default function KycDetailsPage() {
    const router = useRouter();
    const [user, setUser] = useState(null); // This is the 'userDetails' object
    const [isLoadingUser, setIsLoadingUser] = useState(true);
    const [isGeneratingDownload, setIsGeneratingDownload] = useState(false);

    // State for Offer Modal
    const [showOfferModal, setShowOfferModal] = useState(false);
    const [isSubmittingOffer, setIsSubmittingOffer] = useState(false);
    const [offerData, setOfferData] = useState({
        creditLimit: '', tenureDays: '', interestRate: '', processingFeeValue: '',
        processingFeeType: 'flat', riskProfile: 'MEDIUM', notes: '',
    });
    const [showOfferSuccessModal, setShowOfferSuccessModal] = useState(false);
    console.log(showOfferSuccessModal);

    // State for collapsible sections
    const [openSections, setOpenSections] = useState({
        userKyc: true,
        businessInfo: true,
        businessDocs: true,
        financialDocs: true,
        shareholders: {},
        buyers: {},
        overallKyc: true,
    });

    const toggleSection = (sectionId, shareholderOrBuyerId = null) => {
        setOpenSections(prev => {
            if (shareholderOrBuyerId !== null) {
                const dynamicSection = prev[sectionId] || {};
                return {
                    ...prev,
                    [sectionId]: {
                        ...dynamicSection,
                        [shareholderOrBuyerId]: !dynamicSection[shareholderOrBuyerId]
                    }
                };
            }
            return { ...prev, [sectionId]: !prev[sectionId] };
        });
    };

    // --- Fee Calculation Helpers (from CreditAssessmentPage) ---
    const calculateMaxAllowedFee = useCallback((creditLimit) => {
        const limit = Number(creditLimit) || 0;
        if (limit <= 0) return MAX_PROCESSING_FEE_FLAT; // If no limit, cap at flat max
        const percentageMax = (MAX_PROCESSING_FEE_PERCENTAGE / 100) * limit;
        // The logic from CreditAssessmentPage had "+ 999999999" which seems like a debug artifact.
        // Corrected to: return Math.min(MAX_PROCESSING_FEE_FLAT, percentageMax);
        // If the original large number was intentional, please revert.
        return Math.min(MAX_PROCESSING_FEE_FLAT, percentageMax);
    }, []);

    const calculateCurrentFee = useCallback((type, value, creditLimit) => {
        const feeVal = Number(value) || 0;
        const limitVal = Number(creditLimit) || 0;
        if (type === 'flat') return feeVal;
        if (type === 'percentage') return (feeVal / 100) * limitVal;
        return 0;
    }, []);


    const fetchUserDetails = useCallback(async (id) => {
        console.log(`KycDetailsPage: Fetching user details for ID: ${id} via API.`);
        setIsLoadingUser(true);
        try {
            const response = await fetch(`/api/users/${id}`); // Ensure this API endpoint is correct
            if (!response.ok) {
                if (response.status === 404) {
                    console.error(`KycDetailsPage: User with ID ${id} not found via API.`);
                    router.push('/creditAssessment'); // Or a relevant error/dashboard page
                    return;
                }
                throw new Error(`KycDetailsPage: Failed to fetch user details: ${response.statusText}`);
            }
            const data = await response.json();
            setUser(data.user); // Assuming API returns { user: { ... } }

            const initialShareholderStates = {};
            (getNested(data.user, 'shareholders', []) || []).forEach((sh, index) => {
                initialShareholderStates[sh._id || `sh-${index}`] = true;
            });
            const initialBuyerStates = {};
            (getNested(data.user, 'kyc.buyers', []) || []).forEach((b, index) => {
                initialBuyerStates[b._id || `b-${index}`] = true;
            });
            setOpenSections(prev => ({
                ...prev,
                shareholders: initialShareholderStates,
                buyers: initialBuyerStates,
            }));

        } catch (error) {
            console.error("KycDetailsPage: Error fetching user details via API:", error);
            // Potentially redirect or show an error message to the user
            router.push('/creditAssessment');
        } finally {
            setIsLoadingUser(false);
        }
    }, [router]); // Removed setOpenSections from dependencies as it's defined outside or stable

    useEffect(() => {
        if (router.isReady) {
            // We only expect userId from the query now
            const { userId } = router.query;

            if (userId) {
                let assessmentDataFromSession = null;
                const sessionKey = `assessmentDataForUser_${String(userId)}`;

                try {
                    const storedData = sessionStorage.getItem(sessionKey);
                    if (storedData) {
                        assessmentDataFromSession = JSON.parse(storedData);
                        // Important: Remove the data from sessionStorage after it's been retrieved
                        // to prevent using stale data on subsequent visits if not intended.
                        sessionStorage.removeItem(sessionKey);
                    }
                } catch (e) {
                    console.error("KycDetailsPage: Failed to parse assessment data from sessionStorage.", e);
                    // Data might be malformed, proceed to API fetch as a fallback.
                    assessmentDataFromSession = null;
                }

                if (assessmentDataFromSession) {
                    console.log("KycDetailsPage: Loaded assessment data from sessionStorage.");
                    // Use the assessment data from sessionStorage
                    // The structure of assessmentObject might be { userDetails: {...} } or just {...}
                    const userData = assessmentDataFromSession.userDetails || assessmentDataFromSession;

                    if (userData && typeof userData === 'object') {
                        setUser(userData);

                        // Initialize open sections for shareholders and buyers based on the loaded data
                        const initialShareholderStates = {};
                        (getNested(userData, 'shareholders', []) || []).forEach((sh, index) => {
                            initialShareholderStates[sh._id || `sh-${index}`] = true;
                        });
                        const initialBuyerStates = {};
                        (getNested(userData, 'kyc.buyers', []) || []).forEach((b, index) => {
                            initialBuyerStates[b._id || `b-${index}`] = true;
                        });
                        setOpenSections(prev => ({
                            ...prev,
                            shareholders: initialShareholderStates,
                            buyers: initialBuyerStates,
                        }));
                        setIsLoadingUser(false);
                    } else {
                        // sessionStorage data was not in expected format, fallback to API
                        console.warn("KycDetailsPage: Assessment data from sessionStorage was not in the expected format. Fetching via API.");
                        fetchUserDetails(String(userId));
                    }
                } else {
                    // No data in sessionStorage, fetch user details via API
                    console.log("KycDetailsPage: No assessment data in sessionStorage. Fetching user details via API.");
                    fetchUserDetails(String(userId));
                }
            } else {
                console.warn('KycDetailsPage: Neither assessmentObjectString nor userId found in query. Redirecting.');
                router.push('/creditAssessment'); // Redirect if no userId is present
            }
        }
    }, [router.isReady, router.query, fetchUserDetails, router]); // Added router to dependencies of useEffect


    const handleNavigateBack = () => {
        router.push('/creditAssessment'); // Navigate to the main assessment page
    };

    const handleOpenOfferModal = () => {
        if (!user) {
            alert("User data not loaded.");
            return;
        }
        if (String(getNested(user, 'kyc.verificationStatus', '')).toUpperCase() !== 'APPROVED') {
            alert("KYC for this user is not yet approved. Cannot create an offer.");
            return;
        }
        // Reset form for a new offer
        setOfferData({
            creditLimit: '', tenureDays: '', interestRate: '', processingFeeValue: '',
            processingFeeType: 'flat', riskProfile: 'MEDIUM', notes: '',
        });
        setShowOfferModal(true);
    };

    const handleSubmitOffer = useCallback(async () => {
        if (!user) {
            alert('User data is not available.');
            return;
        }
        if (String(getNested(user, 'kyc.verificationStatus', '')).toUpperCase() !== 'APPROVED') {
            alert('Cannot submit offer. User KYC is not approved.');
            return;
        }

        const lenderId = localStorage.getItem('userId');
        const borrowerUserId = user._id;

        if (!borrowerUserId || !lenderId) {
            alert('Borrower User ID or Lender ID is missing. Cannot submit offer.');
            return;
        }

        const currentLimit = Number(offerData.creditLimit);
        const currentTenure = Number(offerData.tenureDays);
        const currentInterest = Number(offerData.interestRate);
        const currentFeeValue = Number(offerData.processingFeeValue);

        if (!(currentLimit > 0) || !(currentTenure > 0) || !(currentInterest >= 0) || !(currentFeeValue >= 0)) {
            alert('Please fill in Credit Limit (>0), Tenure (>0), Service Fee (>=0), and Processing Fee (>=0) with valid numbers.');
            return;
        }

        const currentFee = calculateCurrentFee(offerData.processingFeeType, currentFeeValue, currentLimit);
        const maxAllowed = calculateMaxAllowedFee(currentLimit);

        if (currentFee > maxAllowed) {
            alert(`Processing Fee (QAR ${currentFee.toFixed(2)}) exceeds the maximum allowed (QAR ${maxAllowed.toFixed(2)} for this limit). Please adjust.`);
            return;
        }

        setIsSubmittingOffer(true);

        const payload = {
            merchantId: borrowerUserId,
            lenderId: lenderId,
            offerType: "creditLineOffer",
            creditLimit: currentLimit,
            tenureDays: currentTenure,
            interestRate: String(currentInterest),
            currency: 'QAR',
            processingFee: { type: offerData.processingFeeType, value: currentFeeValue },
            riskProfile: offerData.riskProfile || 'MEDIUM',
            notes: offerData.notes || '',
            status: 'PENDING'
        };
        if (!payload.notes?.trim()) delete payload.notes;

        try {
            const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/createOffer`, payload);
            if (!response.data?.offerId) { // Check based on expected success response from CreditAssessmentPage
                throw new Error(response.data?.message || "Failed to create offer: No offer ID received.");
            }
            setShowOfferModal(false); // Close form modal
            setShowOfferSuccessModal(true); // Show success modal
        } catch (error) {
            console.error('Error submitting offer:', error);
            alert(`Error submitting offer: ${error.response?.data?.message || error.message || 'An unknown error occurred'}`);
        } finally {
            setIsSubmittingOffer(false);
        }
    }, [user, offerData, calculateCurrentFee, calculateMaxAllowedFee]);


    const handleDownloadAllDocuments = useCallback(async () => {
        // ... (keep existing handleDownloadAllDocuments logic as provided in the prompt for KycDetailsPage)
        if (!user) return;
        setIsGeneratingDownload(true);
        console.log("Starting document zip generation for KycDetailsPage...");

        const zip = new JSZip();
        const documentsToFetch = [];
        const failedDocs = [];

        const sanitizeFilename = (name) => {
            if (!name) return 'unnamed_document';
            return name
                .replace(/[\/\\:*?"<>|]/g, '_')
                .replace(/[\x00-\x1f\x7f]/g, '_')
                .replace(/_+/g, '_')
                .replace(/\s+/g, '_')
                .replace(/^_|_$/g, '');
        };

        const addDocToList = (docData, label) => {
            if (docData && docData.signedUrl && docData.filePath) {
                const uploadedFileName = docData.filePath.split('/').pop() || `file_${label.replace(/\s+/g, '_')}`;
                const docName = label;
                documentsToFetch.push({
                    url: docData.signedUrl,
                    zipFileName: `${sanitizeFilename(docName)}_${sanitizeFilename(uploadedFileName)}`,
                    label: docName,
                    uploadedFileName: uploadedFileName,
                });
            }
        };

        const rootBusinessDocFields = [
            { key: 'commercialRegistration', label: 'Commercial Registration (CR)' },
            { key: 'tradeLicense', label: 'Trade License' },
            { key: 'taxCard', label: 'Tax Card' },
            { key: 'establishmentCard', label: 'Establishment Card' },
            { key: 'memorandumOfAssociation', label: 'Memorandum of Association (MOA)' },
            { key: 'articleOfAssociation', label: 'Articles of Association (AOA)' },
            { key: 'otherDocument', label: 'Additional Document 1' },
            { key: 'otherDocumentTwo', label: 'Additional Document 2' },
        ];
        rootBusinessDocFields.forEach(field => addDocToList(getNested(user, field.key), field.label));

        const rootFinancialDocFields = [
            { key: 'bankStatement', label: 'Bank Statements' },
            { key: 'auditedFinancialReport', label: 'Audited Financial Report' },
            { key: 'commercialCreditReport', label: 'Commercial Credit Report (CCR)' },
            { key: 'cashFlowLedger', label: 'Cash Flow Ledger or Mgmt Accounts' },
        ];
        rootFinancialDocFields.forEach(field => addDocToList(getNested(user, field.key), field.label));

        addDocToList(getNested(user, 'kyc.qatariId'), 'User Qatari ID');
        addDocToList(getNested(user, 'kyc.passport'), 'User Passport');
        addDocToList(getNested(user, 'kyc.utilityBill'), 'User Utility Bill');
        addDocToList(getNested(user, 'kyc.incomeDetails.proofOfIncome'), 'User Proof of Income');

        (getNested(user, 'shareholders', []) || []).forEach((sh, index) => {
            const prefix = `Shareholder ${index + 1} (${getNested(sh, 'firstName', 'N/A')} ${getNested(sh, 'lastName', 'N/A')})`;
            addDocToList(getNested(sh, 'passport'), `${prefix} Passport`);
            addDocToList(getNested(sh, 'qid'), `${prefix} QID`);
            addDocToList(getNested(sh, 'proofOfAddress'), `${prefix} Proof of Address`);
        });

        (getNested(user, 'kyc.buyers', []) || []).forEach((buyer, index) => {
            const prefix = `Buyer ${index + 1} (${getNested(buyer, 'buyerName', 'N/A')})`;
            addDocToList(getNested(buyer, 'companyDocument'), `${prefix} Company Document`);
        });

        if (documentsToFetch.length > 0) {
            await Promise.allSettled(
                documentsToFetch.map(async (docInfo) => {
                    try {
                        const response = await fetch(docInfo.url);
                        if (!response.ok) throw new Error(`Download failed for ${docInfo.uploadedFileName}: ${response.status}`);
                        let fileBlob = await response.blob();

                        const isPdf = docInfo.zipFileName.toLowerCase().endsWith('.pdf') || fileBlob.type === 'application/pdf';
                        if (isPdf && fileBlob.size > 0) {
                            try {
                                const arrayBuffer = await fileBlob.arrayBuffer();
                                const pdfDoc = await PDFDocument.load(arrayBuffer);
                                const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
                                const watermarkText = `Restricted`;
                                const pages = pdfDoc.getPages();
                                for (const page of pages) {
                                    const { width, height } = page.getSize();
                                    page.drawText(watermarkText, {
                                        x: width / 2 - (watermarkText.length * 6), // Adjusted for better centering
                                        y: height / 2,
                                        size: 32, font: helveticaFont, color: rgb(0.75, 0.75, 0.75), opacity: 0.3, rotate: degrees(-45),
                                    });
                                }
                                const pdfBytes = await pdfDoc.save();
                                fileBlob = new Blob([pdfBytes], { type: 'application/pdf' });
                            } catch (watermarkError) {
                                console.error(`Failed to apply watermark to ${docInfo.zipFileName}:`, watermarkError);
                                failedDocs.push(`${docInfo.zipFileName} (Watermark failed)`);
                            }
                        }
                        if (fileBlob.size === 0) throw new Error(`Downloaded empty file for ${docInfo.uploadedFileName}`);
                        zip.file(docInfo.zipFileName, fileBlob);
                        return { status: 'fulfilled' };
                    } catch (error) {
                        console.error(`Failed processing ${docInfo.uploadedFileName}:`, error);
                        failedDocs.push(`${docInfo.uploadedFileName} (Download/Process Error: ${error.message})`);
                        return { status: 'rejected', reason: error.message };
                    }
                })
            );

            if (failedDocs.length > 0) {
                alert(`Warning: Could not download or process the following documents:\n\n${failedDocs.join('\n')}\n\nThe ZIP file will be generated with the successfully processed documents.`);
            }

            if (Object.keys(zip.files).length > 0) {
                zip.generateAsync({ type: 'blob', compression: "DEFLATE", compressionOptions: { level: 6 } })
                    .then(blob => {
                        const zipFileName = `kyc_documents_${sanitizeFilename(getNested(user, 'firstName', 'user'))}_${sanitizeFilename(getNested(user, 'lastName', ''))}_${user?._id || 'data'}.zip`;
                        saveAs(blob, zipFileName);
                    })
                    .catch(err => {
                        console.error('Error generating ZIP file:', err);
                        alert(`Failed to generate ZIP: ${err.message}`);
                    });
            } else if (documentsToFetch.length > 0 && failedDocs.length === documentsToFetch.length) {
                alert("All documents failed to download. ZIP file cannot be created.");
            } else {
                alert("No documents were available or successfully processed to include in the ZIP.");
            }
        } else {
            alert("No documents found for this user to download.");
        }
        setIsGeneratingDownload(false);
    }, [user]);

    // --- Render Offer Form Section (Adapted from CreditAssessmentPage) ---
    const renderOfferFormSection = () => {
        if (!user) return null;

        const kycApproved = String(getNested(user, 'kyc.verificationStatus', '')).toUpperCase() === 'APPROVED';
        // For this page, direct checks for existing offers (hasAcceptedOffer, thisLenderOfferDetails) are not
        // straightforward without additional data fetching. The primary gate is KYC approval.
        const canSubmitFormFields = kycApproved; // Simplified for enabling/disabling form fields

        return (
            <div className="bg-white p-6 sm:p-8 rounded-lg"> {/* Added sm:p-8 for consistency with modal examples */}
                <h3 className="text-2xl font-bold text-gray-800 mb-3">
                    Submit New Credit Line Offer
                </h3>
                <div className="mb-6 border-b border-gray-200 pb-5">
                    <p className="text-sm text-gray-700">
                        Creating offer for <span className="font-semibold">{getNested(user, 'firstName')} {getNested(user, 'lastName')}</span>
                    </p>
                    <div className="mt-2 flex items-center">
                        <span className="text-sm mr-2">KYC Status:</span>
                        <StatusBadge status={getNested(user, 'kyc.verificationStatus', 'N/A')} />
                    </div>
                    {!kycApproved && (
                        <div className="mt-3 p-3 bg-red-50 border-l-4 border-red-500 rounded-md">
                            <p className="text-sm text-red-700">KYC must be 'APPROVED' to submit an offer.</p>
                        </div>
                    )}
                </div>
                <form onSubmit={(e) => { e.preventDefault(); if (canSubmitFormFields) { handleSubmitOffer(); } }}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                        {/* Credit Limit */}
                        <div className="relative">
                            <label htmlFor="offerCreditLimit" className="block text-sm font-medium text-gray-700 mb-1">
                                Credit Limit (QAR) <span className="text-red-500">*</span>
                            </label>
                            <div className="relative mt-1 rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 sm:text-sm">QAR</span>
                                </div>
                                <input
                                    type="number" id="offerCreditLimit" name="creditLimit" min="1" step="any"
                                    value={offerData.creditLimit}
                                    onChange={(e) => setOfferData({ ...offerData, creditLimit: e.target.value })}
                                    className="pl-14 py-2.5 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100 disabled:text-gray-500"
                                    placeholder="50,000" required disabled={!canSubmitFormFields || isSubmittingOffer}
                                />
                            </div>
                        </div>
                        {/* Tenure */}
                        <div>
                            <label htmlFor="offerTenure" className="block text-sm font-medium text-gray-700 mb-1">
                                Tenure (Days) <span className="text-red-500">*</span>
                            </label>
                            <div className="relative mt-1 rounded-md shadow-sm">
                                <input
                                    type="number" id="offerTenure" name="tenureDays" min="1" step="1"
                                    value={offerData.tenureDays}
                                    onChange={(e) => setOfferData({ ...offerData, tenureDays: e.target.value })}
                                    className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100 disabled:text-gray-500"
                                    placeholder="90" required disabled={!canSubmitFormFields || isSubmittingOffer}
                                />
                                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 sm:text-sm">days</span>
                                </div>
                            </div>
                        </div>
                        {/* Interest Rate */}
                        <div>
                            <label htmlFor="offerInterestRate" className="block text-sm font-medium text-gray-700 mb-1">
                                Service Fee % <span className="text-red-500">*</span>
                            </label>
                            <div className="relative mt-1 rounded-md shadow-sm">
                                <input
                                    type="number" id="offerInterestRate" name="interestRate" min="0" step="0.01"
                                    value={offerData.interestRate}
                                    onChange={(e) => setOfferData({ ...offerData, interestRate: e.target.value })}
                                    className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100 disabled:text-gray-500"
                                    placeholder="12.5" required disabled={!canSubmitFormFields || isSubmittingOffer}
                                />
                                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span className="text-gray-500 sm:text-sm">%</span>
                                </div>
                            </div>
                        </div>
                        {/* Processing Fee */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Processing Fee <span className="text-red-500">*</span>
                            </label>
                            <div className="flex items-center space-x-2">
                                <div className="relative flex-grow">
                                    <input
                                        type="number" name="processingFeeValue" min="0"
                                        step={offerData.processingFeeType === 'percentage' ? '0.01' : '1'}
                                        value={offerData.processingFeeValue}
                                        onChange={(e) => setOfferData({ ...offerData, processingFeeValue: e.target.value })}
                                        className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100 disabled:text-gray-500"
                                        placeholder="Fee value" aria-label="Processing Fee Value" required disabled={!canSubmitFormFields || isSubmittingOffer}
                                    />
                                </div>
                                <select
                                    name="processingFeeType" value={offerData.processingFeeType}
                                    onChange={(e) => setOfferData({ ...offerData, processingFeeType: e.target.value })}
                                    className="py-2.5 px-3 block w-24 rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100 disabled:text-gray-500"
                                    aria-label="Processing Fee Type" disabled={!canSubmitFormFields || isSubmittingOffer}
                                >
                                    <option value="flat">QAR</option>
                                    <option value="percentage">%</option>
                                </select>
                            </div>
                            {/* Fee Calculation Display */}
                            {(() => {
                                const currentFee = calculateCurrentFee(offerData.processingFeeType, offerData.processingFeeValue, offerData.creditLimit);
                                const maxAllowed = calculateMaxAllowedFee(offerData.creditLimit);
                                const isExceeded = currentFee > maxAllowed && Number(offerData.creditLimit) > 0; // Ensure creditLimit is positive
                                return (<>
                                    <p className="mt-2 text-xs text-gray-500">Max Allowed: {MAX_PROCESSING_FEE_PERCENTAGE}% or QAR {MAX_PROCESSING_FEE_FLAT}. Max for this limit: QAR {maxAllowed.toFixed(2)}</p>
                                    <p className={`mt-1 text-xs ${isExceeded ? 'text-red-600 font-semibold' : 'text-emerald-600'}`}>
                                        Calculated Fee: QAR {currentFee.toFixed(2)} {isExceeded ?
                                            <span className="inline-flex items-center ml-1">
                                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path></svg>
                                                Exceeds Max!
                                            </span> : ''}
                                    </p>
                                </>);
                            })()}
                        </div>
                        {/* Risk Profile - Kept for consistency, can be removed if not needed */}
                        <div>
                            <label htmlFor="offerRiskProfile" className="block text-sm font-medium text-gray-700 mb-1">Risk Profile</label>
                            <select
                                id="offerRiskProfile" name="riskProfile" value={offerData.riskProfile}
                                onChange={(e) => setOfferData({ ...offerData, riskProfile: e.target.value })}
                                className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100 disabled:text-gray-500"
                                disabled={!canSubmitFormFields || isSubmittingOffer}
                            >
                                <option value="LOW">Low</option>
                                <option value="MEDIUM">Medium</option>
                                <option value="HIGH">High</option>
                            </select>
                        </div>
                        {/* Notes */}
                        <div className="md:col-span-2">
                            <label htmlFor="offerNotes" className="block text-sm font-medium text-gray-700 mb-1">Internal Notes (Optional)</label>
                            <textarea
                                id="offerNotes" name="notes" value={offerData.notes}
                                onChange={(e) => setOfferData({ ...offerData, notes: e.target.value })}
                                className="py-2.5 px-3 block w-full rounded-md border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100 disabled:text-gray-500"
                                rows="3" placeholder="Add internal notes about this offer..."
                                disabled={!canSubmitFormFields || isSubmittingOffer}
                            ></textarea>
                        </div>
                        {/* Submit Button Area - Footer will handle the actual submit button */}
                    </div>
                </form>
            </div>
        );
    };


    if (isLoadingUser || !user) {
        return (
            <div className="min-h-screen bg-white flex justify-center items-center">
                <p className="text-gray-600 text-lg">Loading user details...</p>
            </div>
        );
    }

    const rootBusinessDocFieldsDisplay = [
        { key: 'commercialRegistration', label: 'Commercial Registration (CR)' },
        { key: 'tradeLicense', label: 'Trade License' },
        { key: 'taxCard', label: 'Tax Card' },
        { key: 'establishmentCard', label: 'Establishment Card' },
        { key: 'memorandumOfAssociation', label: 'Memorandum of Association (MOA)' },
        { key: 'articleOfAssociation', label: 'Articles of Association (AOA)' },
        { key: 'otherDocument', label: 'Additional Document 1' },
        { key: 'otherDocumentTwo', label: 'Additional Document 2' },
    ];

    const rootFinancialDocFieldsDisplay = [
        { key: 'bankStatement', label: 'Bank Statements' },
        { key: 'auditedFinancialReport', label: 'Audited Financial Report' },
        { key: 'commercialCreditReport', label: 'Commercial Credit Report (CCR)' },
        { key: 'cashFlowLedger', label: 'Cash Flow Ledger / Mgmt Accounts' },
    ];


    const GeolocationDetailsItems = ({ user }) => {
        const geolocationData = getNested(user, 'commercialRegistration.shuftiCallbackData.info.geolocation');

        let locationStatusElement;

        if (geolocationData) {
            const countryName = geolocationData.country_name || '';
            const countryCode = geolocationData.country_code || '';

            if (countryName.toLowerCase() === 'qatar' || countryCode.toUpperCase() === 'QA') {
                locationStatusElement = (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-medium bg-green-100 text-green-800 whitespace-nowrap">
                        🌍 Within Qatar
                    </span>
                );
            } else {
                locationStatusElement = (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-medium bg-red-100 text-red-800 whitespace-nowrap">
                        {`📍Located in: ${countryName || 'Unknown'}`}
                    </span>
                );
            }
        } else {
            locationStatusElement = <span className="text-gray-500 italic">N/A</span>;
        }

        return (
            <> {/* React.Fragment to group list items */}
                <RenderInfoItemViewOnly
                    label="Geolocation Status"
                    value={locationStatusElement}
                />
                <RenderInfoItemViewOnly
                    label="Geolocation Country"
                    value={geolocationData?.country_name || 'N/A'}
                />
                <RenderInfoItemViewOnly
                    label="Geolocation Region"
                    value={geolocationData?.region_name || 'N/A'}
                />
                <RenderInfoItemViewOnly
                    label="CR Geo IP Address"
                    value={geolocationData?.ip || 'N/A'}
                />
            </>
        );
    };

    return (
        <div className="min-h-screen bg-white font-sans">
            <div className="p-4 sm:p-6 lg:p-8">
                {/* Back Link */}
                <div className="mb-6">
                    <button
                        onClick={handleNavigateBack}
                        className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600"
                    >
                        <ChevronLeftIcon className="w-5 h-5 mr-1" />
                        Back to Applications
                    </button>
                </div>

                {/* Header Bar */}
                <div className="bg-white p-4 sm:p-6 shadow-md rounded-lg mb-6">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-800">Review Application</h1>
                            <p className="text-sm text-gray-600 mt-1">
                                User: <span className="font-semibold">{getNested(user, 'firstName', '')} {getNested(user, 'middleName', '')} {getNested(user, 'lastName', '')}</span>
                                {user._id && <span className="text-xs text-gray-500 ml-2">(ID: {user._id})</span>}
                            </p>
                        </div>
                        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
                            <button
                                onClick={handleOpenOfferModal} // Changed from handleCreateOffer
                                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                <CreditCardIcon className="w-5 h-5 mr-2" />
                                Create Credit Offer
                            </button>
                            <button
                                onClick={handleDownloadAllDocuments}
                                disabled={isGeneratingDownload}
                                className={`inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${isGeneratingDownload ? 'opacity-50 cursor-not-allowed' : ''}`}
                            >
                                {isGeneratingDownload ? (
                                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                ) : (
                                    <ArrowDownOnSquareIcon className="w-5 h-5 mr-2" />
                                )}
                                {isGeneratingDownload ? 'Downloading...' : 'Download Documents'}
                            </button>
                        </div>
                    </div>
                </div>

                {/* Main Content Area */}
                <div className="bg-gray-200 p-3 sm:p-4 rounded-lg">
                    {/* User & KYC Details Section */}
                    <CollapsibleSection
                        title="User & KYC Details"
                        isOpen={openSections.userKyc}
                        onToggle={() => toggleSection('userKyc')}
                    >
                        <div className="bg-[#eef6f8] p-4 rounded-md mb-4">
                            <h3 className="text-md font-semibold text-gray-700 mb-3">Personal Details</h3>
                            <dl className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-0">
                                <RenderInfoItemViewOnly label="First Name" value={getNested(user, 'firstName')} />
                                <RenderInfoItemViewOnly label="Middle Name" value={getNested(user, 'middleName')} />
                                <RenderInfoItemViewOnly label="Last Name" value={getNested(user, 'lastName')} />
                                <RenderInfoItemViewOnly label="Email" value={getNested(user, 'email')} />
                                <RenderInfoItemViewOnly label="Mobile Number" value={getNested(user, 'mobileNo')} />
                                <RenderInfoItemViewOnly
                                    label="Account Status"
                                    value={
                                        <StatusBadge status={getNested(user, 'isActive') ? 'ACTIVE' : 'INACTIVE'} />
                                    }
                                />

                                {/* New Status Fields */}
                                <RenderInfoItemViewOnly
                                    label="Business AML Status"
                                    value={
                                        <StatusBadge status={getNested(user, 'kyc.amlStatus', 'N/A')} />
                                    }
                                />
                                <RenderInfoItemViewOnly
                                    label="Business Address Verification"
                                    value={
                                        <StatusBadge status={getNested(user, 'kyc.businessDetails.businessAddressVerification.status', 'N/A')} />
                                    }
                                />
                                <RenderInfoItemViewOnly
                                    label="KYB Verification Status (CR)"
                                    value={
                                        <StatusBadge status={getNested(user, 'commercialRegistration.verificationStatus', 'N/A')} />
                                    }
                                />
                                <GeolocationDetailsItems user={user} />
                            </dl>
                        </div>
                        <div className="bg-[#eef6f8] p-4 rounded-md">
                            <h3 className="text-md font-semibold text-gray-700 mb-3">Bank Details</h3>
                            <dl className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-0">
                                <RenderInfoItemViewOnly label="Account Number" value={getNested(user, 'kyc.incomeDetails.accountNumber')} />
                                <RenderInfoItemViewOnly label="IBAN" value={getNested(user, 'kyc.incomeDetails.ifscCode')} />
                            </dl>
                        </div>
                    </CollapsibleSection>

                    {/* Business Details Section */}
                    <CollapsibleSection
                        title="Business Details"
                        isOpen={openSections.businessInfo}
                        onToggle={() => toggleSection('businessInfo')}
                    >
                        <div className="bg-[#eef6f8] p-4 rounded-md mb-4">
                            <h3 className="text-md font-semibold text-gray-700 mb-3">Business Information</h3>
                            <dl className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-0">
                                <RenderInfoItemViewOnly label="Business Name (Arabic)" value={getNested(user, 'kyc.businessDetails.businessName')} />
                                <RenderInfoItemViewOnly label="Legal Entity Name" value={getNested(user, 'kyc.businessDetails.legalEntityName')} />
                                <RenderInfoItemViewOnly label="Establishment Name" value={getNested(user, 'kyc.businessDetails.establishmentName')} />
                                <RenderInfoItemViewOnly label="Legal Form" value={getNested(user, 'kyc.businessDetails.legalForm')} />
                                <RenderInfoItemViewOnly label="CR Number" value={getNested(user, 'kyc.businessDetails.crNumber')} />
                                <RenderInfoItemViewOnly label="CR Issue Date" value={formatDate(getNested(user, 'kyc.businessDetails.crIssueDate'), false)} />
                                <RenderInfoItemViewOnly label="CR Expiry Date" value={formatDate(getNested(user, 'kyc.businessDetails.crExpiryDate'), false)} />
                                <RenderInfoItemViewOnly label="Establishment ID" value={getNested(user, 'kyc.businessDetails.establishmentId')} />
                                <RenderInfoItemViewOnly label="Establishment ID Issue Date" value={formatDate(getNested(user, 'kyc.businessDetails.establishmentIdIssueDate'), false)} />
                                <RenderInfoItemViewOnly label="Establishment ID Expiry Date" value={formatDate(getNested(user, 'kyc.businessDetails.establishmentIdExpiryDate'), false)} />
                                <RenderInfoItemViewOnly label="Branch Count" value={getNested(user, 'kyc.businessDetails.branchCount')} />
                                <RenderInfoItemViewOnly label="Trade License (TL) Number" value={getNested(user, 'licenseNumber')} />
                                <RenderInfoItemViewOnly label="TL Issue Date" value={formatDate(getNested(user, 'kyc.businessDetails.tlIssueDate'), false)} />
                                <RenderInfoItemViewOnly label="TL Expiry Date" value={formatDate(getNested(user, 'kyc.businessDetails.tlExpiryDate'), false)} />
                            </dl>
                        </div>
                        <div className="bg-[#eef6f8] p-4 rounded-md">
                            <h3 className="text-md font-semibold text-gray-700 mb-3">Business Address</h3>
                            <dl className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-0">
                                <RenderInfoItemViewOnly label="Country" value={getNested(user, 'kyc.businessDetails.businessCountry')} />
                                <RenderInfoItemViewOnly label="Address Line 1" value={getNested(user, 'kyc.businessDetails.businessAddressLine1')} />
                                <RenderInfoItemViewOnly label="Address Line 2" value={getNested(user, 'kyc.businessDetails.businessAddressLine2')} />
                                <RenderInfoItemViewOnly label="City" value={getNested(user, 'kyc.businessDetails.businessCity')} />
                            </dl>
                        </div>
                    </CollapsibleSection>

                    {/* Business Documents Section */}
                    <CollapsibleSection
                        title="Business Documents"
                        bg="[#eef6f8]"
                        isOpen={openSections.businessDocs}
                        onToggle={() => toggleSection('businessDocs')}
                    >
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {rootBusinessDocFieldsDisplay.map(field => (
                                <RenderDocumentDisplayViewOnly
                                    key={field.key}
                                    documentData={getNested(user, field.key, null)}
                                    label={field.label}
                                />
                            ))}
                            {rootBusinessDocFieldsDisplay.every(field => !getNested(user, field.key)) && (
                                <p className="text-gray-500 italic md:col-span-full text-center py-4">No business documents found.</p>
                            )}
                        </div>
                    </CollapsibleSection>

                    {/* Financial Documents Section */}
                    <CollapsibleSection
                        title="Financial Documents"
                        bg="[#eef6f8]"
                        isOpen={openSections.financialDocs}
                        onToggle={() => toggleSection('financialDocs')}
                    >
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {rootFinancialDocFieldsDisplay.map(field => (
                                <RenderDocumentDisplayViewOnly
                                    key={field.key}
                                    documentData={getNested(user, field.key, null)}
                                    label={field.label}
                                />
                            ))}
                            {rootFinancialDocFieldsDisplay.every(field => !getNested(user, field.key)) && (
                                <p className="text-gray-50 italic md:col-span-full text-center py-4">No financial documents found.</p>
                            )}
                        </div>
                    </CollapsibleSection>

                    {/* Shareholder Details Section(s) */}
                    {(getNested(user, 'shareholders', []) || []).map((sh, index) => {
                        const shareholderId = sh._id || `sh-${index}`; // Ensure a unique key for state
                        return (
                            <CollapsibleSection
                                key={shareholderId}
                                bg={"[#dfe5e5]"}
                                title={`Shareholder ${index + 1}: ${getNested(sh, 'firstName', '')} ${getNested(sh, 'lastName', '')}`}
                                isOpen={openSections.shareholders[shareholderId] !== undefined ? openSections.shareholders[shareholderId] : true}
                                onToggle={() => toggleSection('shareholders', shareholderId)}
                            >
                                <div className="p-4 rounded-md mb-4"> {/* Consider bg-[#eef6f8] if you want consistent background for shareholder info */}
                                    <dl className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-2 gap-y-0"> {/* Reduced gap-y */}
                                        <RenderInfoItemViewOnly label="First Name" value={getNested(sh, 'firstName')} />
                                        <RenderInfoItemViewOnly label="Middle Name" value={getNested(sh, 'middleName')} />
                                        <RenderInfoItemViewOnly label="Last Name" value={getNested(sh, 'lastName')} />
                                        <RenderInfoItemViewOnly label="Email" value={getNested(sh, 'email')} />
                                        <RenderInfoItemViewOnly label="Zone" value={getNested(sh, 'address.zone')} />
                                        <RenderInfoItemViewOnly label="Street No." value={getNested(sh, 'address.streetNo')} />
                                        <RenderInfoItemViewOnly label="Building No." value={getNested(sh, 'address.buildingNo')} />
                                        <RenderInfoItemViewOnly label="Floor No." value={getNested(sh, 'address.floorNo')} />
                                        <RenderInfoItemViewOnly label="Unit No." value={getNested(sh, 'address.unitNo')} />
                                        <RenderInfoItemViewOnly label="Added On" value={formatDate(getNested(sh, 'addedOn'))} />
                                        <RenderInfoItemViewOnly label="Modified On" value={formatDate(getNested(sh, 'modifiedOn'))} />
                                        <RenderInfoItemViewOnly
                                            label="Overall KYC Verification Status"
                                            value={
                                                <StatusBadge status={getNested(sh, 'kycVerificationStatus', 'N/A')} />
                                            }
                                        />
                                        <RenderInfoItemViewOnly
                                            label="AML Status"
                                            value={
                                                <StatusBadge status={getNested(sh, 'amlStatus', 'N/A')} />
                                            }
                                        />
                                        <RenderInfoItemViewOnly
                                            label="Video KYC Status"
                                            value={
                                                <StatusBadge status={getNested(sh, 'videoKyc.status', 'N/A')} />
                                            }
                                        />
                                        <RenderInfoItemViewOnly
                                            label="Address Verification Status"
                                            value={
                                                <StatusBadge status={getNested(sh, 'addressVerification.status', 'N/A')} />
                                            }
                                        />
                                    </dl>
                                </div>
                                <div className='bg-[#eef6f8] p-4 rounded-lg'>
                                    <h3 className="text-md font-semibold text-gray-700 mb-3">Shareholder Documents</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'passport')} label="Passport" />
                                        <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'qid')} label="QID" />
                                        <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'proofOfAddress')} label="Proof of Address" />
                                    </div>
                                </div>
                            </CollapsibleSection>
                        );
                    })}
                    {(!getNested(user, 'shareholders') || getNested(user, 'shareholders', []).length === 0) && (
                        <div className="bg-white p-5 shadow rounded-lg mb-6">
                            <h2 className="text-lg font-semibold text-gray-800">Shareholder Details</h2>
                            <p className="text-gray-500 italic mt-2">No shareholders listed.</p>
                        </div>
                    )}

                    {/* Buyer Details Section(s) */}
                    {(getNested(user, 'kyc.buyers', []) || []).map((buyer, index) => {
                        const buyerId = buyer._id || `buyer-${index}`;
                        return (
                            <CollapsibleSection
                                bg={"[#dfe5e5]"}
                                key={buyerId}
                                title={`Buyer ${index + 1}: ${getNested(buyer, 'buyerName', 'N/A')}`}
                                isOpen={openSections.buyers[buyerId] !== undefined ? openSections.buyers[buyerId] : true}
                                onToggle={() => toggleSection('buyers', buyerId)}
                            >
                                <div className="bg-[#eef6f8] p-4 rounded-md mb-4">
                                    <h3 className="text-md font-semibold text-gray-700 mb-3">Buyer Information</h3>
                                    <dl className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-0"> {/* Reduced gap-y */}
                                        <RenderInfoItemViewOnly label="Contact Person" value={getNested(buyer, 'contactPerson')} />
                                        <RenderInfoItemViewOnly label="Contact Phone" value={getNested(buyer, 'contactPhone')} />
                                        <RenderInfoItemViewOnly label="Contact Email" value={getNested(buyer, 'contactEmail')} />
                                        <RenderInfoItemViewOnly label="Registration Number" value={getNested(buyer, 'registrationNumber')} />
                                    </dl>
                                </div>
                                {getNested(buyer, 'companyDocument') && (
                                    <div>
                                        <h3 className="text-md font-semibold text-gray-700 mt-4 mb-3 pl-4">Buyer Company Document</h3> {/* Added pl-4 to align with other content if needed */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 pt-0"> {/* Added p-4 pt-0 or adjust as needed */}
                                            <RenderDocumentDisplayViewOnly documentData={getNested(buyer, 'companyDocument')} label="Company Document" />
                                        </div>
                                    </div>
                                )}
                            </CollapsibleSection>
                        );
                    })}
                    {(!getNested(user, 'kyc.buyers') || getNested(user, 'kyc.buyers', []).length === 0) && (
                        <div className="bg-white p-5 shadow rounded-lg mb-6">
                            <h2 className="text-lg font-semibold text-gray-800">Buyer Details</h2>
                            <p className="text-gray-500 italic mt-2">No buyer details listed.</p>
                        </div>
                    )}

                    {/* Overall KYC Verification Section */}
                    <CollapsibleSection
                        title="Overall KYC Verification"
                        isOpen={openSections.overallKyc}
                        onToggle={() => toggleSection('overallKyc')}
                    >
                        {/* This section's content doesn't have a separate colored subsection in the UI example, so items are direct. */}
                        <dl className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-0"> {/* Reduced gap-y */}
                            <div className="py-1.5 flex flex-row items-baseline"> {/* MODIFIED HERE */}
                                <dt className="text-sm font-medium text-gray-500 whitespace-nowrap">Overall KYC Status:&nbsp;</dt> {/* MODIFIED HERE */}
                                <dd className="text-sm text-gray-900"><StatusBadge status={getNested(user, 'kyc.verificationStatus')} /></dd> {/* mt-0.5 removed as flex handles alignment */}
                            </div>
                            <RenderInfoItemViewOnly label="Verified/Processed On" value={formatDate(getNested(user, 'kyc.verifiedOn'))} />
                            <RenderInfoItemViewOnly label="Overall Verification Notes" value={getNested(user, 'kyc.verificationNotes', '')} isFullWidth={true} />
                        </dl>
                    </CollapsibleSection>
                    {showOfferModal && (renderOfferFormSection())}

                </div>
            </div>
        </div>
    );
}
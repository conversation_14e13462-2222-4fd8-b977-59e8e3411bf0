import React, { useState, useEffect, useCallback, useMemo } from 'react';
import axios from 'axios';
import { Disclosure } from '@headlessui/react'; // Import Disclosure
import { ArrowTopRightOnSquareIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline'; // Import icons
import config from '../../../config.json';
import * as XLSX from 'xlsx';
import LoadingModal from '../../components/Loading';

// --- Reusable Helper Functions ---
const getNested = (obj, path, defaultValue = undefined) => {
  // (Keep the existing getNested function)
  try {
    if (!path || typeof path !== 'string' || !obj) {
      return defaultValue;
    }
    const value = path.split('.').reduce((o, k) => (o && o[k] !== undefined && o[k] !== null) ? o[k] : undefined, obj);
    return (value === undefined) ? defaultValue : value;
  } catch (e) {
    console.error("Error in getNested:", e, "Path:", path, "Object Type:", typeof obj);
    return defaultValue;
  }
};

const DeactivationModal = ({ isOpen, onClose, onDeactivateApiCall, creditLineName, selectedCreditLineFromParent, isDeactivating }) => {
  const [deactivationReason, setDeactivationReason] = useState('');

  if (!isOpen) return null;
  const handleDeactivateClick = () => {
    onDeactivateApiCall(selectedCreditLineFromParent, deactivationReason);
  };

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Deactivation</h3>
        <p className="text-sm text-gray-500 mb-2">
          Are you sure you want to deactivate the credit line for <span className="font-medium">{creditLineName}</span>? This will change the status to 'SUSPENDED'.
        </p>
        {/* ADD THIS TEXTAREA */}
        <div className="mb-4">
          <label htmlFor="deactivationReason" className="block text-sm font-medium text-gray-700">Reason for Deactivation:</label>
          <textarea
            id="deactivationReason"
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-red-500 focus:ring-red-500 sm:text-sm"
            rows={3}
            value={deactivationReason}
            onChange={(e) => setDeactivationReason(e.target.value)}
            placeholder="Please provide a reason for deactivation."
          />
        </div>
        <div className="flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            onClick={handleDeactivateClick}
            className={`px-4 py-2 text-sm font-medium text-white ${isDeactivating ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700'
              } border border-transparent rounded-md`}
            disabled={isDeactivating}
          >
            {isDeactivating ? (
              <svg className="animate-spin ml-1 mr-1 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              'Deactivate'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
// --- Reusable Helper Functions --- (Keep getNested)
// --- Imports --- (Keep React, useState, Disclosure, Icons etc.)

// PASTE THIS NEW FilterSection COMPONENT DEFINITION
const FilterSection = ({ filters, setFilters, resetFilters }) => {
  // Common input styling
  const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  const numberInputClass = `${inputBaseClass} px-2 py-1`;
  const dateInputClass = `${inputBaseClass} px-2 py-1`;
  const textInputClass = `${inputBaseClass} px-3 py-1.5`;

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prevFilters => ({
      ...prevFilters,
      [name]: value,
    }));
  };

  // Calculate the number of active filters
  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(value => value !== '' && value !== null && value !== undefined).length;
  }, [filters]);

  const handleReset = (event) => {
    event.stopPropagation(); // Prevent the disclosure from toggling
    resetFilters();
  };

  return (
    // Add margin below the whole filter section
    <div className="mb-6">
      <Disclosure as="div" className="border border-gray-200 rounded-lg shadow-sm bg-white">
        {({ open }) => (
          <>
            {/* The single Toggable Header Bar - Styled to match example */}
            <div className="flow-root">
              <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring focus-visible:ring-indigo-500 focus-visible:ring-opacity-75">
                {/* Left side: Icon, Text, Count */}
                <span className="flex items-center">
                  <FunnelIcon className="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
                  Filters
                  {activeFilterCount > 0 && (
                    <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-xs font-medium text-gray-800">
                      {activeFilterCount}
                    </span>
                  )}
                </span>

                {/* Right side: Clear button OR Expand/Collapse Icon - Example implicitly uses the whole bar click */}
                <span className="ml-6 flex items-center">
                  {/* Optional: Show Chevron only if preferred over whole bar click */}
                  {/* <ChevronUpIcon
                        className={`${open ? 'rotate-180 transform' : ''} h-5 w-5 text-gray-500 transition-transform duration-150 ease-in-out`}
                    /> */}
                </span>
              </Disclosure.Button>
            </div>

            {/* Separator Line for the Panel */}
            {open && <div className="border-t border-gray-200"></div>}

            {/* The Panel containing all filter inputs */}
            <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
              {/* Top row with search and maybe clear button */}
              <div className="mb-4 flex items-start justify-between">
                <div className="flex-1 mr-4"> {/* Search takes most space */}
                  <label htmlFor="searchTerm" className="sr-only">Search (Name)</label>
                  <input
                    type="text"
                    name="searchTerm"
                    id="searchTerm"
                    value={filters.searchTerm}
                    onChange={handleFilterChange}
                    className={textInputClass}
                    placeholder="Search Borrower or Business..."
                  />
                </div>
                {/* Clear button moved next to search */}
                <button
                  type="button"
                  onClick={handleReset} // Use the wrapper function
                  className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                >
                  <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" aria-hidden="true" />
                  Clear all
                </button>
              </div>

              {/* Grid for the rest of the filters */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">

                {/* Offer Limit Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Offer Limit (QAR)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minLimit" value={filters.minLimit} onChange={handleFilterChange} placeholder="Min" className={numberInputClass} />
                    <input type="number" name="maxLimit" value={filters.maxLimit} onChange={handleFilterChange} placeholder="Max" className={numberInputClass} />
                  </div>
                </div>

                {/* Interest Rate Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Service Fee (%)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minRate" value={filters.minRate} onChange={handleFilterChange} placeholder="Min" step="0.01" className={numberInputClass} />
                    <input type="number" name="maxRate" value={filters.maxRate} onChange={handleFilterChange} placeholder="Max" step="0.01" className={numberInputClass} />
                  </div>
                </div>

                {/* Tenure Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tenure (Days)</label>
                  <div className="flex space-x-2">
                    <input type="number" name="minTenure" value={filters.minTenure} onChange={handleFilterChange} placeholder="Min" step="1" className={numberInputClass} />
                    <input type="number" name="maxTenure" value={filters.maxTenure} onChange={handleFilterChange} placeholder="Max" step="1" className={numberInputClass} />
                  </div>
                </div>

                {/* Date Range */}
                <div className="sm:col-span-2 md:col-span-1"> {/* Adjust span as needed */}
                  <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated Date</label>
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:items-center sm:space-x-2">
                    <input type="date" name="startDate" value={filters.startDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} />
                    <span className="text-gray-500 text-center hidden sm:inline">to</span>
                    <input type="date" name="endDate" value={filters.endDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} />
                  </div>
                </div>
              </div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>
      {/* REMOVED ActiveFilterTags component - it's not in the target UI example */}
    </div>
  );
};
// END OF NEW FilterSection COMPONENT


// REMOVE the ActiveFilterTags component definition entirely from your file
// const ActiveFilterTags = ({ filters, setFilters }) => { ... }; // DELETE THIS


// --- formatDate / getNested --- (Keep as is)

// --- CreditLineActivationPage ---
// REMEMBER: You still need the changes inside CreditLineActivationPage mentioned before:
// 1. REMOVE the `showFilters` state variable.
// 2. REMOVE the `showFilters` and `setShowFilters` props passed to `<FilterSection>`.
// 3. The `<FilterSection>` call should just be:
//    <FilterSection filters={filters} setFilters={setFilters} resetFilters={resetFilters} />
//    (The margin `mb-6` is now handled *inside* the FilterSection component's wrapper div).

// Component to display applied filters as tags
// PASTE THIS NEW ActiveFilterTags COMPONENT DEFINITION
// const ActiveFilterTags = ({ filters, setFilters }) => {
//   // Check if any filter is active
//   const hasActiveFilters = Object.entries(filters).some(([key, value]) => {
//     if (typeof value === 'string') return value.trim() !== '';
//     return value !== '';
//   });

//   if (!hasActiveFilters) return null;

//   // Function to remove a single filter
//   const removeFilter = (filterKey) => {
//     setFilters(prev => ({ ...prev, [filterKey]: '' }));
//   };

//   return (
//     <div className="bg-gray-50 py-3 px-4 sm:px-6 lg:px-8 border-t border-gray-200 rounded-b-lg"> {/* Adjusted styling slightly */}
//       <div className="flex flex-wrap items-center gap-2">
//         <span className="text-sm font-medium text-gray-700">Active filters:</span>

//         {filters.searchTerm && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200"> {/* Adjusted tag style */}
//             Search: {filters.searchTerm}
//             <button
//               onClick={() => removeFilter('searchTerm')}
//               className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none"
//             >
//               <span className="sr-only">Remove filter</span>
//               <XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}

//         {/* Repeat similar span structure for all other filters... */}
//         {/* Example for minLimit */}
//         {filters.minLimit && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Limit: {filters.minLimit}
//             <button onClick={() => removeFilter('minLimit')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span>
//               <XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {/* ... and maxLimit, minRate, maxRate, minTenure, maxTenure, startDate, endDate */}
//         {filters.maxLimit && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Limit: {filters.maxLimit}
//             <button onClick={() => removeFilter('maxLimit')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.minRate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Rate: {filters.minRate}%
//             <button onClick={() => removeFilter('minRate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.maxRate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Rate: {filters.maxRate}%
//             <button onClick={() => removeFilter('maxRate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.minTenure && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Min Tenure: {filters.minTenure} days
//             <button onClick={() => removeFilter('minTenure')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.maxTenure && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             Max Tenure: {filters.maxTenure} days
//             <button onClick={() => removeFilter('maxTenure')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.startDate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             From: {filters.startDate}
//             <button onClick={() => removeFilter('startDate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}
//         {filters.endDate && (
//           <span className="inline-flex items-center rounded-full bg-white px-2.5 py-1 text-xs font-medium text-gray-700 ring-1 ring-inset ring-gray-200">
//             To: {filters.endDate}
//             <button onClick={() => removeFilter('endDate')} className="-mr-1 ml-1.5 inline-flex flex-shrink-0 text-gray-400 hover:text-gray-500 focus:outline-none">
//               <span className="sr-only">Remove filter</span><XMarkIcon className="h-3 w-3" />
//             </button>
//           </span>
//         )}

//       </div>
//     </div>
//   );
// };
// END OF NEW ActiveFilterTags COMPONENT

const formatDate = (dateString) => {
  // (Keep the existing formatDate function)
  if (!dateString || dateString === 'N/A' || dateString === undefined) return 'N/A';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime()) || date.getFullYear() <= 1970) return 'N/A';
    return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return 'Invalid Date';
  }
};
// --- End Helper Functions ---

// =========================================================================
//  MAIN CREDIT LINE ACTIVATION PAGE COMPONENT
// =========================================================================
export default function CreditLineActivationPage() {
  // --- State Variables ---
  const [allActivations, setAllActivations] = useState([]);
  const [pendingActivations, setPendingActivations] = useState([]);
  const [activeActivations, setActiveActivations] = useState([]);

  const [loading, setLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [activatingId, setActivatingId] = useState(null);
  const [deactivatingId, setDeactivatingId] = useState(null);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [selectedCreditLine, setSelectedCreditLine] = useState(null);
  const [isDeactivating, setIsDeactivating] = useState(false); // ADD THIS
  const [isExportingExcel, setIsExportingExcel] = useState(false); // <-- ADD THIS

  const openDeactivateModal = (creditLine) => {
    setSelectedCreditLine(creditLine);
    setShowDeactivateModal(true);
  };


  // --- Data Filtering Logic for Tabs ---
  const filterActivationTabs = useCallback((activations) => {
    setPendingActivations(activations.filter(a => a.creditLineStatus === 'APPROVED' || a.creditLineStatus === 'SUSPENDED'));
    setActiveActivations(activations.filter(a => a.creditLineStatus === 'ACTIVE'));
  }, []);
  // --- State for Filters ---
  const [filters, setFilters] = useState({
    searchTerm: '',
    minLimit: '',
    maxLimit: '',
    minRate: '',
    maxRate: '',
    minTenure: '',
    maxTenure: '',
    startDate: '',
    endDate: '',
  });
  // const [showFilters, setShowFilters] = useState(false);

  const resetFilters = React.useCallback(() => { // Assuming React is imported
    setFilters({
      searchTerm: '', minLimit: '', maxLimit: '', minRate: '', maxRate: '',
      minTenure: '', maxTenure: '', startDate: '', endDate: '',
    });
  }, []);



  // --- Filter Input Change Handler ---
  // const handleFilterChange = (event) => {
  //   const { name, value } = event.target;
  //   setFilters(prevFilters => ({
  //     ...prevFilters,
  //     [name]: value,
  //   }));
  // };

  // --- Data Fetching Function ---
  const fetchData = useCallback(async () => {
    console.log("fetchData called for Activation Page");
    setLoading(true);
    setFetchError(null);
    // Don't clear individual tab states here, filterActivationTabs will do it

    try {
      const loggedInLenderId = localStorage.getItem('userId');
      if (!loggedInLenderId) throw new Error("Lender ID not found.");

      const [clResult, userResult, offerResult] = await Promise.allSettled([
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`),
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`),
        axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers?offerType=creditLineOffer`)
      ]);

      // --- Process Responses ---
      let fetchedCreditLines = [];
      if (clResult.status === 'fulfilled' && clResult.value.data) {
        fetchedCreditLines = Array.isArray(clResult.value.data) ? clResult.value.data : (clResult.value.data.creditLines || []);
      } else { console.error("Failed to fetch credit lines", clResult.reason || clResult.value?.status); }

      let fetchedUsersMap = new Map();
      if (userResult.status === 'fulfilled' && userResult.value.data?.success && Array.isArray(userResult.value.data.kycs)) {
        fetchedUsersMap = new Map(userResult.value.data.kycs.map(user => [user._id, user]));
      } else { console.error("Failed to fetch users", userResult.reason || userResult.value?.status); }

      let fetchedOffers = [];
      if (offerResult.status === 'fulfilled' && offerResult.value.data?.success && Array.isArray(offerResult.value.data.offers)) {
        fetchedOffers = offerResult.value.data.offers;
      } else { console.warn("Failed to fetch offers", offerResult.reason || offerResult.value?.status); }

      // --- Combine and Filter Data ---
      const relevantActivations = [];
      const offersMap = new Map(fetchedOffers.map(o => [`${o.merchantId}-${o.lenderId}`, o]));

      fetchedCreditLines.forEach(cl => {
        const clStatus = String(cl.creditLineStatus || '').toUpperCase();
        const isRelevantStatus = clStatus === 'APPROVED' || clStatus === 'ACTIVE' || clStatus === 'SUSPENDED';
        const isCorrectLender = String(cl.lenderId) === loggedInLenderId;
        const isOfferAccepted = cl.offerAccepted === true;

        if (isRelevantStatus && isCorrectLender && isOfferAccepted) {
          const userDetails = fetchedUsersMap.get(cl.userId);
          const offerDetails = offersMap.get(`${cl.userId}-${cl.lenderId}`);

          if (userDetails && offerDetails) {
            relevantActivations.push({ ...cl, userDetails, offerDetails });
          } else {
            console.warn(`Skipping CL ${cl._id} (Status: ${clStatus}, Lender: ${cl.lenderId}, UserID: ${cl.userId}): Missing -> ${!userDetails ? 'User Details ' : ''}${!offerDetails ? 'Offer Details' : ''}`);
            if (!userDetails) console.warn(` > User Map does not have key: ${cl.userId}`);
            if (!offerDetails) console.warn(` > Offer Map does not have key: ${cl.userId}-${cl.lenderId}`);
          }
        }
      });

      console.log(`Found ${relevantActivations.length} relevant activations for lender ${loggedInLenderId}`);
      setAllActivations(relevantActivations); // Set the master list for this page
      filterActivationTabs(relevantActivations); // Filter into tabs

    } catch (error) {
      console.error('Error during fetchData on Activation Page:', error);
      setFetchError(error.message || "An unknown error occurred.");
      setAllActivations([]);
      filterActivationTabs([]);
    } finally {
      setLoading(false);
    }
  }, [filterActivationTabs]); // Dependency

  // --- Reusable Helper Functions ---
  // const getNested = (obj, path, defaultValue = undefined) => { ... }; // Already in your code

  // const formatDate = (dateString) => { ... }; // Already in your code. We'll use this for export.

  // Helper function for calculating age (if not already globally available)
  // This seems to be missing from your latest CreditLineActivationPage snippet but was used in CreditAssessmentPage
  const calculateAge = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const startDate = new Date(dateString); // parseISO might be more robust if date strings are strictly ISO
      if (isNaN(startDate.getTime())) return 'Invalid Date';

      const now = new Date();
      const diffMs = now - startDate;
      const diffSeconds = Math.floor(diffMs / 1000);
      const diffMinutes = Math.floor(diffSeconds / 60);
      const diffHours = Math.floor(diffMinutes / 60);
      const diffDays = Math.floor(diffHours / 24);

      if (diffDays > 0) return `<span class="math-inline">\{diffDays\} day</span>{diffDays > 1 ? 's' : ''} ago`;
      if (diffHours > 0) return `<span class="math-inline">\{diffHours\} hour</span>{diffHours > 1 ? 's' : ''} ago`;
      if (diffMinutes > 0) return `<span class="math-inline">\{diffMinutes\} minute</span>{diffMinutes > 1 ? 's' : ''} ago`;
      return `Just now`;
    } catch (e) {
      console.error("Error calculating age for export:", dateString, e);
      return 'Error';
    }
  };

  const calculateCurrentFeeForExport = (type, value, creditLimit) => {
    const feeVal = Number(value) || 0;
    const limitVal = Number(creditLimit) || 0;
    if (type === 'flat') return feeVal;
    if (type === 'percentage' && limitVal > 0) return (feeVal / 100) * limitVal;
    return 0;
  };


  // Helper to add document fields to a row object
  const addDocumentFieldsToRow = (rowData, docObject, prefix, getNestedFn, formatDateFn) => {
    if (!docObject || typeof docObject !== 'object') {
      rowData[`${prefix} - File Name`] = 'N/A';
      rowData[`${prefix} - Signed URL`] = 'N/A';
      rowData[`${prefix} - Uploaded On`] = 'N/A';
      rowData[`${prefix} - Status`] = 'N/A';
      rowData[`${prefix} - Notes`] = '';
      rowData[`${prefix} - Verification Date`] = 'N/A';
      rowData[`${prefix} - MIME Type`] = 'N/A';
      return;
    }
    const filePath = getNestedFn(docObject, 'filePath', '');
    rowData[`${prefix} - File Name`] = filePath ? (filePath.split('/').pop() || 'document') : 'N/A';
    rowData[`${prefix} - Signed URL`] = getNestedFn(docObject, 'signedUrl', 'N/A');
    rowData[`${prefix} - Uploaded On`] = formatDateFn(getNestedFn(docObject, 'uploadedOn'));
    rowData[`${prefix} - Status`] = getNestedFn(docObject, 'verificationStatus', 'N/A');
    rowData[`${prefix} - Notes`] = getNestedFn(docObject, 'verificationNotes', '');
    rowData[`${prefix} - Verification Date`] = formatDateFn(getNestedFn(docObject, 'verifiedOrRejectedOn'));
    rowData[`${prefix} - MIME Type`] = getNestedFn(docObject, 'mimeType', 'N/A');
  };

  const handleDeactivateApiCall = useCallback(async (creditLine, reason) => {
    console.log(creditLine);
    if (!creditLine?._id || !creditLine?.userId) {
      alert("Error: Missing necessary information to deactivate.");
      return;
    }
    setIsDeactivating(true); // ADD THIS
    setDeactivatingId(creditLine._id);

    const payload = {
      userId: creditLine.userId,
      creditLineData: {
        _id: creditLine._id,
        creditLineStatus: 'SUSPENDED',
      }
    };
    console.log("Deactivation Payload (Status Update):", JSON.stringify(payload, null, 2));

    try {
      const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/creditLineCreateOrUpdate`, payload);

      if (!response.data?.success && !response.data?.creditLine) {
        throw new Error(response.data?.message || "Backend reported failure during deactivation status update.");
      }

      console.log("Deactivation status updated successfully for:", creditLine._id);

      // Call the second API here
      try {
        const secondApiResponse = await axios.post(
          `${config.apiUrl}/ops/invoiceFinancing/creditLine/${creditLine._id}/addReview`, // Use the route you provided
          {
            status: 'SUSPENDED', // Or 'REJECTED' depending on your logic
            notes: reason, // Send the deactivation reason as notes
            reviewedBy: localStorage.getItem('userId'), // Assuming you want to log who deactivated
            rejectionReasons: reason ? [reason] : [], // Or format as needed
          }
        );

        if (!secondApiResponse.data?.success) {
          console.error("Error calling second deactivation API:", secondApiResponse.data?.message);
          alert(`Deactivation successful (status updated), but error in logging reason: ${secondApiResponse.data?.message || 'Unknown error'}`);
        } else {
          console.log("Second deactivation API call successful:", secondApiResponse.data);
          alert("Credit line deactivated successfully!");
        }
        await fetchData(); // Refresh the list
      } catch (secondApiError) {
        console.error("Error calling the second deactivation API:", secondApiError.response?.data || secondApiError.message);
        alert(`Deactivation successful (status updated), but error in logging reason: ${getNested(secondApiError, 'response.data.message', secondApiError.message) || 'Unknown error'}`);
        await fetchData(); // Refresh the list even if second API fails
      }

    } catch (error) {
      console.error('Error deactivating credit line (status update):', error.response?.data || error.message);
      alert(`Error deactivating credit line: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
    } finally {
      setIsDeactivating(false); // ADD THIS
      setDeactivatingId(null);
      setShowDeactivateModal(false);
    }
  }, [fetchData]);

  // --- Initial Data Fetch ---
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // --- Handle Activate Button Click ---
  const handleActivate = useCallback(async (creditLine) => {
    // (Keep the existing handleActivate function - it's correct)
    if (!creditLine?._id || !creditLine?.userId) {
      alert("Error: Missing necessary information to activate.");
      return;
    }
    if (creditLine.creditLineStatus !== 'APPROVED' && creditLine.creditLineStatus !== 'SUSPENDED') {
      alert("Credit line is not in approved or deactivated status.");
      return;
    }

    setActivatingId(creditLine._id);

    const payload = {
      userId: creditLine.userId,
      creditLineData: {
        _id: creditLine._id,
        creditLineStatus: 'ACTIVE'
      }
    };
    console.log("Activation Payload:", JSON.stringify(payload, null, 2));

    try {
      const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/creditLineCreateOrUpdate`, payload);

      if (!response.data?.success && !response.data?.creditLine) {
        throw new Error(response.data?.message || "Backend reported failure during activation.");
      }

      console.log("Activation successful for:", creditLine._id);
      alert("Credit line activated successfully!");
      await fetchData(); // Refresh the entire list

    } catch (error) {
      console.error('Error activating credit line:', error.response?.data || error.message);
      alert(`Error activating credit line: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
    } finally {
      setActivatingId(null);
    }
  }, [fetchData]);

  const currentTableData = useMemo(() => {
    let sourceData;
    if (activeTab === 'all') {
      sourceData = allActivations;
    } else if (activeTab === 'pending') {
      sourceData = pendingActivations;
    } else { // 'activated'
      sourceData = activeActivations;
    }

    if (!sourceData || sourceData.length === 0) return [];

    const { searchTerm, minLimit, maxLimit, minRate, maxRate, minTenure, maxTenure, startDate, endDate } = filters;

    // Prepare filter values once
    const lowerSearchTerm = searchTerm.toLowerCase();
    const numMinLimit = minLimit === '' ? -Infinity : parseFloat(minLimit);
    const numMaxLimit = maxLimit === '' ? Infinity : parseFloat(maxLimit);
    const numMinRate = minRate === '' ? -Infinity : parseFloat(minRate);
    const numMaxRate = maxRate === '' ? Infinity : parseFloat(maxRate);
    const numMinTenure = minTenure === '' ? -Infinity : parseInt(minTenure, 10);
    const numMaxTenure = maxTenure === '' ? Infinity : parseInt(maxTenure, 10);
    // Get timestamp for start/end of day for date comparison
    const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
    const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;

    // Filter the source data
    return sourceData.filter(cl => {
      const borrowerName = `${getNested(cl, 'userDetails.firstName', '')} ${getNested(cl, 'userDetails.lastName', '')}`.toLowerCase();
      const businessName = getNested(cl, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
      const limit = parseFloat(getNested(cl, 'offerDetails.creditLimit', NaN)); // Default to NaN if missing
      const rate = parseFloat(getNested(cl, 'offerDetails.interestRate', NaN));
      const tenure = parseInt(getNested(cl, 'offerDetails.tenureDays', NaN), 10);
      const updatedAtTs = cl.updatedAt ? new Date(cl.updatedAt).getTime() : null;

      // Apply Filters - return false if ANY filter fails
      if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) {
        return false;
      }
      if (!isNaN(numMinLimit) && (isNaN(limit) || limit < numMinLimit)) { return false; }
      if (!isNaN(numMaxLimit) && (isNaN(limit) || limit > numMaxLimit)) { return false; }
      if (!isNaN(numMinRate) && (isNaN(rate) || rate < numMinRate)) { return false; }
      if (!isNaN(numMaxRate) && (isNaN(rate) || rate > numMaxRate)) { return false; }
      if (!isNaN(numMinTenure) && (isNaN(tenure) || tenure < numMinTenure)) { return false; }
      if (!isNaN(numMaxTenure) && (isNaN(tenure) || tenure > numMaxTenure)) { return false; }
      if (tsStartDate && (!updatedAtTs || updatedAtTs < tsStartDate)) { return false; }
      if (tsEndDate && (!updatedAtTs || updatedAtTs > tsEndDate)) { return false; }

      return true; // Passed all filters
    });
  }, [filters, activeTab, allActivations, pendingActivations, activeActivations]);

  // --- Function to Render Table Rows ---
  const renderTableRows = (data) => {
    // (Keep the existing renderTableRows function)
    if (!data || data.length === 0) {
      return (
        <tr>
          <td colSpan="7" className="text-center py-10 px-4 text-sm text-gray-500 italic">
            No credit lines found matching the current filters in this section.
          </td>
        </tr>
      );
    }

    return data.map((cl) => {
      const offerDetails = cl.offerDetails || {}; // Use the embedded offer details
      // Correctly get the signedUrl for the contract

      // const contractUrl = getNested(offerDetails, 'invoiceContract.signedUrl');
      console.log(cl.offerDetails?.facilityContract?.signedUrl, "url here");
      const contractUrl = cl.offerDetails?.facilityContract?.signedUrl ?? '/creditLineContract.pdf';
      const isActivatingThis = activatingId === cl._id;

      return (
        <tr key={cl._id} className="hover:bg-gray-50">
          {/* Business Name */}
          <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
            {getNested(cl, 'userDetails.kyc.businessDetails.businessName', 'N/A')}
          </td>
          {/* Borrower Name */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
            <div className="font-medium">{[getNested(cl, 'userDetails.firstName'), getNested(cl, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
            <div className="text-xs text-gray-500">{getNested(cl, 'userDetails.email', 'N/A')}</div>
          </td>
          {/* Borrower Phone */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
            {getNested(cl, 'userDetails.mobileNo', 'N/A')}
          </td>
          {/* Last Updated Date */}
          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
            {formatDate(cl.updatedAt)}
          </td>
          {/* Offer Details */}
          <td className="px-4 py-3 whitespace-normal text-xs text-gray-700 align-top">
            <div className="flex flex-col space-y-0.5">
              <span>Limit: <span className="font-medium">{getNested(offerDetails, 'creditLimit', 'N/A')?.toLocaleString()} {cl.currency || 'QAR'}</span></span>
              <span>Tenure: <span className="font-medium">{getNested(offerDetails, 'tenureDays', 'N/A')} Days</span></span>
              <span>Service Fee: <span className="font-medium">{getNested(offerDetails, 'interestRate', 'N/A')}%</span></span>
            </div>
          </td>
          {/* Contract */}
          <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
            {contractUrl ? (
              <a
                href={contractUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-[#1B9D51] hover:text-green-600 text-xs flex items-center justify-center space-x-1"
                title="View CAM Report"
              >
                <span>View</span>
                <ArrowTopRightOnSquareIcon className="w-4 h-4 text-[#1B9D51] hover:text-green-600" />
              </a>
            ) : (
              <span className="text-gray-400 italic text-xs">N/A</span>
            )}
          </td>
          {/* Actions */}
          <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
            {(cl.creditLineStatus === 'APPROVED' || cl.creditLineStatus === 'SUSPENDED') && (
              <button
                onClick={() => handleActivate(cl)}
                disabled={isActivatingThis}
                className={`inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white
                ${isActivatingThis ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#50cd80] hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'}`}
              >
                {isActivatingThis ? (
                  <>
                    <svg className="animate-spin -ml-0.5 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Activating...
                  </>
                ) : 'Activate'}
              </button>
            )}
            {cl.creditLineStatus === 'ACTIVE' && (
              <button
                onClick={() => openDeactivateModal(cl)}
                disabled={deactivatingId === cl._id}
                className={`inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white
                                    ${deactivatingId === cl._id ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'}`}
              >
                {deactivatingId === cl._id ? (
                  <>
                    <svg className="animate-spin -ml-0.5 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Deactivating...
                  </>
                ) : 'Deactivate'}
              </button>
            )}
          </td>
        </tr>
      );
    });
  };

  // --- Common Input Styling ---
  // const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  // const numberInputClass = `${inputBaseClass} px-2 py-1`;
  // const dateInputClass = `${inputBaseClass} px-2 py-1`;
  // const textInputClass = `${inputBaseClass} px-3 py-1.5`;

  // --- MAIN RENDER ---

  // Place this function inside your CreditLineActivationPage component
  // Or make it a standalone utility if preferred, passing all helpers as arguments.

  const flattenCreditLineActivationData = (activation, maxShareholders, maxDirectors, maxBuyers, getNestedFn, formatDateFn, calculateAgeFn, calculateCurrentFeeFnForExport, addDocFn) => {
    const rowData = {};
    const cl = activation; // The 'activation' object is the enriched credit line object
    const user = cl.userDetails || {};
    const kyc = user.kyc || {};
    const businessDetails = kyc.businessDetails || {};
    const incomeDetails = kyc.incomeDetails || {};
    const employmentDetails = kyc.employmentDetails || {};
    const offerDetails = cl.offerDetails || {};

    // --- I. Credit Line Activation Core Info (from table + cl object) ---
    rowData['Credit Line ID'] = getNestedFn(cl, '_id', 'N/A');
    rowData['CL Status (Raw)'] = getNestedFn(cl, 'creditLineStatus', 'N/A');
    // You might want a display status if `StatusBadge` uses a mapping like `STATUS_DISPLAY_NAMES`
    // For now, using raw status. You can add `STATUS_DISPLAY_NAMES[getNestedFn(cl, 'creditLineStatus')]`
    rowData['CL Offer Accepted'] = getNestedFn(cl, 'offerAccepted', 'N/A');
    rowData['CL User ID (Borrower)'] = getNestedFn(cl, 'userId', 'N/A');
    rowData['CL Lender ID'] = getNestedFn(cl, 'lenderId', 'N/A');
    rowData['CL Created At'] = formatDateFn(getNestedFn(cl, 'createdAt'));
    rowData['CL Updated At (Last Updated on Table)'] = formatDateFn(getNestedFn(cl, 'updatedAt'));
    rowData['CL Last Review Date'] = formatDateFn(getNestedFn(cl, 'lastReviewDate'));
    rowData['CL Next Review Date'] = formatDateFn(getNestedFn(cl, 'nextReviewDate'));

    // --- II. User/Borrower Basic Info (from cl.userDetails) ---
    rowData['Borrower Business Name'] = getNestedFn(businessDetails, 'businessName', 'N/A');
    rowData['Borrower First Name'] = getNestedFn(user, 'firstName', 'N/A');
    rowData['Borrower Last Name'] = getNestedFn(user, 'lastName', 'N/A');
    rowData['Borrower Email'] = getNestedFn(user, 'email', 'N/A');
    rowData['Borrower Mobile No'] = getNestedFn(user, 'mobileNo', 'N/A');
    rowData['Borrower Account Active'] = getNestedFn(user, 'isActive', 'N/A');

    // --- III. Offer Details (from cl.offerDetails - as per table) ---
    rowData['Offer Limit (QAR)'] = getNestedFn(offerDetails, 'creditLimit', 'N/A');
    rowData['Offer Tenure (Days)'] = getNestedFn(offerDetails, 'tenureDays', 'N/A');
    rowData['Offer Service Fee %'] = getNestedFn(offerDetails, 'interestRate', 'N/A');
    rowData['Offer ID'] = getNestedFn(offerDetails, '_id', 'N/A');
    rowData['Offer Status'] = getNestedFn(offerDetails, 'status', 'N/A');
    rowData['Offer Processing Fee Type'] = getNestedFn(offerDetails, 'processingFee.type', 'N/A');
    rowData['Offer Processing Fee Value'] = getNestedFn(offerDetails, 'processingFee.value', 'N/A');
    if (calculateCurrentFeeFnForExport && typeof calculateCurrentFeeFnForExport === 'function') {
      const calculatedFee = calculateCurrentFeeFnForExport(getNestedFn(offerDetails, 'processingFee.type'), getNestedFn(offerDetails, 'processingFee.value'), getNestedFn(offerDetails, 'creditLimit'));
      rowData['Offer Calculated Processing Fee (QAR)'] = isNaN(calculatedFee) ? 'N/A' : calculatedFee.toFixed(2);
    } else {
      rowData['Offer Calculated Processing Fee (QAR)'] = 'N/A';
    }
    rowData['Offer Risk Profile'] = getNestedFn(offerDetails, 'riskProfile', 'N/A');
    rowData['Offer Currency'] = getNestedFn(offerDetails, 'currency', 'QAR');
    rowData['Offer Internal Notes'] = getNestedFn(offerDetails, 'notes', '');
    rowData['Offer Created At'] = formatDateFn(getNestedFn(offerDetails, 'createdAt'));
    rowData['Offer Updated At'] = formatDateFn(getNestedFn(offerDetails, 'updatedAt'));
    rowData['Offer Expiry Date'] = formatDateFn(getNestedFn(offerDetails, 'expiryDate'));
    rowData['Offer Accepted Date'] = formatDateFn(getNestedFn(offerDetails, 'acceptedDate'));
    rowData['Offer Contract Accepted Date'] = formatDateFn(getNestedFn(offerDetails, 'loanContractAcceptedDate'));
    addDocFn(rowData, getNestedFn(offerDetails, 'facilityContract'), 'Offer Facility Contract', getNestedFn, formatDateFn);


    // --- IV. Detailed KYC Information from userDetails (Mirroring KycDetailsViewModal) ---
    rowData['User Overall KYC Status'] = getNestedFn(kyc, 'verificationStatus', 'N/A');
    rowData['User KYC Verified On'] = formatDateFn(getNestedFn(kyc, 'verifiedOn'));
    rowData['User KYC Overall Notes'] = getNestedFn(kyc, 'verificationNotes', '');
    addDocFn(rowData, getNestedFn(user, 'camFile'), 'User CAM Report', getNestedFn, formatDateFn);

    // KYC User & KYC Details Tab Items
    rowData['User KYC Personal Addr L1'] = getNestedFn(kyc, 'addressLine1', 'N/A');
    rowData['User KYC Personal Addr L2'] = getNestedFn(kyc, 'addressLine2', 'N/A');
    rowData['User KYC Personal City'] = getNestedFn(kyc, 'city', 'N/A');
    rowData['User KYC Personal State'] = getNestedFn(kyc, 'state', 'N/A');
    rowData['User KYC Personal Postal Code'] = getNestedFn(kyc, 'postalCode', 'N/A');
    rowData['User KYC Personal Country'] = getNestedFn(kyc, 'country', 'N/A');
    rowData['User KYC Bank Acc No'] = getNestedFn(incomeDetails, 'accountNumber', 'N/A');
    rowData['User KYC Bank IBAN'] = getNestedFn(incomeDetails, 'ifscCode', 'N/A'); // IBAN in modal
    rowData['User KYC Employer'] = getNestedFn(employmentDetails, 'employerName', 'N/A');
    rowData['User KYC Position'] = getNestedFn(employmentDetails, 'position', 'N/A');
    addDocFn(rowData, getNestedFn(kyc, 'qatariId'), 'User Personal QID', getNestedFn, formatDateFn);
    addDocFn(rowData, getNestedFn(kyc, 'passport'), 'User Personal Passport', getNestedFn, formatDateFn);
    addDocFn(rowData, getNestedFn(kyc, 'utilityBill'), 'User Personal Utility Bill', getNestedFn, formatDateFn);
    addDocFn(rowData, getNestedFn(employmentDetails, 'employmentLetter'), 'User Employment Letter', getNestedFn, formatDateFn);
    addDocFn(rowData, getNestedFn(incomeDetails, 'proofOfIncome'), 'User Proof of Income', getNestedFn, formatDateFn);

    // KYC Business Details Tab Items
    rowData['User Business Name (from KYC)'] = getNestedFn(businessDetails, 'businessName', 'N/A');
    rowData['User Legal Entity Name (KYC)'] = getNestedFn(businessDetails, 'legalEntityName', 'N/A');
    rowData['User Establishment Name (KYC)'] = getNestedFn(businessDetails, 'establishmentName', 'N/A');
    rowData['User Legal Form (KYC)'] = getNestedFn(businessDetails, 'legalForm', 'N/A');
    rowData['User Ownership Type (KYC)'] = getNestedFn(businessDetails, 'ownershipType', 'N/A');
    rowData['User Sector (KYC)'] = getNestedFn(businessDetails, 'sector', 'N/A');
    rowData['User Firm Nationality (KYC)'] = getNestedFn(businessDetails, 'firmNationality', 'N/A');
    rowData['User CR Number (KYC)'] = getNestedFn(businessDetails, 'crNumber', 'N/A');
    rowData['User CR Issue Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'crIssueDate'));
    rowData['User CR Expiry Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'crExpiryDate'));
    rowData['User TL Number (from User Root)'] = getNestedFn(user, 'licenseNumber', 'N/A');
    rowData['User TL Issue Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'tlIssueDate'));
    rowData['User TL Expiry Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'tlExpiryDate'));
    rowData['User Tax Reg No (TRN) (KYC)'] = getNestedFn(businessDetails, 'taxRegNo', 'N/A');
    rowData['User TIN Number (KYC)'] = getNestedFn(businessDetails, 'tinNumber', 'N/A');
    rowData['User Establishment ID (KYC)'] = getNestedFn(businessDetails, 'establishmentId', 'N/A');
    rowData['User Est. ID Issue Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'establishmentIdIssueDate'));
    rowData['User Est. ID Expiry Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'establishmentIdExpiryDate'));
    rowData['User Branch Count (KYC)'] = getNestedFn(businessDetails, 'branchCount', 'N/A');
    rowData['User Business Address L1 (KYC)'] = getNestedFn(businessDetails, 'businessAddressLine1', 'N/A');
    rowData['User Business Address L2 (KYC)'] = getNestedFn(businessDetails, 'businessAddressLine2', 'N/A');
    rowData['User Business City (KYC)'] = getNestedFn(businessDetails, 'businessCity', 'N/A');
    rowData['User Business Country (KYC)'] = getNestedFn(businessDetails, 'businessCountry', 'N/A');

    // KYC Business Documents (definitions from KycDetailsViewModal, apply to `user` object)
    const kycModalBusinessDocFields = [
      { key: 'commercialRegistration', label: 'CR' }, { key: 'tradeLicense', label: 'Trade License' },
      { key: 'taxCard', label: 'Tax Card' }, { key: 'establishmentCard', label: 'Establishment Card' },
      { key: 'memorandumOfAssociation', label: 'MOA' }, { key: 'articleOfAssociation', label: 'AOA' },
      { key: 'otherDocument', label: 'Other User Doc 1' }, { key: 'otherDocumentTwo', label: 'Other User Doc 2' },
      { key: 'otherDocument3', label: 'Other User Doc 3' }, { key: 'otherDocument4', label: 'Other User Doc 4' },
      { key: 'otherDocument5', label: 'Other User Doc 5' }, { key: 'otherDocument6', label: 'Other User Doc 6' },
      { key: 'otherDocument7', label: 'Other User Doc 7' }, { key: 'otherDocument8', label: 'Other User Doc 8' },
      { key: 'otherDocument9', label: 'Other User Doc 9' }, { key: 'otherDocument10', label: 'Other User Doc 10' },
    ];
    kycModalBusinessDocFields.forEach(field => {
      addDocFn(rowData, getNestedFn(user, field.key), `User KYC BusinessDoc - ${field.label}`, getNestedFn, formatDateFn);
    });

    // KYC Financial Documents
    const kycModalFinancialDocFields = [
      { key: 'bankStatement', label: 'Bank Statements' }, { key: 'auditedFinancialReport', label: 'Audited Report' },
      { key: 'commercialCreditReport', label: 'CCR' }, { key: 'cashFlowLedger', label: 'Cash Flow Ledger' },
    ];
    kycModalFinancialDocFields.forEach(field => {
      addDocFn(rowData, getNestedFn(user, field.key), `User KYC FinDoc - ${field.label}`, getNestedFn, formatDateFn);
    });

    // KYC Shareholders
    const shareholders = getNestedFn(user, 'shareholders', []) || [];
    for (let i = 0; i < maxShareholders; i++) {
      const sh = shareholders[i];
      const prefix = `User Shareholder ${i + 1}`;
      if (sh) {
        rowData[`${prefix} - First Name`] = getNestedFn(sh, 'firstName', 'N/A');
        rowData[`${prefix} - Last Name`] = getNestedFn(sh, 'lastName', 'N/A');
        rowData[`${prefix} - Middle Name`] = getNestedFn(sh, 'middleName', 'N/A');
        rowData[`${prefix} - Email`] = getNestedFn(sh, 'email', 'N/A');
        rowData[`${prefix} - KYC Status`] = getNestedFn(sh, 'kycVerificationStatus', 'N/A');
        rowData[`${prefix} - Addr Zone`] = getNestedFn(sh, 'address.zone', 'N/A');
        rowData[`${prefix} - Addr Street`] = getNestedFn(sh, 'address.streetNo', 'N/A');
        rowData[`${prefix} - Addr Building`] = getNestedFn(sh, 'address.buildingNo', 'N/A');
        rowData[`${prefix} - Addr Floor`] = getNestedFn(sh, 'address.floorNo', 'N/A');
        rowData[`${prefix} - Addr Unit`] = getNestedFn(sh, 'address.unitNo', 'N/A');
        rowData[`${prefix} - Added On`] = formatDateFn(getNestedFn(sh, 'addedOn'));
        rowData[`${prefix} - Modified On`] = formatDateFn(getNestedFn(sh, 'modifiedOn'));
        addDocFn(rowData, getNestedFn(sh, 'passport'), `${prefix} - Passport`, getNestedFn, formatDateFn);
        addDocFn(rowData, getNestedFn(sh, 'qid'), `${prefix} - QID`, getNestedFn, formatDateFn);
        addDocFn(rowData, getNestedFn(sh, 'proofOfAddress'), `${prefix} - Proof of Address`, getNestedFn, formatDateFn);
      } else {
        ['First Name', 'Last Name', 'Middle Name', 'Email', 'KYC Status', 'Addr Zone', 'Addr Street', 'Addr Building', 'Addr Floor', 'Addr Unit', 'Added On', 'Modified On'].forEach(f => rowData[`${prefix} - ${f}`] = '');
        ['Passport', 'QID', 'Proof of Address'].forEach(docType => {
          ['File Name', 'Signed URL', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'MIME Type'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = '');
        });
      }
    }

    // KYC Directors (Excluding Auth Signatories & Beneficial Owners as per prior instruction)
    const directors = getNestedFn(kyc, 'directors', []) || [];
    for (let i = 0; i < maxDirectors; i++) {
      const dir = directors[i];
      const prefix = `User Director ${i + 1}`;
      if (dir) {
        rowData[`${prefix} - Name`] = getNestedFn(dir, 'directorName', 'N/A');
        rowData[`${prefix} - Position`] = getNestedFn(dir, 'position', 'N/A');
        rowData[`${prefix} - Nationality`] = getNestedFn(dir, 'nationality', 'N/A');
        rowData[`${prefix} - DOB`] = formatDateFn(getNestedFn(dir, 'dateOfBirth'));
        rowData[`${prefix} - National ID`] = getNestedFn(dir, 'nationalId', 'N/A');
        rowData[`${prefix} - Address`] = getNestedFn(dir, 'directorAddress', 'N/A');
        addDocFn(rowData, getNestedFn(dir, 'idDocument'), `${prefix} - ID Document`, getNestedFn, formatDateFn);
      } else {
        ['Name', 'Position', 'Nationality', 'DOB', 'National ID', 'Address'].forEach(f => rowData[`${prefix} - ${f}`] = '');
        ['ID Document'].forEach(docType => { ['File Name', 'Signed URL', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'MIME Type'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = ''); });
      }
    }

    // KYC Buyers
    const buyers = getNestedFn(kyc, 'buyers', []) || [];
    for (let i = 0; i < maxBuyers; i++) {
      const buyer = buyers[i];
      const prefix = `User Top Buyer ${i + 1}`;
      if (buyer) {
        rowData[`${prefix} - Name`] = getNestedFn(buyer, 'buyerName', 'N/A');
        rowData[`${prefix} - Contact Person`] = getNestedFn(buyer, 'contactPerson', 'N/A');
        rowData[`${prefix} - Contact Phone`] = getNestedFn(buyer, 'contactPhone', 'N/A');
        rowData[`${prefix} - Contact Email`] = getNestedFn(buyer, 'contactEmail', 'N/A');
        rowData[`${prefix} - Reg No`] = getNestedFn(buyer, 'registrationNumber', 'N/A');
        addDocFn(rowData, getNestedFn(buyer, 'companyDocument'), `${prefix} - Company Doc`, getNestedFn, formatDateFn);
      } else {
        ['Name', 'Contact Person', 'Contact Phone', 'Contact Email', 'Reg No'].forEach(f => rowData[`${prefix} - ${f}`] = '');
        ['Company Doc'].forEach(docType => { ['File Name', 'Signed URL', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'MIME Type'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = ''); });
      }
    }

    return rowData;
  };

  // Place this function inside your CreditLineActivationPage component
  const handleExportActivations = async () => {
    const itemsToExport = currentTableData; // Exporting the currently filtered and tab-selected list

    if (!itemsToExport || itemsToExport.length === 0) {
      alert("No credit line activations in the current view to export.");
      return;
    }
    setIsExportingExcel(true);

    try {
      let maxShareholders = 0;
      let maxDirectors = 0;
      let maxBuyers = 0;

      itemsToExport.forEach(activation => {
        const user = activation.userDetails || {};
        const kyc = user.kyc || {};

        const shareholders = getNested(user, 'shareholders', []) || [];
        if (shareholders.length > maxShareholders) maxShareholders = shareholders.length;

        const directors = getNested(kyc, 'directors', []) || [];
        if (directors.length > maxDirectors) maxDirectors = directors.length;

        const buyers = getNested(kyc, 'buyers', []) || [];
        if (buyers.length > maxBuyers) maxBuyers = buyers.length;
      });

      if (maxShareholders === 0) maxShareholders = 1;
      if (maxDirectors === 0) maxDirectors = 1;
      if (maxBuyers === 0) maxBuyers = 1;

      const excelData = itemsToExport.map(activation =>
        flattenCreditLineActivationData(activation, maxShareholders, maxDirectors, maxBuyers, getNested, formatDate, calculateAge, calculateCurrentFeeForExport, addDocumentFieldsToRow)
      );

      if (excelData.length === 0) {
        alert("No data to export after processing.");
        setIsExportingExcel(false);
        return;
      }

      const worksheet = XLSX.utils.json_to_sheet(excelData);

      if (excelData.length > 0 && excelData[0]) {
        const headers = Object.keys(excelData[0]);
        const colWidths = headers.map(header => {
          const headerLength = header ? header.toString().length : 10;
          return { wch: headerLength + 2 }; // Padding of +2
        });
        worksheet['!cols'] = colWidths;
      }

      const workbook = XLSX.utils.book_new();
      const sheetName = activeTab.charAt(0).toUpperCase() + activeTab.slice(1) + "_Activations";
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

      const now = new Date();
      const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`;
      XLSX.writeFile(workbook, `CreditLineActivations_${sheetName}_${timestamp}.xlsx`);

    } catch (error) {
      console.error("Error exporting credit line activation data to Excel:", error);
      alert("An error occurred while exporting data. Please check the console.");
    } finally {
      setIsExportingExcel(false);
    }
  };

  return (
    <div className="p-4 md:p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-4"> {/* Existing div for title */}
        <h1 className="text-2xl mb-2 font-bold text-gray-800">Credit Line Activation</h1>
        {/* EXPORT BUTTON - ADD THIS */}
        {currentTableData && currentTableData.length > 0 && (
          <button
            onClick={handleExportActivations} // Define this function
            disabled={isExportingExcel || loading || !!activatingId || !!deactivatingId} // Disable if any action is in progress
            className={`ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${(isExportingExcel || loading || !!activatingId || !!deactivatingId) ? 'opacity-50 cursor-not-allowed' : ''
              }`}
          >
            {isExportingExcel ? (
              <> {/* SVG Spinner */} <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Exporting... </>
            ) : (
              <> {/* Download Icon SVG */} <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2"><path strokeLinecap="round" strokeLinejoin="round" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg> Export Data </>
            )}
          </button>
        )}
        {/* End Export Button */}
      </div>
      {/* Filter Component */}
      <div className="mb-6"> {/* Optional: Add margin below filters */}
        <FilterSection
          filters={filters}
          setFilters={setFilters}
          // No showFilters/setShowFilters needed here
          resetFilters={resetFilters}
        />
      </div>

      {/* Tab Navigation */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-6 overflow-x-auto" aria-label="Tabs">
          <button onClick={() => setActiveTab('all')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'all' ? 'border-[#b0cfbf] text-black bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            All ({loading ? '...' : allActivations.length}) {/* Show total count before filtering */}
          </button>
          <button onClick={() => setActiveTab('pending')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'pending' ? 'border-[#b0cfbf] text-black bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            Pending Activation ({loading ? '...' : pendingActivations.length})
          </button>
          <button onClick={() => setActiveTab('activated')}
            className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTab === 'activated' ? 'border-[#b0cfbf] text-black bg-white' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            Activated ({loading ? '...' : activeActivations.length})
          </button>
        </nav>
      </div>

      {/* Loading & Error Display */}
      {loading && !fetchError && (
        <LoadingModal />
      )}
      {fetchError && (
        <div className="bg-red-50 border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{fetchError}</span>
          <button onClick={fetchData} className="ml-4 py-1 px-2 border border-red-300 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200">
            Retry Fetch
          </button>
        </div>
      )}

      {/* Table Display */}
      {!loading && !fetchError && (
        <div className="shadow border-b border-gray-200 sm:rounded-lg overflow-x-scroll">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer Details</th>
                <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Contract</th>
                <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {/* Use the memoized and filtered data */}
              {renderTableRows(currentTableData)}
            </tbody>
          </table>
        </div>
      )}
      <DeactivationModal
        isOpen={showDeactivateModal}
        onClose={() => setShowDeactivateModal(false)}
        onDeactivateApiCall={handleDeactivateApiCall} // Pass the new function
        creditLineName={getNested(selectedCreditLine, 'userDetails.kyc.businessDetails.businessName', 'this credit line')}
        selectedCreditLineFromParent={selectedCreditLine}
        isDeactivating={isDeactivating} // PASS THIS PROP
      />
    </div>
  );
}
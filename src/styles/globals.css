@tailwind base;
@tailwind components;
@tailwind utilities;

/* --- Always visible horizontal scrollbar styling --- */

/* For Webkit browsers (Chrome, Safari, Edge, Opera) */
.custom-h-scrollbar::-webkit-scrollbar {
    height: 8px; /* Set a specific height for the horizontal scrollbar */
    background-color: #f1f1f1; /* Make scrollbar track visible (light grey) */
  }
  
  .custom-h-scrollbar::-webkit-scrollbar-thumb {
    background-color: #888; /* Color of the draggable scroll handle (darker grey) */
    border-radius: 4px;  /* Optional: Rounded corners for the handle */
  }
  
  /* Optional: Style on hover */
  .custom-h-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* Make handle darker on hover */
  }
  
  /* For Firefox (More limited styling) */
  .custom-h-scrollbar {
    scrollbar-width: thin; /* Try to make it less obtrusive */
    scrollbar-color: #888 #f1f1f1; /* thumb color track color */
  }

  @keyframes modal-slide-in {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-modal-slide-in {
    animation: modal-slide-in 0.3s ease-out forwards;
  } 
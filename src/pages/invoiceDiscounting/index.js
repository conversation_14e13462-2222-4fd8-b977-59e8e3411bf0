import React, { useState, useEffect, useCallback, useMemo } from 'react'; // Make sure useMemo is imported
import axios from 'axios';
import { Disclosure } from '@headlessui/react'; // Disclosure comes from Headless UI
import {  FunnelIcon, XMarkIcon, ChevronUpIcon } from '@heroicons/react/24/outline'; // Add Disclosure, FunnelIcon, XMarkIcon, ChevronUpIcon
import { formatDistanceToNow, parseISO, differenceInDays } from 'date-fns'; // Ensure date-fns imports are present if used for age filter
import StatusBadge from '../../components/StatusBadge';
import config from "../../../config.json"
import * as XLSX from 'xlsx';
import { useRouter } from 'next/router'; 
import LoadingModal from '../../components/Loading';

const INVOICE_STATUS_ORDER = {
    'VERIFICATION_PENDING_ANCHOR': 1,
    'MORE_INFO_NEEDED_ANCHOR': 2,
    'VERIFIED_ANCHOR': 3,
    'REJECTED_ANCHOR': 4,
    'VERIFICATION_PENDING_LENDER': 5,
    'MORE_INFO_NEEDED_LENDER': 6,
    'ACCEPTED_LENDER': 7,
    'REJECTED_LENDER': 8,
    'READY_FOR_DISBURSAL': 9,
    'LOAN_IN_PROGRESS': 10,
    'DISBURSED': 11,
    'DEFAULT': 12,
    'LOAN_CANCELLED': 13,
    'WRITTEN_OFF_PAID': 14,
    'UNKNOWN': 99, // Fallback for any other status
  };
  const DEFAULT_INVOICE_STATUS_PRIORITY = Math.max(...Object.values(INVOICE_STATUS_ORDER)) + 1;


const getNested = (obj, path, defaultValue = undefined) => {
    const properties = path.split('.');
    return properties.reduce((acc, key) => (acc && acc[key] !== undefined && acc[key] !== null ? acc[key] : defaultValue), obj);
};


const formatDate = (dateString) => {
    // (Keep the existing formatDate function)
    if (!dateString || dateString === 'N/A' || dateString === undefined) return 'N/A';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime()) || date.getFullYear() <= 1970) return 'N/A';
        return date.toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return 'Invalid Date';
    }
};
// --- End Helper Functions ---

// --- InvoiceFilterSection Component ---
const InvoiceFilterSection = ({ filters, setFilters, resetFilters }) => {
    // Input styling
    const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
    const numberInputClass = `${inputBaseClass} px-2 py-1`;
    const dateInputClass = `${inputBaseClass} px-2 py-1`;
    const textInputClass = `${inputBaseClass} px-3 py-1.5`;
    const selectInputClass = `${inputBaseClass} px-3 py-2`;

    const handleFilterChange = (event) => {
        const { name, value } = event.target;
        setFilters(prevFilters => ({ ...prevFilters, [name]: value }));
    };

    const activeFilterCount = useMemo(() => {
        return Object.values(filters).filter(v => v !== '' && v !== null && v !== undefined).length;
    }, [filters]);

    const handleReset = (event) => {
        event.stopPropagation();
        resetFilters();
    };

    // Status options (reuse from main component if preferred)
    const statusOptions = [
        'READY_FOR_DISBURSAL',
        'VERIFIED_ANCHOR', 'VERIFICATION_PENDING_LENDER', 'ACCEPTED_LENDER',
        'REJECTED_LENDER', 'MORE_INFO_NEEDED_LENDER', 'DISBURSED', 'LOAN_IN_PROGRESS'
    ];
    const statusDisplay = { // Copied from main component for clarity
        'VERIFIED_ANCHOR': 'Verified (Buyer)',
        'VERIFICATION_PENDING_LENDER': 'Pending Verification',
        'ACCEPTED_LENDER': 'Accepted',
        'REJECTED_LENDER': 'Rejected',
        'MORE_INFO_NEEDED_LENDER': 'More Info Needed',
        'READY_FOR_DISBURSAL': 'Ready For Disbursal',
        'DISBURSED': 'Disbursed',
        'LOAN_IN_PROGRESS': 'Loan In Progress',
    };
    const ageOptions = [
        { value: '1', label: 'Today' },
        { value: '7', label: 'Added Last 7 Days' },
        { value: '30', label: 'Added Last 30 Days' },
        { value: '90', label: 'Added Last 90 Days' },
        { value: 'over90', label: 'Added Over 90 Days Ago' },
    ];

    return (
        <div className="mb-6">
            <Disclosure as="div" className="border border-gray-200 rounded-lg shadow-sm bg-white">
                {({ open }) => (
                    <>
                        {/* Header Bar */}
                        <div className="flow-root">
                            <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none">
                                <span className="flex items-center">
                                    <FunnelIcon className="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
                                    Filters
                                    {activeFilterCount > 0 && (
                                        <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-sm font-medium text-gray-800">
                                            {activeFilterCount}
                                        </span>
                                    )}
                                </span>
                                <span className="ml-6 flex items-center">
                                    <ChevronUpIcon className={`${open ? 'rotate-180' : ''} h-5 w-5 text-gray-500 transition-transform`} />
                                </span>
                            </Disclosure.Button>
                        </div>

                        {/* Separator */}
                        {open && <div className="border-t border-gray-200"></div>}

                        {/* Panel */}
                        <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
                            {/* Top Row: Search & Clear */}
                            <div className="mb-6 flex items-start justify-between gap-4">
                                <div className="flex-1">
                                    <label htmlFor="invSearchTerm" className="sr-only">Search</label>
                                    <input
                                        type="text" name="searchTerm" id="invSearchTerm"
                                        value={filters.searchTerm} onChange={handleFilterChange}
                                        className={textInputClass} placeholder="Search Invoice #, Buyer, Customer..."
                                    />
                                </div>
                                <button
                                    type="button" onClick={handleReset}
                                    className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                                >
                                    <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" /> Clear all
                                </button>
                            </div>

                            {/* Filter Grid */}
                            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">

                                {/* Invoice Date Range */}
                                <div className="sm:col-span-2 md:col-span-1">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Date</label>
                                    <div className="flex flex-col space-y-2">
                                        <input type="date" name="invoiceStartDate" value={filters.invoiceStartDate} onChange={handleFilterChange} className={dateInputClass} aria-label="Invoice Start Date" />
                                        <input type="date" name="invoiceEndDate" value={filters.invoiceEndDate} onChange={handleFilterChange} className={dateInputClass} aria-label="Invoice End Date" />
                                    </div>
                                </div>

                                {/* Due Date Range */}
                                <div className="sm:col-span-2 md:col-span-1">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                                    <div className="flex flex-col space-y-2">
                                        <input type="date" name="dueStartDate" value={filters.dueStartDate} onChange={handleFilterChange} className={dateInputClass} aria-label="Due Start Date" />
                                        <input type="date" name="dueEndDate" value={filters.dueEndDate} onChange={handleFilterChange} className={dateInputClass} aria-label="Due End Date" />
                                    </div>
                                </div>

                                {/* Invoice Amount Range */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Amount (QAR)</label>
                                    <div className="flex space-x-2">
                                        <input type="number" name="minAmount" value={filters.minAmount} onChange={handleFilterChange} placeholder="Min" className={numberInputClass} />
                                        <input type="number" name="maxAmount" value={filters.maxAmount} onChange={handleFilterChange} placeholder="Max" className={numberInputClass} />
                                    </div>
                                </div>

                                {/* Status Dropdown */}
                                <div>
                                    <label htmlFor="statusFilterInv" className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                    <select id="statusFilterInv" name="status" value={filters.status} onChange={handleFilterChange} className={selectInputClass}>
                                        <option value="">All Relevant</option>
                                        {statusOptions.map(status => <option key={status} value={status}>{statusDisplay[status] || status}</option>)}
                                    </select>
                                </div>

                                {/* Age Dropdown */}
                                <div>
                                    <label htmlFor="ageFilterInv" className="block text-sm font-medium text-gray-700 mb-1">Invoice Age</label>
                                    <select id="ageFilterInv" name="age" value={filters.age} onChange={handleFilterChange} className={selectInputClass}>
                                        <option value="">Any Age</option>
                                        {ageOptions.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                                    </select>
                                </div>

                            </div>
                        </Disclosure.Panel>
                    </>
                )}
            </Disclosure>
        </div>
    );
};

const calculateInvoiceAge = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const date = parseISO(dateString); // Parse the ISO string (e.g., "2025-04-03T09:33:17.697Z")
        // Add 'ago' suffix for clarity
        return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
        console.error("Error parsing date for age calculation:", dateString, error);
        return 'Invalid Date';
    }
};

const STATUS_BUTTON_LABELS = {
    'VERIFIED_ANCHOR': 'Discount Invoice',
    'VERIFICATION_PENDING_LENDER': 'Discount Invoice',
    'ACCEPTED_LENDER': 'View Offer',
    'REJECTED_LENDER': 'View Offer',
    'MORE_INFO_NEEDED_LENDER': 'Review',
    'DISBURSED': 'View Disbursed Details',
    'READY_FOR_DISBURSAL': 'View Disbursal Details',
    'LOAN_IN_PROGRESS': 'View Offer',
};

// --- Status Styling and Display Names ---
// --- Status Styling and Display Names ---
const STATUS_STYLES = {
    'VERIFIED_ANCHOR': { bg: 'bg-[#4dce83]', text: 'text-white' },
    'VERIFICATION_PENDING_LENDER': { bg: 'bg-yellow-100', text: 'text-yellow-800' },
    // MODIFIED HERE: Use 'border' instead of 'border-1'
    'ACCEPTED_LENDER': { bg: 'bg-white border border-[#4dce83]', text: 'text-[#4dce83]' },
    // MODIFIED HERE: Use a specific shade for 'bg-red', e.g., 'bg-red-500'
    'REJECTED_LENDER': { bg: 'bg-red-500', text: 'text-white' }, // Or bg-red-600, etc.
    'MORE_INFO_NEEDED_LENDER': { bg: 'bg-orange-100', text: 'text-orange-800' },
    'DISBURSED': { bg: 'bg-[#189e51]', text: 'text-white' },
    'READY_FOR_DISBURSAL': { bg: 'bg-[#46a5ef]', text: 'text-white' },
    'LOAN_IN_PROGRESS': { bg: 'bg-purple-100', text: 'text-purple-800' },
    // Ensure any other custom colors like bg-[#4dce83] are correctly processed by your Tailwind setup.
};

const STATUS_DISPLAY_NAMES = {
    'VERIFIED_ANCHOR': 'Verified (Buyer)',
    'VERIFICATION_PENDING_LENDER': 'Pending Verification',
    'ACCEPTED_LENDER': 'Accepted',
    'REJECTED_LENDER': 'Rejected',
    'MORE_INFO_NEEDED_LENDER': 'More Info Needed',
    // Add others
    'READY_FOR_DISBURSAL': 'Ready For Disbursal',
    'DISBURSED': 'Disbursed',
    'LOAN_IN_PROGRESS': 'Loan In Progress',
};

// --- Main Component ---
const InvoiceApprovals = () => {
    // --- State Variables ---
    const [showUploadModal, setShowUploadModal] = useState(false);
    const [uploadedFile, setUploadedFile] = useState(null);
    const [isLoading, setIsLoading] = useState(false); // General loading for actions
    const [isFetching, setIsFetching] = useState(true); // Specific loading for initial fetch
    const [showPdfPreview, setShowPdfPreview] = useState(false);
    const [pdfUrl, setPdfUrl] = useState('');
    const [interestRate, setInterestRate] = useState('');
    const [invoices, setInvoices] = useState([]);
    const [fetchError, setFetchError] = useState(null); // Use specific name
    const [mindeeData, setMindeeData] = useState(null);
    const [extractingData, setExtractingData] = useState(false);
    const [editingInvoice, setEditingInvoice] = useState(null); // Holds the full invoice object
    const [verificationComments, setVerificationComments] = useState('');
    const [isExportingExcel, setIsExportingExcel] = useState(false)
    const router = useRouter()
    // REMOVED: currentInvoiceId (use editingInvoice._id)
    const [showAcceptModal, setShowAcceptModal] = useState(false);
    const [acceptOfferDetails, setAcceptOfferDetails] = useState({
        invoiceDiscountingPercentage: '', // Will be pre-filled
        processingFeeType: 'percentage', // Default, might be overridden
        processingFeeValue: '',          // Will be pre-filled
        tenureDays: '',                  // Will be pre-filled
        emiRepaymentFrequency: 'MONTHLY', // Default, fixed
    });
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);

    const [isFetchingCreditLine, setIsFetchingCreditLine] = useState(false);
    const [creditLineFetchError, setCreditLineFetchError] = useState(null);

    const [viewedOfferDetails, setViewedOfferDetails] = useState(null);

    // --- State for New Filters ---
    const [invoiceFilters, setInvoiceFilters] = useState({
        searchTerm: '',
        invoiceStartDate: '',
        invoiceEndDate: '',
        dueStartDate: '',
        dueEndDate: '',
        minAmount: '',
        maxAmount: '',
        status: '', // Replaces statusFilter
        age: '',    // Replaces ageFilter
    });

    const resetInvoiceFilters = useCallback(() => {
        setInvoiceFilters({
            searchTerm: '',
            invoiceStartDate: '',
            invoiceEndDate: '',
            dueStartDate: '',
            dueEndDate: '',
            minAmount: '',
            maxAmount: '',
            status: '',
            age: '',
        });
    }, []);

    // --- Add this helper function inside the InvoiceApprovals component ---
    const getFilenameFromPath = (path) => {
        if (!path) return 'document';
        try {
            // Attempt to handle both full URLs and simple paths
            const url = new URL(path);
            const pathnameParts = url.pathname.split('/');
            return decodeURIComponent(pathnameParts[pathnameParts.length - 1] || 'document');
        } catch (e) {
            if (e) {
                console.log(e);
            }
            // If not a valid URL, treat as a simple path
            const pathParts = path.split('/');
            return pathParts[pathParts.length - 1] || 'document';
        }
    };


    const addDocumentFieldsToRow = (rowData, docObject, prefix, getNestedFn, formatDateFn) => {
        if (!docObject || typeof docObject !== 'object') {
            rowData[`${prefix} - File Name`] = 'N/A';
            rowData[`${prefix} - Signed URL`] = 'N/A';
            rowData[`${prefix} - Uploaded On`] = 'N/A';
            // Add other relevant doc fields as 'N/A' if the structure expects them
            rowData[`${prefix} - Status (Doc)`] = 'N/A'; // If individual docs have status
            rowData[`${prefix} - Notes (Doc)`] = '';    // If individual docs have notes
            rowData[`${prefix} - MIME Type`] = 'N/A';
            return;
        }
        const filePath = getNestedFn(docObject, 'filePath', '');
        rowData[`${prefix} - File Name`] = filePath ? (filePath.split('/').pop() || 'document') : 'N/A';
        rowData[`${prefix} - Signed URL`] = getNestedFn(docObject, 'signedUrl', 'N/A');
        rowData[`${prefix} - Uploaded On`] = formatDateFn(getNestedFn(docObject, 'uploadedOn'));
        rowData[`${prefix} - Status (Doc)`] = getNestedFn(docObject, 'verificationStatus', 'N/A');
        rowData[`${prefix} - Notes (Doc)`] = getNestedFn(docObject, 'verificationNotes', '');
        rowData[`${prefix} - MIME Type`] = getNestedFn(docObject, 'mimeType', 'N/A');
        // Add verifiedOrRejectedOn if your document objects have it
        // rowData[`${prefix} - Verification Date`] = formatDateFn(getNestedFn(docObject, 'verifiedOrRejectedOn'));
    };

    // --- New Filtering Logic using useMemo ---
    const filteredInvoices = useMemo(() => {
        const {
            searchTerm, invoiceStartDate, invoiceEndDate, dueStartDate, dueEndDate,
            minAmount, maxAmount, status, age
        } = invoiceFilters;

        const lowerSearchTerm = searchTerm.toLowerCase();
        const tsInvStart = invoiceStartDate ? new Date(invoiceStartDate).setHours(0, 0, 0, 0) : null;
        const tsInvEnd = invoiceEndDate ? new Date(invoiceEndDate).setHours(23, 59, 59, 999) : null;
        const tsDueStart = dueStartDate ? new Date(dueStartDate).setHours(0, 0, 0, 0) : null;
        const tsDueEnd = dueEndDate ? new Date(dueEndDate).setHours(23, 59, 59, 999) : null;
        const numMinAmount = minAmount === '' ? -Infinity : parseFloat(minAmount);
        const numMaxAmount = maxAmount === '' ? Infinity : parseFloat(maxAmount);

        return invoices.filter(inv => {
            if (!inv) return false; // Basic safety check

            // Search Filter
            const invoiceNumber = inv.invoiceNumber?.toLowerCase() || '';
            const supplier = inv.supplierName?.toLowerCase() || '';
            const customer = inv.customerName?.toLowerCase() || '';
            if (lowerSearchTerm && !(invoiceNumber.includes(lowerSearchTerm) || supplier.includes(lowerSearchTerm) || customer.includes(lowerSearchTerm))) {
                return false;
            }

            // Invoice Date Filter
            const invoiceDateTs = inv.invoiceDate ? new Date(inv.invoiceDate).getTime() : null;
            if (tsInvStart && (!invoiceDateTs || invoiceDateTs < tsInvStart)) return false;
            if (tsInvEnd && (!invoiceDateTs || invoiceDateTs > tsInvEnd)) return false;

            // Due Date Filter
            const dueDateTs = inv.dueDate ? new Date(inv.dueDate).getTime() : null;
            if (tsDueStart && (!dueDateTs || dueDateTs < tsDueStart)) return false;
            if (tsDueEnd && (!dueDateTs || dueDateTs > tsDueEnd)) return false;

            // Amount Filter
            const totalAmount = parseFloat(String(inv.totalAmount).replace(/[^0-9.-]+/g, ""));
            if (!isNaN(numMinAmount) && (isNaN(totalAmount) || totalAmount < numMinAmount)) return false;
            if (!isNaN(numMaxAmount) && (isNaN(totalAmount) || totalAmount > numMaxAmount)) return false;

            // Status Filter
            if (status && inv.status !== status) return false;

            // Age Filter
            if (age) {
                const dateField = inv?.uploadedAt || inv?.insertedOn;
                if (!dateField) return false; // Exclude if no date for age calc
                try {
                    const invoiceDate = parseISO(dateField);
                    const now = new Date();
                    const diffDays = differenceInDays(now, invoiceDate);
                    let ageMatch = true;
                    switch (age) {
                        case '1': ageMatch = diffDays <= 1; break; // Today might need adjustment based on exact time
                        case '7': ageMatch = diffDays <= 7; break;
                        case '30': ageMatch = diffDays <= 30; break;
                        case '90': ageMatch = diffDays <= 90; break;
                        case 'over90': ageMatch = diffDays > 90; break;
                        default: break;
                    }
                    if (!ageMatch) return false;
                } catch (e) {
                    console.error("Error parsing date for age filter:", dateField, e);
                    return false; // Exclude on error
                }
            }

            return true; // Passed all filters
        });
    }, [invoices, invoiceFilters]); // Re-filter when raw invoices or any filter changes
    // --- END Filtering Logic ---

    // --- Initial Data Fetch ---
    useEffect(() => {
        fetchInvoices();
    }, []); // Run only once on mount

    // --- Fetch Invoices Function (Corrected) ---
    const fetchInvoices = async () => {
        const storedLenderId = localStorage.getItem('userId');
        if (!storedLenderId) {
            console.error("[Lender Dashboard] Lender ID not found in localStorage. Cannot fetch invoices.");
            setIsFetching(false);
            setInvoices([]);
            setFetchError("Lender ID not found. Please log in again.");
            return;
        }

        console.log(`[Lender Dashboard] Fetching invoices for Lender ID: ${storedLenderId}...`);
        setIsFetching(true);
        setFetchError(null);

        try {
            const response = await axios.get(
                `${config.apiUrl}/ops/invoiceFinancing/lender/invoices`,
                { params: { lenderId: storedLenderId } } // Pass lenderId as query param
            );

            if (response.data && Array.isArray(response.data)) {
                const fetchedInvoices = response.data;
                console.log(`[Lender Dashboard] Received ${fetchedInvoices.length} raw invoices.`);

                const excludedStatuses = [
                    // 'VERIFICATION_PENDING_ANCHOR', // Already excluded by backend route
                    'MORE_INFO_NEEDED_ANCHOR',
                    'REJECTED_ANCHOR'
                ];

                // Filter on frontend and access status directly (no _doc)
                const filteredInvoices = fetchedInvoices.filter(inv =>
                    inv.status && !excludedStatuses.includes(inv.status)
                );

                // --- START: APPLIED SORT LOGIC (adapted for direct object properties) ---
            filteredInvoices.sort((a, b) => {
                const statusA = a.status; // Direct property access
                const statusB = b.status; // Direct property access

                const orderA = INVOICE_STATUS_ORDER[statusA] ?? DEFAULT_INVOICE_STATUS_PRIORITY;
                const orderB = INVOICE_STATUS_ORDER[statusB] ?? DEFAULT_INVOICE_STATUS_PRIORITY;

                if (orderA !== orderB) {
                    return orderA - orderB;
                }

                // Date comparison (newest first)
                // Ensure date fields are correctly accessed (e.g., a.uploadedAt or a.insertedOn)
                const dateValueA = a.uploadedAt || a.insertedOn || 0;
                const dateValueB = b.uploadedAt || b.insertedOn || 0;

                const dateA = new Date(dateValueA);
                const dateB = new Date(dateValueB);

                const timeA = !isNaN(dateA.getTime()) ? dateA.getTime() : 0;
                const timeB = !isNaN(dateB.getTime()) ? dateB.getTime() : 0;

                return timeB - timeA; // Newest first
            });
            // --- END: APPLIED SORT LOGIC ---

            console.log(`[Lender Dashboard] Displaying ${filteredInvoices.length} invoices after frontend filtering and sorting.`);
            setInvoices(filteredInvoices); // Set the sorted invoices
            } else {
                console.warn('[Lender Dashboard] Unexpected response format:', response.data);
                setInvoices([]);
            }
        } catch (error) {
            console.error('[Lender Dashboard] Error fetching lender invoices:', error.response?.data || error.message || error);
            if (axios.isAxiosError(error) && error.response?.data?.message) {
                setFetchError(`Failed to fetch invoices: ${error.response.data.message}`);
            } else {
                setFetchError(error.message || "An unknown error occurred while fetching invoices.");
            }
            setInvoices([]);
        } finally {
            setIsFetching(false);
        }
    };


    // --- Event Handlers ---

    const handleDiscountPercentageChange = (value) => {
        if (isNaN(value)) {
            setAcceptOfferDetails({ ...acceptOfferDetails, invoiceDiscountingPercentage: '' });
        } else {
            const newValue = Math.min(100, Math.max(0, value));
            setAcceptOfferDetails({ ...acceptOfferDetails, invoiceDiscountingPercentage: newValue });
        }
    };

    const handleEmiRepaymentFrequencyChange = (e) => {
        setAcceptOfferDetails({ ...acceptOfferDetails, emiRepaymentFrequency: e.target.value });
    };

    // Handle Accept Invoice / Create Offer (ensure editingInvoice is set correctly)
    const handleAcceptInvoice = async () => {
        if (!editingInvoice || !editingInvoice._id || !editingInvoice.userId || !editingInvoice.totalAmount) {
            alert("Cannot proceed: Missing critical invoice details.");
            console.error("Missing data in editingInvoice:", editingInvoice);
            return;
        }

        setIsLoading(true);
        setFetchError(null);

        try {
            const lenderId = localStorage.getItem('userId'); // Use localStorage as per previous context
            if (!lenderId) throw new Error("Lender session expired or not found.");

            // 1. Create Offer
            await axios.post(`${config.apiUrl}/ops/invoiceFinancing/createOffer`, {
                invoiceId: editingInvoice._id, // Use ID from the stored editingInvoice object
                lenderId: lenderId,
                invoiceDiscountingPercentage: acceptOfferDetails.invoiceDiscountingPercentage,
                processingFee: {
                    type: acceptOfferDetails.processingFeeType,
                    value: acceptOfferDetails.processingFeeValue || 0,
                },
                tenureDays: acceptOfferDetails.tenureDays,
                emiRepaymentFrequency: acceptOfferDetails.emiRepaymentFrequency,
                creditLimit: editingInvoice.totalAmount, // Make sure this is the correct logic
                merchantId: editingInvoice.userId, // Use userId from the stored editingInvoice object
                offerType: "invoiceDiscountingOffer"
            });

            // 2. Update Invoice Status
            // Optional: Send comments/verifier if backend accepts them
            await axios.put(`${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${editingInvoice._id}`, {
                status: 'ACCEPTED_LENDER',
                // verificationComments: verificationComments, // Send comments if needed
                // verifiedBy: lenderId
            });

            alert('Invoice accepted and offer created successfully!');
            await fetchInvoices(); // Refresh list
            // Close modals and reset state
            setShowAcceptModal(false);
            setShowConfirmationModal(false);
            setEditingInvoice(null);
            // setVerificationComments(''); // Reset comments

        } catch (error) {
            console.error('Error accepting invoice / creating offer:', error.response?.data || error.message);
            const errorMsg = error.response?.data?.message || error.message || 'Failed to accept invoice / create offer.';
            setFetchError(errorMsg);
            alert(errorMsg);
        } finally {
            setIsLoading(false);
        }
    };

    const handleOpenAcceptModal = useCallback(async (invoiceToAccept) => {
        if (!invoiceToAccept || !invoiceToAccept.userId) {
            console.error("Cannot fetch credit line: Missing invoice or user ID.");
            alert("Cannot proceed: Missing required invoice information.");
            return;
        }

        setEditingInvoice(invoiceToAccept); // Set the context
        setShowAcceptModal(true);           // Show the modal container immediately
        setIsFetchingCreditLine(true);      // Start loader *inside* the modal
        setCreditLineFetchError(null);      // Clear previous errors
        setAcceptOfferDetails({             // Reset form while loading
            invoiceDiscountingPercentage: '',
            processingFeeType: 'percentage',
            processingFeeValue: '',
            tenureDays: '',
            emiRepaymentFrequency: 'MONTHLY',
        });

        try {
            console.log(`Workspaceing credit line for user ID: ${invoiceToAccept.userId}`);
            const response = await axios.get(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`);

            let userCreditLine = null;
            if (response.data && Array.isArray(response.data)) {
                // Find the specific credit line for this user (merchant)
                // Prioritize ACTIVE lines, then others if needed (adjust logic if required)
                userCreditLine = response.data.find(cl => cl.userId === invoiceToAccept.userId && cl.creditLineStatus === 'ACTIVE')
                    || response.data.find(cl => cl.userId === invoiceToAccept.userId); // Fallback to any line for the user
            }

            if (userCreditLine) {
                console.log("Found Credit Line:", userCreditLine);
                setInterestRate(userCreditLine.interestRate)
                // Pre-fill details based on fetched credit line and requirements
                setAcceptOfferDetails({
                    // Fixed requirements
                    invoiceDiscountingPercentage: 80, // Fixed at 80%
                    emiRepaymentFrequency: 'MONTHLY', // Fixed

                    // Values from Credit Line (with fallbacks)
                    // Note: Ensure API field names ('tenure', 'processingFeeType', 'processingFee') match exactly
                    tenureDays: userCreditLine.tenure !== undefined ? userCreditLine.tenure : '',
                    processingFeeType: userCreditLine.processingFeeType || 'percentage', // Use fetched type or default
                    processingFeeValue: userCreditLine.processingFee !== undefined ? userCreditLine.processingFee : '', // Use fetched fee value
                });
            } else {
                console.log("No specific credit line found for user. Using defaults.");
                // Set defaults if no credit line is found
                setAcceptOfferDetails({
                    invoiceDiscountingPercentage: 80, // Fixed default
                    processingFeeType: 'percentage',
                    processingFeeValue: '', // No default processing fee value
                    tenureDays: '',         // No default tenure
                    emiRepaymentFrequency: 'MONTHLY',
                });
                // Optionally set an info message instead of an error if this is expected
                // setCreditLineFetchError("No active credit line found for merchant. Please enter details manually.");
            }

        } catch (error) {
            console.error('Error fetching credit line:', error.response?.data || error.message);
            const errorMsg = error.response?.data?.message || error.message || 'Failed to fetch credit line details.';
            setCreditLineFetchError(errorMsg);
            // Keep modal open but show error, keep form empty/defaulted
            setAcceptOfferDetails({
                invoiceDiscountingPercentage: 80, // Still set fixed defaults even on error? Or keep empty?
                processingFeeType: 'percentage',
                processingFeeValue: '',
                tenureDays: '',
                emiRepaymentFrequency: 'MONTHLY',
            });
        } finally {
            setIsFetchingCreditLine(false); // Stop loader
        }
    }, []);

    // Render Invoice Details (Updated to remove _doc)

    const renderInvoiceDetails = (invoice) => {
        // Guard clause if no invoice data is provided
        if (!invoice) {
            return <p className="text-gray-500 p-4 text-center">No invoice selected or details available.</p>;
        }

        return (
            // Outer container for the details section
            <div className="bg-white shadow-lg rounded-lg p-4 sm:p-6 border border-gray-100 flex flex-col h-full max-h-screen">
                {/* Header for the details section */}
                <div className="mb-4 sm:mb-5 pb-3 sm:pb-4 border-b border-gray-200">
                    <h3 className="text-base sm:text-lg font-bold text-gray-800">Invoice Details</h3>
                </div>

                {/* Scrollable content area */}
                <div className="overflow-y-auto flex-grow pr-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                    {/* Grid layout for main invoice fields */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 sm:gap-x-6 gap-y-3 sm:gap-y-4 mb-4">
                        {/* Left Column */}
                        <div className="space-y-3 sm:space-y-4">
                            {/* Invoice Number */}
                            <div className="bg-white rounded-md p-2 sm:p-3">
                                <span className="text-xs text-gray-500 block">Invoice Number</span>
                                <span className="text-sm sm:text-base text-gray-800 font-medium break-words">{invoice.invoiceNumber || 'N/A'}</span>
                            </div>
                            {/* Invoice Date */}
                            <div className="bg-white rounded-md p-2 sm:p-3">
                                <span className="text-xs text-gray-500 block">Invoice Date</span>
                                <span className="text-sm sm:text-base text-gray-800 font-medium">{invoice.invoiceDate || 'N/A'}</span>
                            </div>
                            {/* Due Date */}
                            <div className="bg-white rounded-md p-2 sm:p-3">
                                <span className="text-xs text-gray-500 block">Due Date</span>
                                <span className="text-sm sm:text-base text-gray-800 font-medium">{invoice.dueDate || 'N/A'}</span>
                            </div>
                        </div>
                        {/* Right Column */}
                        <div className="space-y-3 sm:space-y-4">
                            {/* Supplier */}
                            <div className="bg-white rounded-md p-2 sm:p-3">
                                <span className="text-xs text-gray-500 block">Buyer</span>
                                <span className="text-sm sm:text-base text-gray-800 font-medium break-words">{invoice.supplierName || 'N/A'}</span>
                            </div>
                            {/* Customer */}
                            <div className="bg-white rounded-md p-2 sm:p-3">
                                <span className="text-xs text-gray-500 block">Customer</span>
                                <span className="text-sm sm:text-base text-gray-800 font-medium break-words">{invoice.customerName || 'N/A'}</span>
                            </div>
                            {/* Status */}
                            <div className="bg-white rounded-md p-2 sm:p-3">
                                <span className="text-xs text-gray-500 block">Status</span>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusStyle(invoice.status)} capitalize`}>
                                    {getStatusDisplay(invoice.status)}
                                </span>
                            </div>
                        </div>
                    </div>
                    {/* Amount Due Section */}
                    <div className="sm:p-2 p-2 bg-blue-50 rounded-lg border border-blue-100">
                        <div className="flex items-center space-x-2 mb-1 sm:mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
                            </svg>
                            <span className="text-sm sm:text-base font-medium text-blue-800">Amount Due</span>
                        </div>
                        <div className="text-xl sm:text-2xl font-bold text-blue-900">{formatAmount(invoice.totalAmount)}</div>
                    </div>

                    {/* --- Additional Documents Section --- */}
                    <div className="mt-2 sm:mt-2 pt-1 sm:pt-1 border-t border-gray-200">
                        <h4 className="text-sm sm:text-md font-semibold text-gray-700 mb-3">Supporting Documents</h4>
                        {/* Scrollable list container */}
                        <div className="space-y-2 pr-1">

                            {(invoice.additionalInvoiceDocuments && invoice.additionalInvoiceDocuments.length > 0) ? (
                                // Map through the documents if they exist
                                invoice.additionalInvoiceDocuments.map((doc, index) => (
                                    <div key={doc._id || index} className="flex items-center justify-between bg-gray-50 p-2 pl-3 rounded border border-gray-200 text-xs sm:text-sm">
                                        {/* File icon and name */}
                                        <div className="flex items-center space-x-2 overflow-hidden mr-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0011.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                            <span className="truncate text-gray-700" title={getFilenameFromPath(doc.filePath)}>
                                                {getFilenameFromPath(doc.filePath)}
                                            </span>
                                        </div>
                                        {/* View link */}
                                        {doc.signedUrl ? (
                                            <a
                                                href={doc.signedUrl}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-xs text-blue-600 hover:underline font-medium ml-2 flex-shrink-0 px-2 py-0.5 rounded hover:bg-blue-100 transition-colors"
                                                title={`View Document (Uploaded: ${new Date(doc.uploadedOn).toLocaleDateString()})`}
                                            >
                                                View
                                            </a>
                                        ) : (
                                            // Fallback if no signed URL is present
                                            <span className="text-xs text-gray-400 ml-2 flex-shrink-0">No link</span>
                                        )}
                                    </div>
                                ))
                            ) : (
                                // Message when no documents are present
                                <p className="text-xs sm:text-sm text-gray-500 italic">No supporting documents were uploaded with this invoice.</p>
                            )}
                        </div>
                    </div>

                    {/* Verification Comments Section */}
                    <div className="mt-1 sm:mt-1">
                        <label htmlFor={`comments-${invoice._id}`} className="block text-sm font-medium text-gray-700 mb-1">Verification Comments</label>
                        <textarea
                            id={`comments-${invoice._id}`}
                            value={verificationComments}
                            disabled={invoice.status === 'ACCEPTED_LENDER' || invoice.status === 'DISBURSED' || invoice.status === 'LOAN_IN_PROGRESS'}
                            onChange={(e) => setVerificationComments(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition text-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
                            rows={2}
                            placeholder="Add comments (optional)"
                        />
                    </div>
                </div>

                {/* Sticky footer with action button - always visible */}
                <div className="mt-1 pt-3 border-t border-gray-200 sticky bottom-0 bg-white">
                    <button
                        onClick={() => handleOpenAcceptModal(invoice)}
                        className={`w-full px-4 py-2.5 sm:py-3 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition font-medium text-sm sm:text-base flex items-center justify-center ${
                            // Define which statuses allow accepting the invoice
                            ['VERIFICATION_PENDING_LENDER', 'VERIFIED_ANCHOR', 'MORE_INFO_NEEDED_LENDER'].includes(invoice.status)
                                ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md' // Active state styles
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed' // Disabled state styles
                            }`}
                        // Disable button based on the invoice status
                        disabled={!['VERIFICATION_PENDING_LENDER', 'VERIFIED_ANCHOR', 'MORE_INFO_NEEDED_LENDER'].includes(invoice.status)}
                        title={!['VERIFICATION_PENDING_LENDER', 'VERIFIED_ANCHOR', 'MORE_INFO_NEEDED_LENDER'].includes(invoice.status) ? `Cannot accept invoice with status: ${getStatusDisplay(invoice.status)}` : 'Accept Invoice for Discounting'}
                        aria-disabled={!['VERIFICATION_PENDING_LENDER', 'VERIFIED_ANCHOR', 'MORE_INFO_NEEDED_LENDER'].includes(invoice.status)}
                    >
                        {/* Checkmark Icon */}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        Accept Invoice for Discounting
                    </button>
                </div>
            </div>
        );
    };

    // handleFileUpload logic remains the same
    const handleFileUpload = async (e) => {
        const file = e.target.files[0];
        if (file) {
            setUploadedFile(file);
            setPdfUrl(URL.createObjectURL(file));

            try {
                setExtractingData(true);

                const mindeeFormData = new FormData();
                mindeeFormData.append("document", file);

                const mindeeResponse = await axios.post(
                    "https://api.mindee.net/v1/products/mindee/invoices/v4/predict",
                    mindeeFormData,
                    {
                        headers: {
                            Authorization: "a22f7e4051a9178de0f37d3d7a49b17c", // Replace with your key
                            "Content-Type": "multipart/form-data"
                        }
                    }
                );

                const data = mindeeResponse.data.document.inference.prediction;
                setMindeeData({
                    invoiceNumber: data.invoice_number?.value || 'INV-' + Math.floor(Math.random() * 10000),
                    invoiceDate: data.date?.value || new Date().toISOString().split('T')[0],
                    dueDate: data.due_date?.value || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    totalAmount: data.total_amount?.value || (Math.random() * 10000 + 1000).toFixed(2),
                    supplierName: data.supplier_name?.value || 'Default Buyer',
                    customerName: data.customer_name?.value || 'Default Customer',
                    billingAddress: data.billing_address?.value || 'Default Billing Address',
                    customerAddress: data.customer_address?.value || 'Default Customer Address',
                    panNo: 'SAMPLE-PAN',
                    gstin: 'SAMPLE-GSTIN'
                });

                setShowPdfPreview(true);
            } catch (mindeeError) {
                console.error('Error with Mindee API (using fallback):', mindeeError.response?.data || mindeeError.message);
                setMindeeData({
                    invoiceNumber: 'INV-' + Math.floor(Math.random() * 10000),
                    invoiceDate: new Date().toISOString().split('T')[0],
                    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    totalAmount: (Math.random() * 10000 + 1000).toFixed(2),
                    supplierName: 'Fallback Buyer',
                    customerName: 'Fallback Customer',
                    billingAddress: 'Fallback Billing Address',
                    customerAddress: 'Fallback Customer Address',
                    panNo: 'FALLBACK-PAN',
                    gstin: 'FALLBACK-GSTIN'
                });
                setShowPdfPreview(true);
            } finally {
                setExtractingData(false);
            }
        }
    };

    // handleUploadSubmit logic remains the same
    // REMINDER: Ensure backend /uploadInvoice associates the invoice with the correct userId
    // Currently relies on a prompt - needs a better method.
    const handleUploadSubmit = async () => {
        if (!uploadedFile || !mindeeData) return;

        // --- Logic to determine targetUserId is needed here if lender uploads ---
        // Example: Maybe there's a dropdown or search to select the merchant?
        const targetUserId = prompt("Enter Merchant User ID for this invoice (NEEDS PROPER UI):");
        if (!targetUserId) {
            alert("Merchant selection is required to upload an invoice.");
            return;
        }
        // --- End needed logic ---

        setIsLoading(true);
        setFetchError(null);

        try {
            const formData = new FormData();
            formData.append('pdfFile', uploadedFile);
            Object.keys(mindeeData).forEach(key => {
                formData.append(key, mindeeData[key]);
            });
            // Set appropriate initial status for lender upload?
            formData.append('status', "VERIFICATION_PENDING_LENDER");
            formData.append('userId', targetUserId); // Associate with selected merchant

            await axios.post(`${config.apiUrl}/ops/invoiceFinancing/uploadInvoice`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            });

            alert("Invoice uploaded successfully!");
            await fetchInvoices();
            // Reset state
            setShowUploadModal(false);
            setShowPdfPreview(false);
            setUploadedFile(null);
            setMindeeData(null);
            setPdfUrl('');

        } catch (error) {
            console.error('Error uploading invoice:', error.response?.data || error.message);
            const errorMsg = error.response?.data?.message || error.message || 'Failed to upload invoice.';
            setFetchError(errorMsg);
            alert(`Upload Failed: ${errorMsg}`);
        } finally {
            setIsLoading(false);
        }
    };


    const navigateToInvoiceDetail = useCallback((invoice) => {
    if (!invoice) {
        console.error('Cannot navigate: Invoice object is null or undefined.');
        // Optionally set an error state to inform the user
        return;
    }
    router.push({
        pathname: '/invoiceDiscountingDetail', // Ensure this path matches your file structure/routing
        query: { invoiceData: JSON.stringify(invoice) },
    });
}, [router]);

    const getStatusDisplay = (status) => {
        return STATUS_DISPLAY_NAMES[status] || status?.replace(/_/g, ' ') || 'UNKNOWN';
    };
    const formatAmount = (amount) => {
        if (amount === null || amount === undefined) return 'N/A';
        if (typeof amount === 'string' && amount.startsWith('QAR')) { return amount; }
        const numAmount = Number(String(amount).replace(/[^0-9.-]+/g, ""));
        if (isNaN(numAmount)) { return 'N/A'; }
        return `QAR ${numAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    };

    // calculateDiscountedAmount remains the same (uses editingInvoice state)
    const calculateDiscountedAmount = () => {
        if (!editingInvoice || !editingInvoice.totalAmount || acceptOfferDetails.invoiceDiscountingPercentage === '' || acceptOfferDetails.invoiceDiscountingPercentage === null) return 'N/A';
        try {
            const totalAmount = parseFloat(String(editingInvoice.totalAmount).replace(/[^0-9.-]+/g, ""));
            const discountPercentage = parseFloat(acceptOfferDetails.invoiceDiscountingPercentage);
            if (isNaN(totalAmount) || isNaN(discountPercentage)) return 'N/A';
            // Lender Payout = Total Amount * (Discount % / 100)
            const discountedAmount = totalAmount * (discountPercentage / 100);
            return formatAmount(discountedAmount);
        } catch (e) { console.error("Error calculating amount:", e); return "Error"; }
    };

    // handleProcessingFeeValueChange remains the same
    const handleProcessingFeeValueChange = (e) => {
        const value = parseFloat(e.target.value);
        if (isNaN(value)) {
            setAcceptOfferDetails({ ...acceptOfferDetails, processingFeeValue: '' });
        } else {
            let newValue = value;
            if (acceptOfferDetails.processingFeeType === 'flat') {
                newValue = Math.min(10000, Math.max(0, value));
            } else {
                newValue = Math.min(10, Math.max(0, value));
            }
            setAcceptOfferDetails({ ...acceptOfferDetails, processingFeeValue: newValue });
        }
    };

    // handleTenureDaysChange remains the same
    const handleTenureDaysChange = (e) => {
        const value = parseInt(e.target.value, 10);
        if (isNaN(value)) {
            setAcceptOfferDetails({ ...acceptOfferDetails, tenureDays: '' });
        } else {
            const newValue = Math.max(1, value);
            setAcceptOfferDetails({ ...acceptOfferDetails, tenureDays: newValue });
        }
    };

    // isFormValid remains the same
    const isFormValid = () => {
        const discountValid = acceptOfferDetails.invoiceDiscountingPercentage !== '' && !isNaN(parseFloat(acceptOfferDetails.invoiceDiscountingPercentage)) && parseFloat(acceptOfferDetails.invoiceDiscountingPercentage) > 0 && parseFloat(acceptOfferDetails.invoiceDiscountingPercentage) <= 100;
        const feeValid = acceptOfferDetails.processingFeeValue !== '' && !isNaN(parseFloat(acceptOfferDetails.processingFeeValue)) && parseFloat(acceptOfferDetails.processingFeeValue) >= 0;
        const tenureValid = acceptOfferDetails.tenureDays !== '' && !isNaN(parseInt(acceptOfferDetails.tenureDays, 10)) && parseInt(acceptOfferDetails.tenureDays, 10) >= 1;
        return discountValid && feeValid && tenureValid;
    };

    // Place this function inside your InvoiceApprovals component
    const flattenInvoiceForExport = (invoiceWrapper, maxAdditionalDocs, getNestedFn, formatDateFn, calculateAgeFn, formatAmountFn, getStatusDisplayFn, addDocFn) => {
        const rowData = {};
        const invoice = invoiceWrapper._doc || invoiceWrapper; // Adapt if your invoice structure is different after filtering

        // --- I. Data from Main Table View ---
        rowData['Invoice ID'] = getNestedFn(invoice, '_id', 'N/A');
        rowData['Invoice Number'] = getNestedFn(invoice, 'invoiceNumber', 'N/A');
        rowData['Invoice Date'] = formatDateFn(getNestedFn(invoice, 'invoiceDate'));
        rowData['Due Date'] = formatDateFn(getNestedFn(invoice, 'dueDate'));
        rowData['Invoice Age'] = calculateAgeFn(getNestedFn(invoice, 'uploadedAt') || getNestedFn(invoice, 'insertedOn'));
        rowData['Buyer Name (supplierName in code)'] = getNestedFn(invoice, 'supplierName', 'N/A');
        rowData['Customer Name (customerName in code)'] = getNestedFn(invoice, 'customerName', 'N/A');
        rowData['Total Amount (Raw)'] = getNestedFn(invoice, 'totalAmount', null);
        rowData['Total Amount (Formatted)'] = formatAmountFn(getNestedFn(invoice, 'totalAmount'));
        rowData['Status (Display)'] = getStatusDisplayFn(getNestedFn(invoice, 'status')); // Uses your existing getStatusDisplay
        rowData['Uploaded At'] = formatDateFn(getNestedFn(invoice, 'uploadedAt') || getNestedFn(invoice, 'insertedOn'));

        // --- II. Data from Invoice Details Modal (renderInvoiceDetailsModalContent) ---
        rowData['Billing Address'] = getNestedFn(invoice, 'billingAddress', 'N/A');
        rowData['Customer/Supplier Address'] = getNestedFn(invoice, 'customerAddress', 'N/A'); // Label might need adjustment based on context
        rowData['Verification Comments (Invoice Level)'] = getNestedFn(invoice, 'verificationComments', '');

        // --- III. Main Invoice Document Details ---
        const mainInvoiceDocInfo = {
            filePath: getNestedFn(invoice, 'filePath', getNestedFn(invoice, 'invoiceDocumentName', `Invoice_${invoice.invoiceNumber}.pdf`)), // invoiceDocumentName may not exist
            signedUrl: getNestedFn(invoice, 'signedUrl'), // This is the primary PDF URL for preview
            uploadedOn: getNestedFn(invoice, 'uploadedAt') || getNestedFn(invoice, 'insertedOn'),
            verificationStatus: getNestedFn(invoice, 'status'), // The invoice's own status
            verificationNotes: getNestedFn(invoice, 'verificationComments'), // Comments related to this invoice
            mimeType: getNestedFn(invoice, 'mimeType', 'application/pdf'),
        };
        addDocFn(rowData, mainInvoiceDocInfo, 'Main Invoice Document', getNestedFn, formatDateFn);

        // --- IV. Supporting/Additional Invoice Documents ---
        const additionalDocs = getNestedFn(invoice, 'additionalInvoiceDocuments', []) || [];
        for (let i = 0; i < maxAdditionalDocs; i++) {
            const doc = additionalDocs[i];
            const prefix = `Supporting Doc ${i + 1}`;
            if (doc) {
                addDocFn(rowData, doc, prefix, getNestedFn, formatDateFn);
            } else {
                ['File Name', 'Signed URL', 'Uploaded On', 'Status (Doc)', 'Notes (Doc)', 'MIME Type'].forEach(field => rowData[`${prefix} - ${field}`] = '');
            }
        }

        // --- V. Offer Details (If invoice status indicates an offer was made/accepted and details are available) ---
        // This part depends on how `viewedOfferDetails` state is populated and structured when an invoice's offer is viewed.
        // For simplicity, if an invoice has an associated `offerId` or a status like `ACCEPTED_LENDER`, you might fetch/include those.
        // The current page structure doesn't show a persistent `viewedOfferDetails` linked to each invoice in the main list.
        // It's shown when an "ACCEPTED_LENDER" invoice is viewed in the modal.
        // For export, we might need to look up offer details if available on the `invoice` object directly or associated via IDs.
        // Let's assume if an offer was made, some key fields are on the invoice object or linked.
        rowData['Associated Offer ID (if any)'] = getNestedFn(invoice, 'offerDetails._id', getNestedFn(invoice, 'relatedOfferId', 'N/A')); // Example paths
        rowData['Offer Status (if any)'] = getNestedFn(invoice, 'offerDetails.status', 'N/A');
        rowData['Offer Discounting % (if any)'] = getNestedFn(invoice, 'offerDetails.invoiceDiscountingPercentage', 'N/A');
        rowData['Offer Processing Fee (if any)'] = `${getNestedFn(invoice, 'offerDetails.processingFee.type', '')} ${getNestedFn(invoice, 'offerDetails.processingFee.value', '')}`.trim() || 'N/A';
        rowData['Offer Tenure (if any)'] = getNestedFn(invoice, 'offerDetails.tenureDays', 'N/A');
        addDocFn(rowData, getNestedFn(invoice, 'offerDetails.invoiceContract'), 'Offer Invoice Contract', getNestedFn, formatDateFn);
        return rowData;
    };

    // Place this function inside your InvoiceApprovals component
    const handleExportInvoiceApprovals = async () => {
        const itemsToExport = filteredInvoices; // Use the currently filtered list of invoices

        if (!itemsToExport || itemsToExport.length === 0) {
            alert("No invoices in the current view to export.");
            return;
        }
        setIsExportingExcel(true);

        try {
            let maxAdditionalDocs = 0;
            itemsToExport.forEach(invoiceWrapper => {
                const invoice = invoiceWrapper._doc || invoiceWrapper;
                const additionalDocs = getNested(invoice, 'additionalInvoiceDocuments', []) || [];
                if (additionalDocs.length > maxAdditionalDocs) {
                    maxAdditionalDocs = additionalDocs.length;
                }
            });
            if (maxAdditionalDocs === 0) maxAdditionalDocs = 1; // For header consistency

            const excelData = itemsToExport.map(invoiceWrapper =>
                flattenInvoiceForExport(
                    invoiceWrapper,
                    maxAdditionalDocs,
                    getNested,
                    formatDate, // Using existing formatDate
                    calculateInvoiceAge,
                    formatAmount,
                    getStatusDisplay,
                    addDocumentFieldsToRow
                )
            );

            if (excelData.length === 0) {
                alert("No data to export after processing.");
                setIsExportingExcel(false);
                return;
            }

            const worksheet = XLSX.utils.json_to_sheet(excelData);

            if (excelData.length > 0 && excelData[0]) {
                const headers = Object.keys(excelData[0]);
                const colWidths = headers.map(header => {
                    const headerLength = header ? header.toString().length : 10;
                    return { wch: headerLength + 2 }; // Padding of +2
                });
                worksheet['!cols'] = colWidths;
            }

            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "InvoiceApprovals");

            const now = new Date();
            const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`;
            XLSX.writeFile(workbook, `Invoice_Approvals_Export_${timestamp}.xlsx`);

        } catch (error) {
            console.error("Error exporting invoice approvals data to Excel:", error);
            alert("An error occurred while exporting invoice data. Please check the console.");
        } finally {
            setIsExportingExcel(false);
        }
    };

    if (isFetching || isLoading || isExportingExcel || isFetchingCreditLine) {
        return <LoadingModal />
    }

    // --- Component Return JSX (Using Original Structure) ---
    return (
        <div className="p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-2 pb-4">
                <h1 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2 sm:mb-0">Invoice Approvals</h1>
                <div className="flex space-x-3 items-center"> {/* Ensure items-center for vertical alignment */}
                    <button onClick={fetchInvoices} disabled={isFetching || isLoading || isExportingExcel /* Add isExportingExcel */} className="flex items-center px-3 py-1.5 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50" title="Refresh invoice list">
                        <XMarkIcon className={`w-4 h-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />Refresh List {/* Changed to XMarkIcon as ArrowPathIcon was not in your imports for this file */}
                    </button>

                    {/* EXPORT BUTTON - ADD THIS */}
                    {filteredInvoices && filteredInvoices.length > 0 && (
                        <button
                            onClick={handleExportInvoiceApprovals} // We will define this function
                            disabled={isExportingExcel || isFetching || isLoading}
                            className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${(isExportingExcel || isFetching || isLoading) ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                        >
                            {isExportingExcel ? (
                                <> <XMarkIcon className="w-4 h-4 mr-2 animate-spin" /> Exporting... </>
                            ) : (
                                <> <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2"><path strokeLinecap="round" strokeLinejoin="round" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg> Export Data </>
                            )}
                        </button>
                    )}
                    {/* End Export Button */}
                </div>
            </div>

            {/* Error Display */}
            {fetchError && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{fetchError}</span>
                    <button onClick={fetchInvoices} className="absolute top-0 bottom-0 right-0 px-4 py-3 text-red-800 font-bold">Retry</button>
                </div>
            )}

            <InvoiceFilterSection
                filters={invoiceFilters}
                setFilters={setInvoiceFilters}
                resetFilters={resetInvoiceFilters}
            />

            {/* Invoice Table */}
            <div className="overflow-x-auto border-2 border-gray-200 rounded-lg">
                <table className="w-full bg-white shadow-md rounded-lg border-b-1 border-gray-200">
                    <thead className="border-b-1 border-gray-200">
                        <tr>
                            {/* Updated Headers */}
                            <th className="p-3 text-left text-sm font-semibold text-gray-600 tracking-wider">Invoice ID</th>
                            <th className="p-3 text-left text-sm font-semibold text-gray-600 tracking-wider">Invoice Date</th>
                            <th className="p-3 text-left text-sm font-semibold text-gray-600 tracking-wider">Due Date</th>
                            {/* --- Added Age Header --- */}
                            <th className="p-3 text-left text-sm font-semibold text-gray-600 tracking-wider">Age</th>
                            {/* --- End Age Header --- */}
                            <th className="p-3 text-left text-sm font-semibold text-gray-600 tracking-wider">Buyer</th>
                            <th className="p-3 text-left text-sm font-semibold text-gray-600 tracking-wider">Customer</th>
                            <th className="p-3 text-left text-sm font-semibold text-gray-600 tracking-wider">Amount</th>{/* Align Right */}
                            <th className="p-3 text-left text-sm font-semibold text-gray-600 tracking-wider">Status</th>
                            <th className="p-3 text-center text-sm font-semibold text-gray-600 tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    {/* === UPDATED TBODY to remove _doc === */}
                    <tbody className="divide-y divide-gray-200 border-t-2 border-gray-200">
                        {isFetching ? (
                            <tr><td colSpan="9" className="p-4 text-center"><LoadingModal /><p className="mt-2 text-gray-500">Loading Invoices...</p></td></tr> // Updated colspan
                        ) : filteredInvoices.length === 0 ? ( // Check FILTERED list
                            <tr><td colSpan="9" className="p-6 text-center text-sm text-gray-500">
                                {Object.values(invoiceFilters).some(v => v !== '') ? 'No invoices match the current filters.' : 'No invoices available.'} {/* Updated message */}
                            </td></tr>) : (
                            // --- MODIFIED: Map over filteredInvoices ---
                            filteredInvoices.map((invoice) => (
                                // Ensure invoice exists before rendering row
                                invoice && (
                                    <tr key={invoice._id} className="hover:bg-gray-50 transition-colors">
                                        {/* Add align-top for consistency, use direct properties */}
                                        <td className="p-3 align-top whitespace-nowrap"><button onClick={() => navigateToInvoiceDetail(invoice)} className="text-blue-600 hover:text-blue-800 hover:underline font-medium text-sm" title={`View details for ${invoice.invoiceNumber}`}>{invoice.invoiceNumber || 'N/A'}</button></td>
                                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap">{invoice.invoiceDate || 'N/A'}</td>
                                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap">{invoice.dueDate || 'N/A'}</td>
                                        {/* --- Added Age Cell --- */}
                                        <td className="p-3 align-top text-sm text-gray-600 whitespace-nowrap">{calculateInvoiceAge(invoice.uploadedAt || invoice.insertedOn)}</td>
                                        {/* --- End Age Cell --- */}
                                        <td className="p-3 align-top text-sm text-gray-700 max-w-[150px] truncate" title={invoice.supplierName || ''}>{invoice.supplierName || 'N/A'}</td>
                                        <td className="p-3 align-top text-sm text-gray-700 max-w-[150px] truncate" title={invoice.customerName || ''}>{invoice.customerName || 'N/A'}</td>
                                        <td className="p-3 align-top text-sm text-gray-900 font-semibold whitespace-nowrap">{formatAmount(invoice.totalAmount)}</td>
                                        <td className="p-3 align-top whitespace-nowrap"><StatusBadge status={invoice.status} /></td>
                                        <td className="p-3 align-top text-center whitespace-nowrap">
                                            <button
                                                onClick={() => navigateToInvoiceDetail(invoice)}
                                                className={`inline-flex items-center px-4 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-lg ${STATUS_STYLES[invoice.status]?.bg || 'bg-white'
                                                    } ${STATUS_STYLES[invoice.status]?.text || 'text-gray-700'}  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-grey-500`}
                                                title="Actions"
                                            >
                                                {STATUS_BUTTON_LABELS[invoice.status] || 'Details'}
                                            </button>
                                        </td>

                                    </tr>
                                )
                            ))
                        )}
                    </tbody>
                    {/* === END UPDATED TBODY === */}
                </table>
            </div>

            {/* --- Modals --- */}
            {/* Using the exact Modal JSX structures from your original code */}
            {/* Upload Modal */}
            {showUploadModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
                    {/* Your Original Upload Modal JSX */}
                    <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg">
                        <h3 className="text-xl font-semibold mb-4">Upload Invoice</h3>
                        <div className="space-y-4">
                            {/* ... file upload input ... */}
                            <div className="flex items-center justify-center w-full">
                                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                        <svg className="w-8 h-8 mb-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                        </svg>
                                        <p className="mb-2 text-sm text-gray-500">
                                            <span className="font-semibold">Click to upload</span> or drag and drop
                                        </p>
                                        <p className="text-sm text-gray-500">PDF (MAX. 10MB)</p>
                                    </div>
                                    <input
                                        type="file"
                                        className="hidden"
                                        accept=".pdf"
                                        onChange={handleFileUpload}
                                    />
                                </label>
                            </div>
                            {uploadedFile && (
                                <p className="text-sm text-gray-600">Selected: {uploadedFile.name}</p>
                            )}
                            {/* ... cancel/submit buttons ... */}
                            <div className="flex justify-end space-x-2">
                                <button
                                    onClick={() => {
                                        setShowUploadModal(false);
                                        setUploadedFile(null);
                                        setMindeeData(null);
                                    }}
                                    className="px-4 py-2 text-gray-500 hover:text-gray-700"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleUploadSubmit}
                                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                                    disabled={!uploadedFile || isLoading || extractingData}
                                >
                                    {isLoading ? (
                                        <span className="flex items-center">
                                            <LoadingModal />
                                        </span>
                                    ) : 'Upload'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* PDF Preview Modal (Original Structure, but renderInvoiceDetails updated) */}
            {showPdfPreview && editingInvoice && ( // Ensure modal shows only when state is true AND editingInvoice has data
                // Modal Backdrop
                <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4 overflow-y-auto"> {/* Allow backdrop scroll */}

                    {/* Modal Content Box */}
                    <div className="bg-white p-5 md:p-6 rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] flex flex-col"> {/* Use max-h and flex-col */}

                        {/* Modal Header */}
                        <div className="flex-shrink-0 flex justify-between items-center mb-4 pb-3 border-b border-gray-200">
                            <h3 className="text-lg sm:text-xl font-semibold text-gray-800">Invoice Details: {editingInvoice.invoiceNumber || 'N/A'}</h3>
                            <button
                                onClick={() => {
                                    setShowPdfPreview(false); // Close modal
                                    setPdfUrl(''); // Clear PDF URL
                                    setEditingInvoice(null); // Clear the context
                                    setVerificationComments(''); // Clear comments
                                    setViewedOfferDetails(null); // --- ADD: Clear viewed offer details
                                }}
                                className="text-gray-500 hover:text-gray-800 p-1 rounded-full hover:bg-gray-100 transition-colors"
                                aria-label="Close invoice details" // Accessibility
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        {/* Modal Body Layout (Flex container) */}
                        {/* This parent sets the height for its children */}
                        <div className="flex-grow flex flex-col md:flex-row min-h-0 gap-4 overflow-hidden"> {/* Added min-h-0, Prevent body overflow */}

                            {/* --- MODIFY: Left Pane - Conditional Content --- */}
                            {/* Check if status requires showing offer details AND if offer details were successfully fetched */}
                            {['ACCEPTED_LENDER', 'READY_FOR_DISBURSAL', 'DISBURSED', 'LOAN_IN_PROGRESS'].includes(editingInvoice.status) && viewedOfferDetails ? (
                                // --- Display Offer Details when status matches and offer data is available ---
                                <div className="w-full md:w-3/5 border border-gray-200 rounded-md overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 bg-gray-50 p-4 flex flex-col">
                                    <div className="flex-shrink-0 mb-4 pb-3 border-b border-gray-200">
                                        <h4 className="text-base font-semibold text-gray-700">Offer Details</h4>
                                    </div>
                                    <div className="space-y-3 text-sm text-gray-700 flex-grow">
                                        {/* Use fetched offer details - verify field names from API response */}
                                        <p><strong>Lender Payout (%):</strong> {viewedOfferDetails.invoiceDiscountingPercentage ?? 'N/A'}%</p>
                                        {/* Calculate and display Lender Payout Amount using invoice total amount */}
                                        <p><strong>Lender Payout Amount:</strong> {formatAmount((parseFloat(String(editingInvoice.totalAmount).replace(/[^0-9.-]+/g, "")) * (viewedOfferDetails.invoiceDiscountingPercentage / 100)) || 0)}</p>
                                        {/* Use interestRate from the offer for Service Fee */}
                                        <p><strong>Service Fee (%):</strong> {viewedOfferDetails.interestRate ?? 'N/A'}%</p>
                                        <hr className="my-2" />
                                        <p><strong>Processing Fee Type:</strong> {viewedOfferDetails.processingFee?.type ?? 'N/A'}</p> {/* Use optional chaining */}
                                        <p><strong>Processing Fee Value:</strong> {viewedOfferDetails.processingFee?.value ?? 'N/A'} {viewedOfferDetails.processingFee?.type === 'percentage' ? '%' : ' QAR'}</p> {/* Use optional chaining */}
                                        <hr className="my-2" />
                                        <p><strong>Tenure:</strong> {viewedOfferDetails.tenureDays ?? 'N/A'} Days</p>
                                        <p><strong>Repayment Frequency:</strong> {viewedOfferDetails.emiRepaymentFrequency ?? 'N/A'}</p>
                                        {/* ADD Disbursement Details if available */}
                                        {editingInvoice.status === 'DISBURSED' && viewedOfferDetails.disbursementInfo && (
                                            <>
                                                <hr className="my-2" />
                                                <h5 className="text-sm font-semibold text-gray-700 mt-4">Disbursal Details</h5>
                                                <p><strong>Disbursed Amount:</strong> {formatAmount(viewedOfferDetails.disbursementInfo.disbursedAmount)}</p>
                                                <p><strong>Disbursed On:</strong> {viewedOfferDetails.disbursementInfo.disbursedOn ? new Date(viewedOfferDetails.disbursementInfo.disbursedOn).toLocaleDateString() : 'N/A'}</p>
                                                <p><strong>UTR:</strong> {viewedOfferDetails.disbursementInfo.utr || 'N/A'}</p>
                                            </>
                                        )}
                                    </div>
                                    {/* --- ADD: Links Section --- */}
                                    <div className="flex-shrink-0 mt-4 pt-3 border-t border-gray-200 space-y-3">
                                        <h4 className="text-sm font-semibold text-gray-700">Documents</h4>
                                        {/* Use signedUrl from invoiceContract within the offer object */}
                                        {viewedOfferDetails.invoiceContract?.signedUrl ? (
                                            <a
                                                href={viewedOfferDetails.invoiceContract.signedUrl}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:underline text-sm flex items-center"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                                                </svg>
                                                View Invoice Contract
                                            </a>
                                        ) : (
                                            <p className="text-xs text-gray-500">Invoice Contract link not available.</p>
                                        )}
                                        {/* Use signedUrl from the editingInvoice state for the original invoice PDF */}
                                        {editingInvoice.signedUrl ? (
                                            <a
                                                href={editingInvoice.signedUrl}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:underline text-sm flex items-center"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                                                </svg>
                                                View Invoice PDF
                                            </a>
                                        ) : (
                                            <p className="text-xs text-gray-500">Invoice PDF link not available.</p>
                                        )}
                                    </div>
                                </div>
                                // --- END Display Offer Details ---
                            ) : pdfUrl ? (
                                // --- Existing PDF Iframe (shown for other statuses or if offer details not loaded) ---
                                <div className="w-full md:w-3/5 border border-gray-200 rounded-md overflow-hidden flex flex-col bg-gray-100">
                                    <div className="flex-shrink-0 p-2 bg-gray-200 border-b border-gray-300">
                                        <p className="text-sm font-medium text-gray-700">Invoice Document</p>
                                    </div>
                                    <iframe
                                        src={pdfUrl}
                                        className="w-full flex-grow border-0 min-h-0"
                                        title="Invoice PDF Preview"
                                    />
                                </div>
                                // --- End Existing PDF Iframe ---
                            ) : (
                                // Fallback if no PDF URL is available and not showing offer details
                                <div className="w-full md:w-3/5 border border-gray-200 rounded-md flex items-center justify-center flex-grow text-gray-500 p-10">
                                    Invoice preview is not available.
                                </div>
                            )}
                            {/* --- END MODIFY: Left Pane --- */}


                            {/* Right Pane: Details (Scrollable) */}
                            {/* Keep this as is, rendering invoice details */}
                            <div className="w-full md:w-2/5 h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 md:pr-2">
                                {renderInvoiceDetails(editingInvoice)}
                            </div>

                        </div> {/* End Modal Body Layout */}
                    </div> {/* End Modal Content Box */}
                </div> // End Modal Backdrop
            )}
            {/* Accept Offer Modal (Original Structure) */}
            {showAcceptModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
                    <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg">
                        <h3 className="text-xl font-semibold mb-4">Accept Invoice for Discounting</h3>

                        {/* --- MODIFICATION START: Conditional Content --- */}
                        {isFetchingCreditLine ? (
                            <div className="py-10"> {/* Add padding for loader */}
                                <LoadingModal />
                            </div>
                        ) : creditLineFetchError ? (
                            <div className="py-10 text-center">
                                <p className="text-red-600 font-semibold">Error:</p>
                                <p className="text-red-500 mt-1">{creditLineFetchError}</p>
                                <p className="text-sm text-gray-500 mt-3">Please enter offer details manually or try again later.</p>
                                {/* Optionally, show the form anyway below the error, or just the cancel button */}
                                {/* Render the form below error message */}
                                <div className="space-y-4 mt-4">
                                    {/* Re-render form fields here if needed, or just Cancel button */}
                                    {/* ... (Form fields copied from below) ... */}
                                </div>
                                <div className="flex justify-end space-x-2 pt-4 border-t mt-5">
                                    <button type="button" onClick={() => setShowAcceptModal(false)} className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                                    {/* Maybe hide 'Next' button if error prevents proceeding? */}
                                </div>
                            </div>
                        ) : (
                            // --- Original Form Content ---
                            <div className="space-y-4">
                                {/* Discounting % Input */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Lender Payout Percentage (%)</label>
                                    <input
                                        type="number"
                                        value={acceptOfferDetails.invoiceDiscountingPercentage} // Value comes from state
                                        onChange={(e) => handleDiscountPercentageChange(parseFloat(e.target.value))}
                                        className="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                        required min="0" max="100" placeholder="e.g., 80" // Placeholder updated
                                    />
                                    {/* ... (Validation messages and calculated amount) ... */}
                                    {acceptOfferDetails.invoiceDiscountingPercentage === '' && (<p className="mt-1 text-red-500 text-sm">Required</p>)}
                                    {editingInvoice && (<p className="mt-2 text-sm text-gray-600">Lender Payout Amount: {calculateDiscountedAmount()}</p>)}
                                </div>
                                {/* Processing Fee Type */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Processing Fee Type</label>
                                    <select
                                        value={acceptOfferDetails.processingFeeType} // Value comes from state
                                        onChange={(e) => setAcceptOfferDetails({ ...acceptOfferDetails, processingFeeType: e.target.value, processingFeeValue: '' })}
                                        className="mt-1 w-full p-2 border border-gray-300 rounded-md" required>
                                        <option value="percentage">Percentage (%)</option>
                                        <option value="flat">Flat (QAR)</option>
                                    </select>
                                </div>
                                {/* Processing Fee Value */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Processing Fee Value</label>
                                    <input
                                        type="number"
                                        value={acceptOfferDetails.processingFeeValue} // Value comes from state
                                        onChange={handleProcessingFeeValueChange}
                                        className="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                        min="0" step={acceptOfferDetails.processingFeeType === 'percentage' ? '0.01' : '1'}
                                    />
                                    <p className="text-sm text-gray-500 mt-1">{acceptOfferDetails.processingFeeType === 'percentage' ? 'Max 10%' : 'Max QAR 10,000'}</p>
                                </div>
                                {/* Tenure */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Tenure (Days)</label>
                                    <input
                                        type="number"
                                        value={acceptOfferDetails.tenureDays} // Value comes from state
                                        onChange={handleTenureDaysChange}
                                        className="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                        min="1" placeholder="Days until repayment"
                                    />
                                </div>
                                {/* Repayment Frequency */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Repayment Frequency</label>
                                    <select
                                        value={acceptOfferDetails.emiRepaymentFrequency} // Value comes from state
                                        onChange={handleEmiRepaymentFrequencyChange}
                                        className="mt-1 w-full p-2 border border-gray-300 rounded-md">
                                        <option value="MONTHLY">Monthly</option>
                                        <option value="WEEKLY">Weekly</option>
                                        <option value="DAILY">Daily</option>
                                    </select>
                                </div>
                                {/* Buttons */}
                                <div className="flex justify-end space-x-2 pt-4 border-t mt-5">
                                    <button type="button" onClick={() => setShowAcceptModal(false)} className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                                    <button type="button" onClick={() => { if (isFormValid()) { setShowConfirmationModal(true); } else { alert("Please fill required fields correctly."); } }} className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400" disabled={isLoading || !isFormValid()}> {isLoading ? 'Processing...' : 'Next'} </button>
                                </div>
                            </div>
                        )}
                        {/* --- MODIFICATION END --- */}
                    </div>
                </div>
            )}

            {/* Confirmation Modal (Original Structure) */}
            {showConfirmationModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
                    {/* Your Original Confirmation Modal JSX */}
                    <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg">
                        <h3 className="text-xl font-semibold mb-4">Confirm Invoice Discounting Offer</h3>
                        <p className="mb-4 text-sm text-gray-600">Review the details before confirming:</p>
                        {/* Details Summary */}
                        <div className="space-y-2 bg-gray-50 p-4 rounded border text-sm mb-6">
                            <p><strong>Lender Payout (%):</strong> {acceptOfferDetails.invoiceDiscountingPercentage}%</p>
                            <p><strong>Lender Payout Amount:</strong> {calculateDiscountedAmount()}</p>
                            <p><strong>Service Fee  (%):</strong> {interestRate ?? 'Inherited from credit line'}</p>
                            <hr className="my-2" />
                            <p><strong>Processing Fee Type:</strong> {acceptOfferDetails.processingFeeType}</p>
                            <p><strong>Processing Fee Value:</strong> {acceptOfferDetails.processingFeeValue}{acceptOfferDetails.processingFeeType === 'percentage' ? '%' : ' QAR'}</p>
                            <hr className="my-2" />
                            <p><strong>Tenure:</strong> {acceptOfferDetails.tenureDays} Days</p>
                            <p><strong>Repayment Frequency:</strong> {acceptOfferDetails.emiRepaymentFrequency}</p>
                        </div>
                        {/* Buttons */}
                        <div className="flex justify-end space-x-2 pt-4">
                            <button onClick={() => setShowConfirmationModal(false)} className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200" disabled={isLoading}>Cancel</button>
                            <button onClick={handleAcceptInvoice} className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50" disabled={isLoading}>{isLoading ? 'Confirming...' : 'Confirm & Create Offer'}</button>
                        </div>
                    </div>
                </div>
            )}

        </div> // End Main Page Div
    );
};

export default InvoiceApprovals;
import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios'; // Using axios for offer and update API calls
import { Disclosure } from '@headlessui/react';
import { ArrowTopRightOnSquareIcon, CheckIcon, ChevronUpIcon, DocumentIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useMemo } from 'react'; // Add useMemo if not already imported
import J<PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import StatusBadge from '../../components/StatusBadge';
import config from "../../../config.json";
import { PDFDocument, StandardFonts, rgb, degrees } from 'pdf-lib';
import { useRouter } from 'next/router';
import * as XLSX from 'xlsx'; // <-- ADD THIS IMPORT
import LoadingModal from '../../components/Loading';

const CREDIT_LINE_STATUS_ORDER = {
    'UNDER_REVIEW': 1,
    'DRAFT': 2,       // Assuming DRAFT might appear in kycStatus or derived status
    'ON_HOLD': 3,
    'APPROVED': 4,    // Typically a KYC status before CL becomes ACTIVE
    'ACTIVE': 5,      // Typically a CreditLineStatus
    'SUSPENDED': 6,   // Can be a CreditLineStatus or a review action status
    'EXPIRED': 7,
    'REJECTED': 8,
    'PENDING': 1.5,   // Example: if KYC PENDING should come after UNDER_REVIEW but before DRAFT
    'INFO_NEEDED': 2.5, // Example: if KYC INFO_NEEDED comes after DRAFT
};
const DEFAULT_CREDIT_LINE_STATUS_PRIORITY = Math.max(...Object.values(CREDIT_LINE_STATUS_ORDER), 0) + 1;

// --- Constants for Processing Fee ---
const MAX_PROCESSING_FEE_FLAT = 500;
const MAX_PROCESSING_FEE_PERCENTAGE = 5; // 5%

const AssessmentFilterSection = ({ filters, setFilters, resetFilters }) => {
    // Input styling (same as before)
    const inputBaseClass = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
    const numberInputClass = `${inputBaseClass} px-2 py-1`;
    const dateInputClass = `${inputBaseClass} px-2 py-1`;
    const textInputClass = `${inputBaseClass} px-3 py-1.5`;
    const selectInputClass = `${inputBaseClass} px-3 py-2`; // Specific style for selects
    //uat comment
    const handleFilterChange = (event) => {
        const { name, value } = event.target;
        setFilters(prevFilters => ({
            ...prevFilters,
            [name]: value,
        }));
    };

    // Calculate active filters
    const activeFilterCount = useMemo(() => {
        return Object.values(filters).filter(value => value !== '' && value !== null && value !== undefined).length;
    }, [filters]);

    const handleReset = (event) => {
        event.stopPropagation();
        resetFilters();
    };

    // Define options based on your schema enums
    const offerStatusOptions = [
        'PENDING', 'ACCEPTED', 'LOAN_CONTRACT_ACCEPTED', 'INITIATED_FUND_TRANSFER',
        'READY_FOR_DISBURSAL', 'LOAN_IN_PROGRESS', 'PAID', 'LOAN_CANCELLED',
        'REJECTED', 'EXPIRED', 'WRITTEN_OFF', 'DEFAULTED', 'OVERDUE'
    ];
    const creditLineStatusOptions = [
        'DRAFT', 'ON_HOLD', 'UNDER_REVIEW', 'APPROVED', 'REJECTED',
        'ACTIVE', 'SUSPENDED', 'EXPIRED'
    ];
    // Note: KYC Status might come from User schema, define options accordingly
    const kycStatusOptions = [
        'APPROVED', 'VERIFIED', 'REJECTED', 'PENDING', 'UNDER_REVIEW',
        'INFO_NEEDED', 'REVIEW', 'SUBMITTED', 'INITIATED', 'REINITIATED', 'N/A'
    ];


    return (
        <div className="mb-6"> {/* Margin below the whole filter section */}
            <Disclosure as="div" className="border border-gray-200 rounded-lg shadow-sm bg-white">
                {({ open }) => (
                    <>
                        {/* The Filter Header Bar */}
                        <div className="flow-root">
                            <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus-visible:ring focus-visible:ring-indigo-500 focus-visible:ring-opacity-75">
                                <span className="flex items-center">
                                    <FunnelIcon className="mr-2 h-5 w-5 text-gray-400" aria-hidden="true" />
                                    Filters
                                    {activeFilterCount > 0 && (
                                        <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-sm font-medium text-gray-800">
                                            {activeFilterCount}
                                        </span>
                                    )}
                                </span>
                                <span className="ml-6 flex items-center">
                                    <ChevronUpIcon
                                        className={`${open ? 'rotate-180 transform' : ''} h-5 w-5 text-gray-500 transition-transform duration-150 ease-in-out`}
                                    />
                                </span>
                            </Disclosure.Button>
                        </div>

                        {/* Separator */}
                        {open && <div className="border-t border-gray-200"></div>}

                        {/* Panel with Filter Inputs */}
                        <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
                            {/* Top row: Search & Clear */}
                            <div className="mb-6 flex items-start justify-between gap-4"> {/* Increased bottom margin */}
                                <div className="flex-1"> {/* Search takes most space */}
                                    <label htmlFor="assessSearchTerm" className="sr-only">Search (Name)</label>
                                    <input
                                        type="text" name="searchTerm" id="assessSearchTerm"
                                        value={filters.searchTerm} onChange={handleFilterChange}
                                        className={textInputClass} placeholder="Search Business or Borrower..."
                                    />
                                </div>
                                <button
                                    type="button" onClick={handleReset}
                                    className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                >
                                    <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" aria-hidden="true" />
                                    Clear all
                                </button>
                            </div>

                            {/* Grid for other filters - Adjust grid columns as needed */}
                            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">

                                {/* Applied Date Range */}
                                <div className="sm:col-span-2 md:col-span-2 lg:col-span-2">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Applied Date Range</label>
                                    <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:items-center sm:space-x-2">
                                        <input type="date" name="startDate" value={filters.startDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} aria-label="Start Date" />
                                        <span className="text-gray-500 text-center hidden sm:inline">to</span>
                                        <input type="date" name="endDate" value={filters.endDate} onChange={handleFilterChange} className={dateInputClass + ' flex-1'} aria-label="End Date" />
                                    </div>
                                </div>

                                {/* Offer Credit Limit Range */}
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Offer Limit (QAR)</label>
                                    <div className="flex space-x-2">
                                        <input type="number" name="minOfferLimit" value={filters.minOfferLimit} onChange={handleFilterChange} placeholder="Min" className={numberInputClass} />
                                        <input type="number" name="maxOfferLimit" value={filters.maxOfferLimit} onChange={handleFilterChange} placeholder="Max" className={numberInputClass} />
                                    </div>
                                </div>

                                {/* Offer Status Dropdown */}
                                <div>
                                    <label htmlFor="offerStatus" className="block text-sm font-medium text-gray-700 mb-1">Offer Status</label>
                                    <select id="offerStatus" name="offerStatus" value={filters.offerStatus} onChange={handleFilterChange} className={selectInputClass}>
                                        <option value="">All Statuses</option>
                                        {offerStatusOptions.map(status => <option key={status} value={status}>{status.replace('_', ' ')}</option>)}
                                    </select>
                                </div>

                                {/* Credit Line Status Dropdown */}
                                <div>
                                    <label htmlFor="creditLineStatus" className="block text-sm font-medium text-gray-700 mb-1">Credit Line Status</label>
                                    <select id="creditLineStatus" name="creditLineStatus" value={filters.creditLineStatus} onChange={handleFilterChange} className={selectInputClass}>
                                        <option value="">All Statuses</option>
                                        {creditLineStatusOptions.map(status => <option key={status} value={status}>{status.replace('_', ' ')}</option>)}
                                    </select>
                                </div>

                                {/* KYC Status Dropdown */}
                                <div>
                                    <label htmlFor="kycStatus" className="block text-sm font-medium text-gray-700 mb-1">KYC Status</label>
                                    <select id="kycStatus" name="kycStatus" value={filters.kycStatus} onChange={handleFilterChange} className={selectInputClass}>
                                        <option value="">All Statuses</option>
                                        {kycStatusOptions.map(status => <option key={status} value={status}>{status.replace('_', ' ')}</option>)}
                                    </select>
                                </div>

                                {/* Add more filters here if needed */}

                            </div>
                        </Disclosure.Panel>
                    </>
                )}
            </Disclosure>
            {/* REMOVED ActiveFilterTags component */}
        </div>
    );
};


// --- Helper Function: Calculate Age ---
// Returns a string like "X days ago", "Y hours ago", etc.
const calculateAge = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const startDate = new Date(dateString);
        if (isNaN(startDate.getTime())) return 'Invalid Date';

        const now = new Date();
        const diffMs = now - startDate;
        const diffSeconds = Math.floor(diffMs / 1000);
        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffDays > 0) {
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        } else if (diffHours > 0) {
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        } else if (diffMinutes > 0) {
            return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
        } else {
            return `Just now`;
        }
    } catch (e) {
        console.error("Error calculating age:", dateString, e);
        return 'Error';
    }
};


const RenderInfoItemViewOnly = ({ label, value }) => {
    const displayValue = (val) => {
        if (val === null || val === undefined || val === '') {
            return <span className="text-gray-500 italic">N/A</span>;
        }
        if (typeof val === 'boolean') {
            return val ? 'Yes' : 'No';
        }
        // Add specific formatting if needed (e.g., for currency)
        return String(val);
    };

    //build check

    return (
        <div className="py-1 break-words text-sm">
            <span className="font-medium text-gray-600">{label}: </span>
            <span className="text-gray-800">{displayValue(value)}</span>
        </div>
    );
};

const RenderDocumentDisplayViewOnly = ({ documentData, label }) => {
    if (!documentData || !documentData.verificationStatus) {
        return (
            <div className="bg-white p-3 rounded-lg shadow border border-gray-200 h-full flex flex-col justify-between">
                <div>
                    <p className="text-sm font-medium text-gray-800 mb-1">{label}</p>
                    <p className="text-sm text-gray-500 italic">Not Submitted</p>
                </div>
            </div>
        );
    }

    const status = String(documentData.verificationStatus || 'N/A').toUpperCase();
    // const statusColorClasses = {
    //     VERIFIED: 'bg-green-100 text-green-800', APPROVED: 'bg-green-100 text-green-800',
    //     REJECTED: 'bg-red-100 text-red-800',
    //     PENDING: 'bg-yellow-100 text-yellow-800', REVIEW: 'bg-yellow-100 text-yellow-800', UNDER_REVIEW: 'bg-blue-100 text-blue-800', INFO_NEEDED: 'bg-yellow-100 text-yellow-800',
    //     SUBMITTED: 'bg-blue-100 text-blue-800',
    //     INITIATED: 'bg-gray-100 text-gray-600', REINITIATED: 'bg-gray-100 text-gray-600',
    //     'N/A': 'bg-gray-100 text-gray-600',
    // };
    // const statusClass = statusColorClasses[status] || statusColorClasses['N/A'];

    return (
        <div className="bg-white p-3 rounded-lg shadow border border-gray-200 h-full flex flex-col justify-between">
            <div>
                <div className="flex justify-between items-start mb-1">
                    <p className="text-sm font-medium text-gray-800 flex-1 mr-2">{label}</p>
                    <StatusBadge status={status} />
                </div>
                {documentData.signedUrl && (
                    <a
                        href={documentData.signedUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-indigo-600 hover:text-indigo-800 text-sm break-all block underline mb-1"
                        title={documentData.filePath || 'View Document'}
                    >
                        View Document ({documentData.mimeType || 'PDF'})
                    </a>
                )}
                {!documentData.signedUrl && documentData.filePath && (
                    <p className="text-sm text-gray-500 italic mb-1">File path present but no view URL.</p>
                )}
                {!documentData.signedUrl && !documentData.filePath && status !== 'NOT_SUBMITTED' && status !== 'N/A' && (
                    <p className="text-sm text-gray-500 italic mb-1">Document submitted, processing...</p>
                )}
                {documentData.uploadedOn && (
                    <p className="text-sm text-gray-500">Uploaded: {formatDate(documentData.uploadedOn)}</p>
                )}
                {documentData.verifiedOrRejectedOn && (
                    <p className="text-sm text-gray-500">Processed: {formatDate(documentData.verifiedOrRejectedOn)}</p>
                )}
            </div>
            {documentData.verificationNotes && (
                <p className="text-sm mt-2 pt-1 border-t border-dashed">Notes: {documentData.verificationNotes}</p>
            )}
        </div>
    );
};

// Utility to safely get nested properties (replace with lodash.get or similar if available)
const getNested = (obj, path, defaultValue = undefined) => {
    const properties = path.split('.');
    return properties.reduce((acc, key) => (acc && acc[key] !== undefined && acc[key] !== null ? acc[key] : defaultValue), obj);
};


// Utility to format dates (replace with a robust library like date-fns or moment if available)
const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'Invalid Date';
        // Simple formatting, adjust as needed
        const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
        return date.toLocaleDateString('en-GB', options);
    } catch (e) {
        if (e) {
            console.log(e);
        }
        return 'Invalid Date';
    }
};


// --- Main Component ---

const KycDetailsViewModal = ({ show, user, onClose, activeTabState, isGeneratingPdf, setIsGeneratingPdf }) => {
    const [activeTab, setActiveTab] = activeTabState;

    useEffect(() => {
        // Default to a relevant tab when opening
        if (show) {
            setActiveTab('userDetails'); // Start with user details
        }
    }, [show, setActiveTab]);

    if (!show || !user) {
        return null;
    }

    console.log("USER HERE", user);

    // Define document field mappings based on the ROOT user object structure
    const rootBusinessDocFields = [
        { key: 'commercialRegistration', label: 'Commercial Registration (CR)' },
        { key: 'tradeLicense', label: 'Trade License' },
        { key: 'taxCard', label: 'Tax Card' },
        { key: 'establishmentCard', label: 'Establishment Card' },
        { key: 'memorandumOfAssociation', label: 'Memorandum of Association (MOA)' },
        { key: 'articleOfAssociation', label: 'Articles of Association (AOA)' },
        { key: 'otherDocument', label: 'Additional Document 1' },
        { key: 'otherDocumentTwo', label: 'Additional Document 2' },
        { key: 'otherDocument3', label: 'Additional Document 3' },
        { key: 'otherDocument4', label: 'Additional Document 4' },
        { key: 'otherDocument5', label: 'Additional Document 5' },
        { key: 'otherDocument6', label: 'Additional Document 6' },
        { key: 'otherDocument7', label: 'Additional Document 7' },
        { key: 'otherDocument8', label: 'Additional Document 8' },
        { key: 'otherDocument9', label: 'Additional Document 9' },
        { key: 'otherDocument10', label: 'Additional Document 10' },
    ];

    const rootFinancialDocFields = [
        { key: 'bankStatement', label: 'Bank Statements' },
        { key: 'auditedFinancialReport', label: 'Audited Financial Report' },
        { key: 'commercialCreditReport', label: 'Commercial Credit Report (CCR)' },
        { key: 'cashFlowLedger', label: 'Cash Flow Ledger / Mgmt Accounts' }, // Labelled as Ledger in data
        // { key: 'cashFlowDocument', label: 'Cash Flow Statement Doc' }, // Raw statement upload
    ];

    // Note: Primary Applicant QID/Passport/Utility bill are now under KYC Details Tab

    // Define tabs with more descriptive names
    const tabs = [
        { key: 'userDetails', label: 'User & KYC Details' },
        { key: 'businessDetails', label: 'Business Details' },
        { key: 'businessDocs', label: 'Business Documents' },
        { key: 'financialDocs', label: 'Financial Documents' },
        { key: 'shareholders', label: 'Shareholders' },
        // { key: 'directorsSignatories', label: 'Directors & Signatories' }, // Combine if needed
        { key: 'buyers', label: 'Top Buyers' },
        { key: 'verification', label: 'Overall Verification' },
    ];

    const getOverallKycStatus = () => {
        const status = String(getNested(user, 'kyc.verificationStatus', 'N/A')).toUpperCase();
        return <StatusBadge status={status} />;
    };

    const handleDownloadDocumentsZip = async () => {
        if (!user) return;

        // Rename state variable for clarity if desired (optional, but good practice)
        // For this example, we'll keep isGeneratingPdf but update the button text
        setIsGeneratingPdf(true); // Reuse state, indicates processing
        console.log("Starting document zip generation...");

        const zip = new JSZip();
        const documentsToFetch = []; // Renamed for clarity
        const failedDocs = [];

        // --- Helper function to sanitize filenames ---
        const sanitizeFilename = (name) => {
            // Remove or replace characters invalid in Windows/Mac/Linux filenames
            // Including / \ : * ? " < > | and control characters
            // Replace multiple spaces/underscores with a single underscore
            if (!name) return 'unnamed_document';
            return name
                .replace(/[\/\\:*?"<>|]/g, '_') // Replace invalid chars with underscore
                .replace(/[\x00-\x1f\x7f]/g, '_') // Replace control chars
                .replace(/_+/g, '_') // Collapse multiple underscores
                .replace(/\s+/g, '_') // Replace spaces with underscores
                .replace(/^_|_$/g, ''); // Trim leading/trailing underscores
        };

        // --- Reusable function to add document details to the list ---
        // Updated to store necessary info for ZIP naming
        const addDocToList = (path, categoryLabel, labelPrefix = '') => {
            // Ensure getNested is accessible in this scope
            const docData = getNested(user, path, null);
            if (docData && docData.signedUrl && docData.filePath) {
                const uploadedFileName = docData.filePath.split('/').pop() || `file_${path.replace(/\./g, '_')}`;
                // Construct the base document name (can adjust logic here)
                const docName = labelPrefix + path.split('.').pop(); // e.g., "Shareholder 1 passport" or "commercialRegistration"

                documentsToFetch.push({
                    url: docData.signedUrl,
                    // Construct the final filename for the ZIP entry
                    zipFileName: `${sanitizeFilename(docName)}_${sanitizeFilename(uploadedFileName)}`,
                    originalPath: path, // For debugging
                    label: docName,      // Keep label for potential error messages
                    uploadedFileName: uploadedFileName,
                });
                console.log(`Scheduled fetch for: ${uploadedFileName} (as ${docName}) from ${path}`);
            } else {
                console.log(`Document not found or no URL/filePath for path: ${path}`);
                // Optionally add to failedDocs here if missing is considered a failure
                // failedDocs.push(`${path} (Missing or invalid data)`);
            }
        };

        // --- Define Document Fields (Keep your existing definitions) ---
        const businessDocFields = [
            { key: 'commercialRegistration', label: 'Commercial Registration', isRequired: true },
            { key: 'tradeLicense', label: 'Trade License', isRequired: true },
            { key: 'taxCard', label: 'Tax Card', isRequired: true },
            { key: 'establishmentCard', label: 'Establishment Card', isRequired: true },
            { key: 'memorandumOfAssociation', label: 'Memorandum of Association', isRequired: true },
            { key: 'articleOfAssociation', label: 'Article of Association', isRequired: true },
            { key: 'commercialCreditReport', label: 'Commercial Credit Report', isRequired: false },
            { key: 'otherDocument', label: 'Other Document 1', isRequired: false },
            { key: 'otherDocumentTwo', label: 'Other Document 2', isRequired: false },
            { key: 'otherDocument3', label: 'Other Document 3', isRequired: false },
            { key: 'otherDocument4', label: 'Other Document 4', isRequired: false },
            { key: 'otherDocument5', label: 'Other Document 5', isRequired: false },
            { key: 'otherDocument6', label: 'Other Document 6', isRequired: false },
            { key: 'otherDocument7', label: 'Other Document 7', isRequired: false },
            { key: 'otherDocument8', label: 'Other Document 8', isRequired: false },
            { key: 'otherDocument9', label: 'Other Document 9', isRequired: false },
            { key: 'otherDocument10', label: 'Other Document 10', isRequired: false },
        ];
        const financialDocFields = [
            { key: 'bankStatement', label: 'Bank Statement (Last 6 months)', isRequired: true },
            { key: 'auditedFinancialReport', label: 'Audited Financial Report (12mo)', isRequired: true },
            { key: 'cashFlowLedger', label: 'Cash Flow / Ledger', isRequired: true },
        ];
        // --- End Document Fields ---


        // --- Populate the list of documents to fetch ---
        console.log("Collecting document paths...");

        businessDocFields.forEach(f => addDocToList(f.key, 'Business Documents', f.label + ' '));
        financialDocFields.forEach(f => addDocToList(f.key, 'Financial Documents', f.label + ' '));
        addDocToList('kyc.qatariId', 'KYC Personal', 'Qatari ID ');
        addDocToList('kyc.passport', 'KYC Personal', 'Passport ');
        addDocToList('kyc.utilityBill', 'KYC Personal', 'Utility Bill ');
        addDocToList('kyc.incomeDetails.proofOfIncome', 'KYC Employment/Income', 'Proof of Income ');

        (getNested(user, 'shareholders', []) || []).forEach((sh, index) => {
            const prefix = `Shareholder ${index + 1} `;
            addDocToList(`shareholders.${index}.passport`, 'Shareholders', prefix + 'Passport ');
            addDocToList(`shareholders.${index}.qid`, 'Shareholders', prefix + 'QID ');
            addDocToList(`shareholders.${index}.proofOfAddress`, 'Shareholders', prefix + 'Proof of Address ');
        });

        // (getNested(user, 'kyc.directors', []) || []).forEach((dir, index) => {
        //     addDocToList(`kyc.directors.${index}.idDocument`, 'Directors', `Director ${index + 1} ID Document `);
        // });

        (getNested(user, 'authorizedSignatories', []) || []).forEach((sig, index) => {
            addDocToList(`authorizedSignatories.${index}.idDocument`, 'Authorized Signatories', `Signatory ${index + 1} ID Document `);
        });

        (getNested(user, 'beneficialOwners', []) || []).forEach((owner, index) => {
            addDocToList(`beneficialOwners.${index}.idDocument`, 'Beneficial Owners', `Owner ${index + 1} ID Document `);
        });

        (getNested(user, 'kyc.buyers', []) || []).forEach((buyer, index) => {
            addDocToList(`kyc.buyers.${index}.companyDocument`, 'Buyers', `Buyer ${index + 1} Company Document `);
        });

        console.log(`Found ${documentsToFetch.length} documents to potentially download.`);

        // --- Fetch and Add Documents to ZIP ---
        if (documentsToFetch.length > 0) {
            console.log("Fetching documents and adding to ZIP...");
            const results = await Promise.allSettled(
                documentsToFetch.map(async (docInfo) => {
                    try {
                        console.log(`Downloading (KycDetailsViewModal): ${docInfo.uploadedFileName} (URL: ${docInfo.url})`);
                        const response = await fetch(docInfo.url);
                        if (!response.ok) {
                            throw new Error(`Download failed for ${docInfo.uploadedFileName} with status ${response.status}`);
                        }
                        let fileBlob = await response.blob(); // <-- Changed from blob to fileBlob for clarity

                        // --- WATERMARKING LOGIC START ---
                        const isPdf = docInfo.zipFileName.toLowerCase().endsWith('.pdf') || fileBlob.type === 'application/pdf';

                        if (isPdf) {
                            console.log(`Attempting to watermark (KycDetailsViewModal): ${docInfo.zipFileName}`);
                            try {
                                const arrayBuffer = await fileBlob.arrayBuffer();
                                const pdfDoc = await PDFDocument.load(arrayBuffer);
                                const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

                                // Use 'user' prop for watermark text details
                                const watermarkText = `Restricted`;

                                const pages = pdfDoc.getPages();
                                for (const page of pages) {
                                    const { width, height } = page.getSize();
                                    page.drawText(watermarkText, {
                                        x: width / 2 - (watermarkText.length * 3), // Adjust for centering
                                        y: height / 2,
                                        size: 32,
                                        font: helveticaFont,
                                        color: rgb(0.6, 0.6, 0.6),
                                        opacity: 0.4,
                                        rotate: degrees(-45),
                                    });
                                }
                                const pdfBytes = await pdfDoc.save();
                                fileBlob = new Blob([pdfBytes], { type: 'application/pdf' }); // Update fileBlob with watermarked version
                                console.log(`Successfully watermarked (KycDetailsViewModal): ${docInfo.zipFileName}`);
                            } catch (watermarkError) {
                                console.error(`Failed to apply watermark to ${docInfo.zipFileName} (KycDetailsViewModal):`, watermarkError);
                                failedDocs.push(`${docInfo.zipFileName} (Watermark failed: ${watermarkError.message})`);
                                // Original fileBlob will be used if watermarking fails
                            }
                        }
                        // --- WATERMARKING LOGIC END ---

                        if (fileBlob.size === 0) {
                            throw new Error(`Downloaded empty file for ${docInfo.uploadedFileName}`);
                        }
                        console.log(`Adding to zip (KycDetailsViewModal): ${docInfo.zipFileName}, size: ${fileBlob.size} bytes`);
                        zip.file(docInfo.zipFileName, fileBlob);
                        return { status: 'fulfilled', path: docInfo.originalPath };
                    } catch (error) {
                        console.error(`Failed processing ${docInfo.uploadedFileName} (Path: ${docInfo.originalPath}, KycDetailsViewModal):`, error);
                        return { status: 'rejected', path: docInfo.originalPath, reason: error.message || 'Unknown error' };
                    }
                })
            );

            results.forEach(result => {
                if (result.status === 'rejected') {
                    const failedDocInfo = documentsToFetch.find(d => d.originalPath === result.path);
                    failedDocs.push(`${failedDocInfo?.zipFileName || result.path} (Error: ${result.reason})`);
                }
            });

            if (failedDocs.length > 0) {
                console.warn("Some documents failed to download or add to zip:", failedDocs);
                alert(`Warning: Could not download or add the following documents to the ZIP:\n\n${failedDocs.join('\n')}\n\nThe ZIP file will be generated with the successfully downloaded documents.`);
            }

            console.log("Generating ZIP file...");
            try {
                const zipBlob = await zip.generateAsync(
                    {
                        type: 'blob',
                        compression: "DEFLATE",
                        compressionOptions: { level: 6 }
                    },
                    (metadata) => {
                        if (metadata) { console.log(metadata.percent.toFixed(2) + ' %'); } // Optional progress feedback
                    }
                );

                // Ensure getNested is accessible here too
                const zipFileName = `kyc_documents_${sanitizeFilename(getNested(user, 'firstName', 'user'))}_${sanitizeFilename(getNested(user, 'lastName', ''))}_${user._id}.zip`;
                saveAs(zipBlob, zipFileName); // Use saveAs here
                console.log("ZIP generation complete and download initiated.");

            } catch (zipError) {
                console.error('Error generating ZIP file:', zipError);
                alert(`Failed to generate the final ZIP file: ${zipError.message}`);
            }

        } else {
            console.log("No documents found to include in the ZIP.");
            alert("No documents were found for this user to download.");
        }

        setIsGeneratingPdf(false); // Reset loading state
    };

    const getShareholderStatusClass = (status) => {
        const upperStatus = String(status || 'INITIATED').toUpperCase();
        if (['APPROVED', 'VERIFIED'].includes(upperStatus)) { return 'bg-green-100 text-green-800'; }
        else if (upperStatus === 'REJECTED') { return 'bg-red-100 text-red-800'; }
        else if (['PENDING', 'UNDER_REVIEW', 'INFO_NEEDED', 'SUBMITTED'].includes(upperStatus)) { return 'bg-yellow-100 text-yellow-800'; }
        else { return 'bg-blue-100 text-blue-800'; } // Default for INITIATED etc.
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-start z-50 p-4 pt-10 overflow-y-auto">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">

                {/* Modal Header */}
                <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10 flex-shrink-0">
                    <h2 className="text-xl font-semibold text-gray-800">
                        User Details: {getNested(user, 'firstName', '')} {getNested(user, 'lastName', '')}
                        <span className="text-sm text-gray-500 ml-2">(ID: {user._id})</span>
                    </h2>
                    <div className="flex items-center space-x-2 flex-shrink-0"> {/* Button container */}

                        {/* --- PASTE THE BUTTON JSX HERE --- */}
                        <button
                            onClick={handleDownloadDocumentsZip} // Connects to the zip download function
                            disabled={isGeneratingPdf} // Disables button during processing
                            className={`inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 transition duration-150 ease-in-out ${isGeneratingPdf ? 'opacity-50 cursor-not-allowed' : ''}`}
                            title="Download All KYC Documents as ZIP"
                        >
                            {isGeneratingPdf ? (
                                <>
                                    <svg className="animate-spin -ml-0.5 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Processing... {/* Loading text */}
                                </>
                            ) : (
                                <>
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                        {/* Icon suggestion: Download or Archive/Zip icon */}
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                    </svg>
                                    Download Documents (ZIP) {/* Button text */}
                                </>
                            )}
                        </button>
                        {/* --- END DOWNLOAD BUTTON --- */}

                        {/* Add your Close button or other header controls here */}
                        {/* <button onClick={onClose} className="...">Close</button> */}

                    </div>
                </div>

                {/* Modal Content Area (Scrollable) */}
                <div className="flex-grow overflow-y-auto">
                    {/* Tab Navigation */}
                    <div className="px-6 border-b border-gray-200 sticky top-0 bg-white z-[9]">
                        <nav className="-mb-px flex space-x-4 overflow-x-auto" aria-label="User Detail Tabs">
                            {tabs.map(tab => (
                                <button
                                    key={tab.key}
                                    className={`whitespace-nowrap py-3 px-2 border-b-2 font-medium text-sm transition-colors duration-150 ${activeTab === tab.key ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
                                    onClick={() => setActiveTab(tab.key)}
                                >
                                    {tab.label}
                                </button>
                            ))}
                        </nav>
                    </div>

                    {/* Tab Content Display Area */}
                    <div className="p-4 sm:p-6 bg-gray-50">

                        {/* Tab: User & KYC Details */}
                        {activeTab === 'userDetails' && (
                            <div className="space-y-6">
                                {/* Basic User Info */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Primary User Information</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="First Name" value={getNested(user, 'firstName')} />
                                        <RenderInfoItemViewOnly label="Middle Name" value={getNested(user, 'middleName')} />
                                        <RenderInfoItemViewOnly label="Last Name" value={getNested(user, 'lastName')} />
                                        <RenderInfoItemViewOnly label="Email" value={getNested(user, 'email')} />
                                        <RenderInfoItemViewOnly label="Mobile Number" value={getNested(user, 'mobileNo')} />
                                        <RenderInfoItemViewOnly label="Account Status" value={getNested(user, 'isActive')} />
                                    </div>
                                </div>

                                {/* KYC Personal Address */}
                                {/* <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">KYC - Personal Address</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="Address Line 1" value={getNested(user, 'kyc.addressLine1')} />
                                        <RenderInfoItemViewOnly label="Address Line 2" value={getNested(user, 'kyc.addressLine2')} />
                                        <RenderInfoItemViewOnly label="City" value={getNested(user, 'kyc.city')} />
                                        <RenderInfoItemViewOnly label="State" value={getNested(user, 'kyc.state')} />
                                        <RenderInfoItemViewOnly label="Postal Code" value={getNested(user, 'kyc.postalCode')} />
                                        <RenderInfoItemViewOnly label="Country" value={getNested(user, 'kyc.country')} />
                                    </div>
                                </div> */}


                                {/* KYC Income Details */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Bank Details</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="Account Number" value={getNested(user, 'kyc.incomeDetails.accountNumber')} />
                                        <RenderInfoItemViewOnly label="IBAN" value={getNested(user, 'kyc.incomeDetails.ifscCode')} />
                                        {/* Add other income details if available */}
                                    </div>
                                </div>

                                {/* KYC Employment Details (if any) */}
                                {Object.keys(getNested(user, 'kyc.employmentDetails', {})).length > 0 && (
                                    <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">KYC - Employment Details</h3>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                            {/* Map through employmentDetails keys dynamically or add specific fields */}
                                            <RenderInfoItemViewOnly label="Employer Name" value={getNested(user, 'kyc.employmentDetails.employerName')} />
                                            <RenderInfoItemViewOnly label="Position" value={getNested(user, 'kyc.employmentDetails.position')} />
                                            {/* ... etc */}
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Tab: Business Details */}
                        {activeTab === 'businessDetails' && (
                            <div className="space-y-6">
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Business Information</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="Business Name" value={getNested(user, 'kyc.businessDetails.businessName')} />
                                        <RenderInfoItemViewOnly label="Legal Entity Name" value={getNested(user, 'kyc.businessDetails.legalEntityName')} />
                                        <RenderInfoItemViewOnly label="Establishment Name" value={getNested(user, 'kyc.businessDetails.establishmentName')} />
                                        <RenderInfoItemViewOnly label="Legal Form" value={getNested(user, 'kyc.businessDetails.legalForm')} />
                                        <RenderInfoItemViewOnly label="Ownership Type" value={getNested(user, 'kyc.businessDetails.ownershipType')} />
                                        <RenderInfoItemViewOnly label="Sector" value={getNested(user, 'kyc.businessDetails.sector')} />
                                        <RenderInfoItemViewOnly label="Firm Nationality" value={getNested(user, 'kyc.businessDetails.firmNationality')} />
                                        <RenderInfoItemViewOnly label="CR Number" value={getNested(user, 'kyc.businessDetails.crNumber')} />
                                        <RenderInfoItemViewOnly label="CR Issue Date" value={formatDate(getNested(user, 'kyc.businessDetails.crIssueDate'))} />
                                        <RenderInfoItemViewOnly label="CR Expiry Date" value={formatDate(getNested(user, 'kyc.businessDetails.crExpiryDate'))} />
                                        <RenderInfoItemViewOnly label="Trade License (TL) Number" value={getNested(user, 'licenseNumber')} /> {/* From root */}
                                        <RenderInfoItemViewOnly label="TL Issue Date" value={formatDate(getNested(user, 'kyc.businessDetails.tlIssueDate'))} />
                                        <RenderInfoItemViewOnly label="TL Expiry Date" value={formatDate(getNested(user, 'kyc.businessDetails.tlExpiryDate'))} />
                                        <RenderInfoItemViewOnly label="Tax Reg No (TRN)" value={getNested(user, 'kyc.businessDetails.taxRegNo')} />
                                        <RenderInfoItemViewOnly label="TIN Number" value={getNested(user, 'kyc.businessDetails.tinNumber')} />
                                        <RenderInfoItemViewOnly label="Establishment ID" value={getNested(user, 'kyc.businessDetails.establishmentId')} />
                                        <RenderInfoItemViewOnly label="Establishment ID Issue" value={formatDate(getNested(user, 'kyc.businessDetails.establishmentIdIssueDate'))} />
                                        <RenderInfoItemViewOnly label="Establishment ID Expiry" value={formatDate(getNested(user, 'kyc.businessDetails.establishmentIdExpiryDate'))} />
                                        <RenderInfoItemViewOnly label="Branch Count" value={getNested(user, 'kyc.businessDetails.branchCount')} />
                                        {/* Add any other relevant business details from kyc.businessDetails */}
                                        {/* Example: <RenderInfoItemViewOnly label="Industry" value={getNested(user, 'kyc.businessDetails.industry')} /> */}
                                        {/* Example: <RenderInfoItemViewOnly label="Operating Years" value={getNested(user, 'kyc.businessDetails.operatingYears')} /> */}
                                    </div>
                                </div>

                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Business Address</h3>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1">
                                        <RenderInfoItemViewOnly label="Address Line 1" value={getNested(user, 'kyc.businessDetails.businessAddressLine1')} />
                                        <RenderInfoItemViewOnly label="Address Line 2" value={getNested(user, 'kyc.businessDetails.businessAddressLine2')} />
                                        <RenderInfoItemViewOnly label="City" value={getNested(user, 'kyc.businessDetails.businessCity')} />
                                        <RenderInfoItemViewOnly label="Country" value={getNested(user, 'kyc.businessDetails.businessCountry')} />
                                        {/* Add Zone, Street, Building etc. if they exist under kyc.businessDetails */}
                                    </div>
                                </div>
                            </div>
                        )}


                        {/* Tab: Business Documents */}
                        {activeTab === 'businessDocs' && (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {rootBusinessDocFields.map(field => (
                                    <RenderDocumentDisplayViewOnly
                                        key={field.key}
                                        // Access document object directly from the root user object
                                        documentData={getNested(user, field.key, null)}
                                        label={field.label}
                                    />
                                ))}
                                {rootBusinessDocFields.every(field => !getNested(user, field.key)) && <p className="text-gray-500 italic md:col-span-3">No core business documents uploaded.</p>}
                            </div>
                        )}

                        {/* Tab: Financial Documents */}
                        {activeTab === 'financialDocs' && (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {rootFinancialDocFields.map(field => (
                                    <RenderDocumentDisplayViewOnly
                                        key={field.key}
                                        // Access document object directly from the root user object
                                        documentData={getNested(user, field.key, null)}
                                        label={field.label}
                                    />
                                ))}
                                {rootFinancialDocFields.every(field => !getNested(user, field.key)) && <p className="text-gray-500 italic md:col-span-3">No financial documents uploaded.</p>}
                            </div>
                        )}

                        {/* Tab: Shareholders */}
                        {activeTab === 'shareholders' && (
                            <div className="space-y-6">
                                {(getNested(user, 'shareholders', []) || []).map((sh, index) => (
                                    <div key={sh?._id || index} className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2 flex justify-between items-center">
                                            <span>Shareholder {index + 1}: {getNested(sh, 'firstName', '')} {getNested(sh, 'lastName', '')}</span>
                                            <span className={`px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getShareholderStatusClass(getNested(sh, 'kycVerificationStatus'))}`}>
                                                KYC: {String(getNested(sh, 'kycVerificationStatus', 'INITIATED')).toUpperCase()}
                                            </span>
                                        </h3>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-4">
                                            <RenderInfoItemViewOnly label="Middle Name" value={getNested(sh, 'middleName')} />
                                            <RenderInfoItemViewOnly label="Email" value={getNested(sh, 'email')} />
                                            <RenderInfoItemViewOnly label="Zone" value={getNested(sh, 'address.zone')} />
                                            <RenderInfoItemViewOnly label="Street" value={getNested(sh, 'address.streetNo')} />
                                            <RenderInfoItemViewOnly label="Building" value={getNested(sh, 'address.buildingNo')} />
                                            <RenderInfoItemViewOnly label="Floor" value={getNested(sh, 'address.floorNo')} />
                                            <RenderInfoItemViewOnly label="Unit" value={getNested(sh, 'address.unitNo')} />
                                            {/* <RenderInfoItemViewOnly label="Landmark" value={getNested(sh, 'address.additionalLandmark')} /> */}
                                            <RenderInfoItemViewOnly label="Added On" value={formatDate(getNested(sh, 'addedOn'))} />
                                            <RenderInfoItemViewOnly label="Modified On" value={formatDate(getNested(sh, 'modifiedOn'))} />
                                            {/* Add shareholding percentage if available */}
                                            {/* <RenderInfoItemViewOnly label="Shareholding %" value={getNested(sh, 'shareholdingPercentage') !== undefined ? `${getNested(sh, 'shareholdingPercentage')}%` : undefined} /> */}
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-3 border-t">
                                            <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'passport', null)} label="Passport" />
                                            <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'qid', null)} label="QID" />
                                            <RenderDocumentDisplayViewOnly documentData={getNested(sh, 'proofOfAddress', null)} label="Proof of Address" />
                                        </div>
                                        {/* {getNested(sh, 'passport.verificationNotes') && <p className="text-xs text-red-600 mt-2">Passport Notes: {getNested(sh, 'passport.verificationNotes')}</p>}
                                        {getNested(sh, 'qid.verificationNotes') && <p className="text-xs text-red-600 mt-1">QID Notes: {getNested(sh, 'qid.verificationNotes')}</p>}
                                        {getNested(sh, 'proofOfAddress.verificationNotes') && <p className="text-xs text-red-600 mt-1">Proof of Address Notes: {getNested(sh, 'proofOfAddress.verificationNotes')}</p>} */}
                                    </div>
                                ))}
                                {/* {(getNested(user, 'kyc.shareholders', []) || []).length === 0 && <p className="text-gray-500 italic mt-4">No shareholders listed.</p>} */}
                            </div>
                        )}

                        {/* Tab: Directors & Signatories */}
                        {activeTab === 'directorsSignatories' && (
                            <div className="space-y-6">
                                {/* Directors */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Directors</h3>
                                    {(getNested(user, 'kyc.directors', []) || []).length > 0 ? (
                                        <div className="space-y-4">
                                            {(user.kyc.directors || []).map((dir, index) => (
                                                <div key={dir._id || index} className="border rounded p-3 bg-gray-50">
                                                    <p className="text-base font-medium text-gray-800 mb-2">Director {index + 1}: {getNested(dir, 'directorName')}</p>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                                        <RenderInfoItemViewOnly label="Position" value={getNested(dir, 'position')} />
                                                        <RenderInfoItemViewOnly label="Nationality" value={getNested(dir, 'nationality')} />
                                                        <RenderInfoItemViewOnly label="DOB" value={formatDate(getNested(dir, 'dateOfBirth'))} />
                                                        <RenderInfoItemViewOnly label="National ID" value={getNested(dir, 'nationalId')} />
                                                        <RenderInfoItemViewOnly label="Address" value={getNested(dir, 'directorAddress')} />
                                                        {/* Add UBO, Signatory flags if present */}
                                                    </div>
                                                    {/* Add document display if director object contains documents */}
                                                    {/* {getNested(dir, 'idDocument') && ( ... <RenderDocumentDisplayViewOnly ... /> )} */}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500 italic">No directors listed.</p>
                                    )}
                                </div>

                                {/* Authorized Signatories */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Authorized Signatories</h3>
                                    {(getNested(user, 'kyc.authorizedSignatories', []) || []).length > 0 ? (
                                        <div className="space-y-4">
                                            {(user.kyc.authorizedSignatories || []).map((sig, index) => (
                                                <div key={sig._id || index} className="border rounded p-3 bg-gray-50">
                                                    <p className="text-base font-medium text-gray-800 mb-2">Signatory {index + 1}: {getNested(sig, 'name')}</p>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                                        <RenderInfoItemViewOnly label="Position" value={getNested(sig, 'position')} />
                                                        <RenderInfoItemViewOnly label="Contact" value={getNested(sig, 'contactNumber')} />
                                                    </div>
                                                    {/* Add document display if signatory object contains documents */}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500 italic">No authorized signatories listed.</p>
                                    )}
                                </div>

                                {/* Beneficial Owners */}
                                <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Beneficial Owners (UBOs)</h3>
                                    {(getNested(user, 'kyc.beneficialOwners', []) || []).length > 0 ? (
                                        <div className="space-y-4">
                                            {(user.kyc.beneficialOwners || []).map((owner, index) => (
                                                <div key={owner._id || index} className="border rounded p-3 bg-gray-50">
                                                    <p className="text-base font-medium text-gray-800 mb-2">Owner {index + 1}: {getNested(owner, 'name')}</p>
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                                        <RenderInfoItemViewOnly label="Ownership %" value={getNested(owner, 'ownership') !== undefined ? `${getNested(owner, 'ownership')}%` : 'N/A'} />
                                                        {/* Add other UBO details */}
                                                    </div>
                                                    {/* Add document display if owner object contains documents */}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <p className="text-gray-500 italic">No beneficial owners listed.</p>
                                    )}
                                </div>
                            </div>
                        )}


                        {/* Tab: Top Buyers */}
                        {activeTab === 'buyers' && (
                            <div className="space-y-4">
                                {(getNested(user, 'kyc.buyers', []) || []).map((buyer, index) => (
                                    <div key={buyer?._id || index} className="bg-white p-4 rounded-lg shadow border border-gray-200">
                                        <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">
                                            Buyer {index + 1}: {getNested(buyer, 'buyerName', 'N/A')}
                                        </h3>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 mb-3">
                                            <RenderInfoItemViewOnly label="Contact Person" value={getNested(buyer, 'contactPerson')} />
                                            <RenderInfoItemViewOnly label="Contact Phone" value={getNested(buyer, 'contactPhone')} />
                                            <RenderInfoItemViewOnly label="Contact Email" value={getNested(buyer, 'contactEmail')} />
                                            <RenderInfoItemViewOnly label="Registration Number" value={getNested(buyer, 'registrationNumber')} />
                                            {/* <RenderInfoItemViewOnly label="Business Type" value={getNested(buyer, 'businessType')} /> */}
                                            {/* <RenderInfoItemViewOnly label="Payment Terms" value={getNested(buyer, 'paymentTerms')} /> */}
                                            {/* <RenderInfoItemViewOnly label="Address" value={getNested(buyer, 'buyerAddress')} /> */}
                                        </div>
                                        {/* Add buyer document if it exists */}
                                        {/* {getNested(buyer, 'companyDocument') && ( ... <RenderDocumentDisplayViewOnly ... /> )} */}
                                    </div>
                                ))}
                                {(getNested(user, 'kyc.buyers', []) || []).length === 0 && <p className="text-gray-500 italic">No buyers listed.</p>}
                            </div>
                        )}

                        {/* Tab: Overall Verification */}
                        {activeTab === 'verification' && (
                            <div className="space-y-6">
                                <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
                                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Overall KYC Verification</h3>
                                    <div className="space-y-3">
                                        <div className="py-1 break-words text-sm">
                                            <span className="font-medium text-gray-600">Overall KYC Status: </span>
                                            {getOverallKycStatus()}
                                        </div>
                                        <RenderInfoItemViewOnly label="Verified/Processed On" value={formatDate(getNested(user, 'kyc.verifiedOn'))} />
                                        <RenderInfoItemViewOnly label="Overall Verification Notes" value={getNested(user, 'kyc.verificationNotes', '')} />
                                        {/* Add verifiedBy, rejectionReason if available */}
                                        {/* <RenderInfoItemViewOnly label="Verified By" value={getNested(user, 'kyc.verifiedBy', 'N/A')} /> */}
                                        {/* <RenderInfoItemViewOnly label="Rejection Reason" value={getNested(user, 'kyc.rejectionReason')} /> */}
                                    </div>
                                </div>
                            </div>
                        )}

                    </div> {/* End Tab Content Area */}
                </div> {/* End Modal Content Area (Scrollable) */}


                {/* Modal Footer (Only Close Button) */}
                <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end sticky bottom-0 flex-shrink-0">
                    <button
                        onClick={onClose}
                        type="button"
                        className="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        Close
                    </button>
                </div>
            </div> {/* End Modal Container */}
        </div> // End Modal Backdrop
    );
};

// =========================================================================
//          MAIN CREDIT ASSESSMENT PAGE COMPONENT (Uses Tailwind)
// =========================================================================
export default function CreditAssessmentPage() {
    // --- State Variables ---
    const [assessments, setAssessments] = useState([]); // Holds ALL raw fetched CreditLine data
    const [offers, setOffers] = useState([]); // Holds all raw fetched creditLineOffers
    const [usersMap, setUsersMap] = useState(new Map()); // Holds fetched user data keyed by ID

    console.log(assessments, offers, usersMap);

    // Filtered Data States for Tabs
    const [applicationAssessments, setApplicationAssessments] = useState([]); // Tab 1
    const [isGeneratingPdf, setIsGeneratingPdf] = useState(false); // Tab 1
    const [offerAssessments, setOfferAssessments] = useState([]);           // Tab 2
    const [rejectedAssessments, setRejectedAssessments] = useState([]);       // Tab 3
    const [isExportingExcel, setIsExportingExcel] = useState(false);

    // Modal States
    const [selectedAssessmentForOffer, setSelectedAssessmentForOffer] = useState(null);
    const [selectedUserForKycModal, setSelectedUserForKycModal] = useState(null);
    const [selectedAssessmentForRejection, setSelectedAssessmentForRejection] = useState(null);

    const [showOfferModal, setShowOfferModal] = useState(false);
    const [showKycDetailsModal, setShowKycDetailsModal] = useState(false);
    const [showMoreInfoModal, setShowMoreInfoModal] = useState(false);
    const [showRejectionModal, setShowRejectionModal] = useState(false);
    const [selectedAssessmentForMoreInfo, setSelectedAssessmentForMoreInfo] = useState(null);
    const [moreInfoNotes, setMoreInfoNotes] = useState('');
    const [isSubmittingMoreInfo, setIsSubmittingMoreInfo] = useState(false);
    const [suspendedAssessments, setSuspendedAssessments] = useState([]);
    const router = useRouter();

    // Loading & Error States
    const [loading, setLoading] = useState(true);

    const [fetchError, setFetchError] = useState(null);
    // --- NEW MODAL OPENING FUNCTION ---
    const handleOpenMoreInfoModal = useCallback((assessment) => {
        if (!assessment?._id || !assessment?.userId) {
            alert("Error: Missing assessment info for requesting more details."); return;
        }
        // You might add similar validation as rejection if needed (e.g., check if already suspended/rejected/offered)
        // For now, assume if the button is visible, the action is allowed.

        setSelectedAssessmentForMoreInfo(assessment);
        setMoreInfoNotes(''); // Reset notes field
        setShowMoreInfoModal(true);
    }, []); // Add dependencies if it uses external state/props not shown here


    // Top Level Tab State
    const [activeTopLevelTab, setActiveTopLevelTab] = useState('applications'); // 'applications', 'creditOffers', 'rejected'

    // Offer Modal State
    const [isSubmittingOffer, setIsSubmittingOffer] = useState(false);
    const [offerData, setOfferData] = useState({
        creditLimit: '', tenureDays: '', interestRate: '', processingFeeValue: '',
        processingFeeType: 'flat', riskProfile: 'MEDIUM', notes: '',
    });

    // Rejection Modal State
    const [rejectionReason, setRejectionReason] = useState('');
    const [isSubmittingRejection, setIsSubmittingRejection] = useState(false);

    // KYC View Modal State
    const [activeKycModalTab, setActiveKycModalTab] = useState('businessDocs');

    // --- State for Filters ---
    // --- State for Filters ---
    const [assessmentFilters, setAssessmentFilters] = useState({
        searchTerm: '',
        startDate: '', // Applied Date Start
        endDate: '',   // Applied Date End
        minOfferLimit: '', // New: Offer Credit Limit Min
        maxOfferLimit: '', // New: Offer Credit Limit Max
        offerStatus: '',   // New: Offer Status (from InvoiceFinancingOffers status enum)
        creditLineStatus: '', // New: Credit Line Status (from InvoiceFinancingCreditLine status enum)
        kycStatus: '',      // New: KYC Status (from user kyc.verificationStatus)
    });

    // --- PASTE THE PROVIDED handleDownloadDocumentsZip FUNCTION HERE ---

    // --- END OF handleDownloadDocumentsZip FUNCTION ---

    const resetAssessmentFilters = useCallback(() => {
        setAssessmentFilters({
            searchTerm: '',
            startDate: '',
            endDate: '',
            minOfferLimit: '', // Reset new filters
            maxOfferLimit: '', // Reset new filters
            offerStatus: '',   // Reset new filters
            creditLineStatus: '', // Reset new filters
            kycStatus: '',      // Reset new filters
        });
    }, []);

    // --- Fee Calculation Helpers ---
    const calculateMaxAllowedFee = useCallback((creditLimit) => {
        const limit = Number(creditLimit) || 0;
        if (limit <= 0) return MAX_PROCESSING_FEE_FLAT;
        const percentageMax = (MAX_PROCESSING_FEE_PERCENTAGE / 100) * limit;
        return Math.min(MAX_PROCESSING_FEE_FLAT, percentageMax) + 999999999;
    }, []);

    const calculateCurrentFee = useCallback((type, value, creditLimit) => {
        const feeVal = Number(value) || 0;
        const limitVal = Number(creditLimit) || 0;
        if (type === 'flat') return feeVal;
        if (type === 'percentage') return (feeVal / 100) * limitVal;
        return 0;
    }, []);

    // --- Apply Filters to Tab Data ---
    const filteredApplicationAssessments = useMemo(() => {
        // Destructure *all* filters from assessmentFilters
        const { searchTerm, startDate, endDate, creditLineStatus, kycStatus } = assessmentFilters;
        const lowerSearchTerm = searchTerm.toLowerCase();
        const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
        const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;
        // const numMinOfferLimit = minOfferLimit === '' ? -Infinity : parseFloat(minOfferLimit);
        // const numMaxOfferLimit = maxOfferLimit === '' ? Infinity : parseFloat(maxOfferLimit);

        return applicationAssessments.filter(assessment => {
            const borrowerName = `${getNested(assessment, 'userDetails.firstName', '')} ${getNested(assessment, 'userDetails.lastName', '')}`.toLowerCase();
            const businessName = getNested(assessment, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
            const appliedAtTs = assessment.createdAt ? new Date(assessment.createdAt).getTime() : null;

            // Get relevant data for filtering - handle cases where offer details might not exist yet
            // const currentOfferDetails = assessment.acceptedOrActiveOfferDetails || assessment.thisLenderOfferDetails;
            // const offerLimit = parseFloat(getNested(currentOfferDetails, 'creditLimit', NaN)); // Get offer limit if exists
            // const currentOfferStatus = getNested(currentOfferDetails, 'status', ''); // Offer status
            const currentCreditLineStatus = getNested(assessment, 'creditLineStatus', ''); // CL status from main doc
            const currentKycStatus = getNested(assessment, 'kycStatus', ''); // Derived KYC status

            // Apply Filters
            if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) return false;
            if (tsStartDate && (!appliedAtTs || appliedAtTs < tsStartDate)) return false;
            if (tsEndDate && (!appliedAtTs || appliedAtTs > tsEndDate)) return false;

            // --- New Filter Checks ---
            // Offer Limit: Only apply if min/max are set. Maybe skip if no offer exists? Or treat NaN as failing the check?
            // This check might be more relevant for the 'Offers' tab where an offer *should* exist.
            // For applications, they likely won't have an offer limit yet. Let's comment it out for this tab.
            // if (!isNaN(numMinOfferLimit) && (isNaN(offerLimit) || offerLimit < numMinOfferLimit)) { return false; }
            // if (!isNaN(numMaxOfferLimit) && (isNaN(offerLimit) || offerLimit > numMaxOfferLimit)) { return false; }

            // Offer Status: Likely not relevant for the 'Applications' tab as there's no offer yet.
            // if (offerStatus && currentOfferStatus !== offerStatus) return false;

            // Credit Line Status: Filter by the status in the CreditLine document
            if (creditLineStatus && currentCreditLineStatus !== creditLineStatus) return false;

            // KYC Status: Filter by the user's KYC status
            if (kycStatus && currentKycStatus !== kycStatus) return false;
            // --- End New Filter Checks ---

            return true; // Passed all filters
        });
    }, [applicationAssessments, assessmentFilters]);

    const filteredOfferAssessments = useMemo(() => {
        // Destructure *all* filters
        const { searchTerm, startDate, endDate, minOfferLimit, maxOfferLimit, offerStatus, creditLineStatus, kycStatus } = assessmentFilters;
        const lowerSearchTerm = searchTerm.toLowerCase();
        const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
        const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;
        const numMinOfferLimit = minOfferLimit === '' ? -Infinity : parseFloat(minOfferLimit);
        const numMaxOfferLimit = maxOfferLimit === '' ? Infinity : parseFloat(maxOfferLimit);

        return offerAssessments.filter(assessment => {
            const borrowerName = `${getNested(assessment, 'userDetails.firstName', '')} ${getNested(assessment, 'userDetails.lastName', '')}`.toLowerCase();
            const businessName = getNested(assessment, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
            const appliedAtTs = assessment.createdAt ? new Date(assessment.createdAt).getTime() : null;

            // Offer details *should* exist in this tab
            const currentOfferDetails = assessment.acceptedOrActiveOfferDetails || assessment.thisLenderOfferDetails;
            const offerLimit = parseFloat(getNested(currentOfferDetails, 'creditLimit', NaN));
            // Use marketplaceStatus as it reflects the combined/interpreted status for the table
            const currentOfferStatus = getNested(assessment, 'marketplaceStatus', '');
            const currentCreditLineStatus = getNested(assessment, 'creditLineStatus', '');
            const currentKycStatus = getNested(assessment, 'kycStatus', '');

            // --- Apply Filters ---
            if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) return false;
            if (tsStartDate && (!appliedAtTs || appliedAtTs < tsStartDate)) return false;
            if (tsEndDate && (!appliedAtTs || appliedAtTs > tsEndDate)) return false;

            // Offer Limit Check (relevant here)
            if (!isNaN(numMinOfferLimit) && (isNaN(offerLimit) || offerLimit < numMinOfferLimit)) { return false; }
            if (!isNaN(numMaxOfferLimit) && (isNaN(offerLimit) || offerLimit > numMaxOfferLimit)) { return false; }

            // Offer Status Check (Check against the displayed marketplaceStatus for consistency)
            // Note: This might need adjustment if `offerStatus` filter values don't exactly match `marketplaceStatus` strings
            if (offerStatus && (!currentOfferStatus || !currentOfferStatus.toUpperCase().includes(offerStatus.toUpperCase()))) return false; // Check if filter value is part of the status string

            // Credit Line Status Check
            if (creditLineStatus && currentCreditLineStatus !== creditLineStatus) return false;

            // KYC Status Check
            if (kycStatus && currentKycStatus !== kycStatus) return false;
            // --- End Filters ---

            return true;
        });
    }, [offerAssessments, assessmentFilters]);

    const addDocumentFieldsToRow = (rowData, docObject, prefix, getNestedFn, formatDateFn) => {
        if (!docObject || typeof docObject !== 'object') { // Ensure docObject is an object
            rowData[`${prefix} - File Name`] = 'N/A';
            rowData[`${prefix} - Signed URL`] = 'N/A';
            rowData[`${prefix} - Uploaded On`] = 'N/A';
            rowData[`${prefix} - Status`] = 'NOT_SUBMITTED'; // Or appropriate default
            rowData[`${prefix} - Notes`] = '';
            rowData[`${prefix} - Verification Date`] = 'N/A';
            rowData[`${prefix} - MIME Type`] = 'N/A';
            return;
        }
        const filePath = getNestedFn(docObject, 'filePath', '');
        rowData[`${prefix} - File Name`] = filePath ? (filePath.split('/').pop() || 'document') : 'N/A';
        rowData[`${prefix} - Signed URL`] = getNestedFn(docObject, 'signedUrl', 'N/A');
        rowData[`${prefix} - Uploaded On`] = formatDateFn(getNestedFn(docObject, 'uploadedOn'));
        rowData[`${prefix} - Status`] = getNestedFn(docObject, 'verificationStatus', 'PENDING'); // Default from RenderDocumentDisplayViewOnly
        rowData[`${prefix} - Notes`] = getNestedFn(docObject, 'verificationNotes', '');
        rowData[`${prefix} - Verification Date`] = formatDateFn(getNestedFn(docObject, 'verifiedOrRejectedOn'));
        rowData[`${prefix} - MIME Type`] = getNestedFn(docObject, 'mimeType', 'N/A'); // As seen in RenderDocumentDisplayViewOnly
    };

    const filteredRejectedAssessments = useMemo(() => {
        // Apply relevant filters (Search, Date, maybe CL Status, KYC Status)
        const { searchTerm, startDate, endDate, creditLineStatus, kycStatus } = assessmentFilters; // Offer limit/status likely irrelevant here
        const lowerSearchTerm = searchTerm.toLowerCase();
        const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
        const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;

        return rejectedAssessments.filter(assessment => {
            const borrowerName = `${getNested(assessment, 'userDetails.firstName', '')} ${getNested(assessment, 'userDetails.lastName', '')}`.toLowerCase();
            const businessName = getNested(assessment, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
            const appliedAtTs = assessment.createdAt ? new Date(assessment.createdAt).getTime() : null;
            const currentCreditLineStatus = getNested(assessment, 'creditLineStatus', '');
            const currentKycStatus = getNested(assessment, 'kycStatus', '');

            // Apply Filters
            if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) return false;
            if (tsStartDate && (!appliedAtTs || appliedAtTs < tsStartDate)) return false;
            if (tsEndDate && (!appliedAtTs || appliedAtTs > tsEndDate)) return false;
            if (creditLineStatus && currentCreditLineStatus !== creditLineStatus) return false;
            if (kycStatus && currentKycStatus !== kycStatus) return false;
            // --- End Filters ---

            return true;
        });
    }, [rejectedAssessments, assessmentFilters]);

    const filteredSuspendedAssessments = useMemo(() => {
        // Apply relevant filters (Search, Date, KYC Status) - Adjust as needed
        const { searchTerm, startDate, endDate, kycStatus, creditLineStatus /* Potentially add other filters */ } = assessmentFilters;
        const lowerSearchTerm = searchTerm.toLowerCase();
        const tsStartDate = startDate ? new Date(startDate).setHours(0, 0, 0, 0) : null;
        const tsEndDate = endDate ? new Date(endDate).setHours(23, 59, 59, 999) : null;

        return suspendedAssessments.filter(assessment => {
            const borrowerName = `${getNested(assessment, 'userDetails.firstName', '')} ${getNested(assessment, 'userDetails.lastName', '')}`.toLowerCase();
            const businessName = getNested(assessment, 'userDetails.kyc.businessDetails.businessName', '').toLowerCase();
            const appliedAtTs = assessment.createdAt ? new Date(assessment.createdAt).getTime() : null;
            const currentKycStatus = getNested(assessment, 'kycStatus', '');
            const currentCreditLineStatus = getNested(assessment, 'creditLineStatus', ''); // Filter by overall CL status if desired

            // Apply Filters
            if (lowerSearchTerm && !(borrowerName.includes(lowerSearchTerm) || businessName.includes(lowerSearchTerm))) return false;
            if (tsStartDate && (!appliedAtTs || appliedAtTs < tsStartDate)) return false;
            if (tsEndDate && (!appliedAtTs || appliedAtTs > tsEndDate)) return false;
            if (kycStatus && currentKycStatus !== kycStatus) return false;
            if (creditLineStatus && currentCreditLineStatus !== creditLineStatus) return false;
            // --- Add other filter checks if needed ---

            return true;
        });
    }, [suspendedAssessments, assessmentFilters]);

    // --- Remember to update the table bodies to use these new filtered arrays ---
    // e.g., replace `applicationAssessments.map` with `filteredApplicationAssessments.map`
    // e.g., replace `offerAssessments.map` with `filteredOfferAssessments.map`
    // e.g., replace `rejectedAssessments.map` with `filteredRejectedAssessments.map`
    // And update the "no data" messages to mention filters, e.g., "No applications match the current filters."

    // --- *** REVISED Data Filtering Logic *** ---
    const filterAndSetData = useCallback((allFetchedCreditLines, userDetailsMap, allFetchedOffers, loggedInLenderId) => {
        const applications = []; // Tab 1: Needs action (Offer/Reject/Suspend)
        const suspended = [];    // Tab 2: Needs more info (Suspended by this lender)
        const creditOffers = []; // Tab 3: Offer submitted/accepted
        const rejected = [];     // Tab 4: Rejected by this lender
        const loggedInLenderStr = String(loggedInLenderId);

        console.log("Filtering: CLs=", allFetchedCreditLines.length, "Users=", userDetailsMap.size, "Offers=", allFetchedOffers.length, "Lender=", loggedInLenderStr);

        allFetchedCreditLines.forEach(cl => {
            const userDetails = userDetailsMap.get(cl.userId);
            if (!userDetails) {
                console.warn(`Filter: User details NOT FOUND for CL ${cl._id} (userId: ${cl.userId}). Skipping.`);
                return;
            }

            const userIdFromUserDetails = userDetails._id;
            const kycStatus = String(getNested(userDetails, 'kyc.verificationStatus', 'N/A')).toUpperCase();
            const clStatus = String(cl.creditLineStatus || 'UNKNOWN').toUpperCase(); // Overall status

            // Find relevant offers for *this specific user*
            const userOffers = allFetchedOffers.filter(offer => String(offer.merchantId) === String(userIdFromUserDetails));
            const thisLenderOffer = userOffers.find(offer => String(offer.lenderId) === loggedInLenderStr);
            const acceptedOrActiveOffer = userOffers.find(offer =>
                offer.status && ['ACCEPTED', 'LOAN_CONTRACT_ACCEPTED', 'INITIATED_FUND_TRANSFER', 'READY_FOR_DISBURSAL', 'LOAN_IN_PROGRESS', 'PAID', 'OVERDUE', 'DEFAULTED']
                    .includes(String(offer.status).toUpperCase())
            );

            // --- Check LATEST reviewHistory entry by THIS lender ---
            let lastReviewByThisLender = null;
            if (Array.isArray(cl.reviewHistory)) {
                // Find the most recent entry reviewed by the logged-in lender
                lastReviewByThisLender = [...cl.reviewHistory]
                    .filter(h => String(h.reviewedBy) === loggedInLenderStr)
                    .sort((a, b) => new Date(b.reviewDate || b.createdAt) - new Date(a.reviewDate || a.createdAt)) // Sort descending by date
                [0]; // Get the latest one
            }
            // --- End Check ---

            // Base assessment object
            const assessment = {
                ...cl,
                userDetails: userDetails,
                kycStatus: kycStatus,
                marketplaceStatus: clStatus, // Default status, will be refined below
                thisLenderOfferDetails: thisLenderOffer,
                acceptedOrActiveOfferDetails: acceptedOrActiveOffer,
                hasAcceptedOffer: !!acceptedOrActiveOffer,
                // Store the latest review details from this lender for easy access
                lastReviewDetails: lastReviewByThisLender ? {
                    status: lastReviewByThisLender.status,
                    date: lastReviewByThisLender.reviewDate || lastReviewByThisLender.createdAt,
                    notes: lastReviewByThisLender.notes || (lastReviewByThisLender.rejectionReasons?.join(', ')),
                    reviewedBy: String(lastReviewByThisLender.reviewedBy)
                } : null,
                // Specific flags based on the last review
                isSuspendedByThisLender: lastReviewByThisLender?.status === 'SUSPENDED',
                isRejectedByThisLender: lastReviewByThisLender?.status === 'REJECTED',
            };

            // --- Tab Categorization Logic (Order Matters) ---

            // 1. Suspended Tab: If the LATEST review action by THIS lender was 'SUSPENDED'
            //    (And no subsequent offer has been made or accepted)
            if (assessment.isSuspendedByThisLender && !assessment.hasAcceptedOffer && !assessment.thisLenderOfferDetails) {
                assessment.marketplaceStatus = `Suspended (More Info Needed)`; // Update status for display
                suspended.push(assessment);
                return; // Stop processing this CL
            }

            // 2. Rejected Tab: If the LATEST review action by THIS lender was 'REJECTED'
            //    (And no subsequent offer has been made or accepted - though this should be unlikely if rejection is final)
            if (assessment.isRejectedByThisLender && !assessment.hasAcceptedOffer && !assessment.thisLenderOfferDetails) {
                assessment.marketplaceStatus = `Rejected by You`; // Update status for display
                rejected.push(assessment);
                return; // Stop processing this CL
            }

            // 3. Applications Tab: If KYC is approved, no accepted offer, no offer submitted *by this lender*,
            //    AND the latest review action wasn't SUSPENDED or REJECTED by this lender.
            const isEligibleForApplicationTab =
                clStatus === 'UNDER_REVIEW' && // Still needs overall review
                kycStatus === 'APPROVED' &&    // KYC must be approved
                !assessment.hasAcceptedOffer && // No globally accepted offer
                !assessment.thisLenderOfferDetails && // This lender hasn't made an offer yet
                !assessment.isSuspendedByThisLender && // Not currently suspended by this lender
                !assessment.isRejectedByThisLender;   // Not finally rejected by this lender

            if (isEligibleForApplicationTab) {
                applications.push(assessment);
                return; // Stop processing this CL
            }

            // 4. Credit Offers Tab: If this lender has an offer OR any offer is accepted/active.
            //    (This implicitly handles cases where an offer was made *after* a suspension was cleared, or if another lender's offer was accepted)
            const showInOffers = assessment.thisLenderOfferDetails || assessment.hasAcceptedOffer;

            if (showInOffers) {
                // --- STATUS LOGIC FOR CREDIT OFFERS TAB (same as before) ---
                if (assessment.acceptedOrActiveOfferDetails) {
                    const acceptedLenderIdStr = String(assessment.acceptedOrActiveOfferDetails.lenderId || 'Unknown');
                    if (acceptedLenderIdStr === loggedInLenderStr) {
                        assessment.marketplaceStatus = `Your Offer: ${assessment.acceptedOrActiveOfferDetails.status}`;
                    } else {
                        assessment.marketplaceStatus = "Offer Accepted (Other Lender)";
                    }
                } else if (assessment.thisLenderOfferDetails) {
                    assessment.marketplaceStatus = `Your Offer: ${assessment.thisLenderOfferDetails.status || 'Submitted'}`;
                } else {
                    assessment.marketplaceStatus = clStatus; // Fallback
                }
                // --- END STATUS LOGIC ---
                creditOffers.push(assessment);
                return; // Stop processing this CL
            }

            // Optional: Log items that didn't match any category
            // console.log(`Filter: CL ${cl._id} (User: ${userIdFromUserDetails}, CL Status: ${clStatus}, KYC: ${kycStatus}, Accepted: ${assessment.hasAcceptedOffer}, This Lender Offer: ${!!assessment.thisLenderOfferDetails}, Last Review: ${assessment.lastReviewDetails?.status}) -> No Tab Match`);

        });

        const sortLogic = (a, b) => {
            // Primary sorting: by 'kycStatus' (or the relevant status field on the 'assessment' object)
            const statusA = String(getNested(a, 'kycStatus', 'UNKNOWN')).toUpperCase();
            const statusB = String(getNested(b, 'kycStatus', 'UNKNOWN')).toUpperCase();

            const orderA = CREDIT_LINE_STATUS_ORDER[statusA] ?? DEFAULT_CREDIT_LINE_STATUS_PRIORITY;
            const orderB = CREDIT_LINE_STATUS_ORDER[statusB] ?? DEFAULT_CREDIT_LINE_STATUS_PRIORITY;

            if (orderA !== orderB) {
                return orderA - orderB; // Ascending order based on defined priority
            }

            // Secondary sorting: by creation date ('createdAt' property on the 'assessment' object)
            const dateA = new Date(getNested(a, 'createdAt', 0));
            const dateB = new Date(getNested(b, 'createdAt', 0));

            const timeA = !isNaN(dateA.getTime()) ? dateA.getTime() : 0;
            const timeB = !isNaN(dateB.getTime()) ? dateB.getTime() : 0;

            return timeB - timeA; // Descending order for dates (newest first)
        };

        console.log("Filtering Complete - Counts: Applications:", applications.length, "Suspended:", suspended.length, "Offers:", creditOffers.length, "Rejected:", rejected.length);
        setApplicationAssessments(applications.sort(sortLogic));
        setSuspendedAssessments(suspended.sort(sortLogic));
        setOfferAssessments(creditOffers.sort(sortLogic));
        setRejectedAssessments(rejected.sort(sortLogic));

    }, []); // Keep dependency array empty if it's a pure function of its args


    // --- Data Fetching Function ---
    const fetchData = useCallback(async () => {
        setLoading(true);
        setFetchError(null);
        setApplicationAssessments([]);
        setOfferAssessments([]);
        setSuspendedAssessments([]); // Reset new state
        setRejectedAssessments([]);
        setAssessments([]);
        setOffers([]);
        setUsersMap(new Map());

        try {
            const loggedInLenderId = localStorage.getItem('userId');
            if (!loggedInLenderId) throw new Error("Lender ID not found. Please log in again.");
            console.log("Fetching data for Lender ID:", loggedInLenderId);

            // Parallel fetching using Promise.allSettled
            const [clResult, userResult, offerResult] = await Promise.allSettled([
                fetch(`${config.apiUrl}/ops/invoiceFinancing/fetchCreditLines`),
                fetch(`${config.apiUrl}/ops/invoiceFinancing/getSubmittedKycs`),
                axios.get(`${config.apiUrl}/ops/invoiceFinancing/offers?offerType=creditLineOffer`)
            ]);

            // Process Credit Lines
            let fetchedCreditLines = [];
            if (clResult.status === 'fulfilled' && clResult.value.ok) {
                fetchedCreditLines = await clResult.value.json() || [];
                if (!Array.isArray(fetchedCreditLines)) {
                    console.warn("Credit lines data was not an array, wrapping.", fetchedCreditLines);
                    fetchedCreditLines = fetchedCreditLines ? [fetchedCreditLines] : [];
                }
            } else if (clResult.status === 'fulfilled' && clResult.value.status === 404) {
                console.log("No credit lines found (404).");
                fetchedCreditLines = [];
            } else if (clResult.status === 'rejected' || !clResult.value.ok) {
                const errorReason = clResult.status === 'rejected' ? clResult.reason : `Status ${clResult.value.status}`;
                console.error(`Failed to fetch credit lines: ${errorReason}`);
                // Decide if you want to throw an error or continue with empty data
                // throw new Error(`Failed to fetch credit lines: ${errorReason}`);
            }
            console.log("Fetched Credit Lines:", fetchedCreditLines.length);

            // Process Users
            let fetchedUsersMap = new Map();
            if (userResult.status === 'fulfilled' && userResult.value.ok) {
                const usersResJson = await userResult.value.json();
                if (usersResJson && usersResJson.success === true && Array.isArray(usersResJson.kycs)) {
                    fetchedUsersMap = new Map(usersResJson.kycs.map(user => [user._id, user]));
                } else {
                    console.log("User KYC fetch successful but no array data found.");
                }
            } else if (userResult.status === 'fulfilled' && userResult.value.status === 404) {
                console.log("User KYC endpoint returned 404 (No users found).");
            } else if (userResult.status === 'rejected' || !userResult.value.ok) {
                const errorReason = userResult.status === 'rejected' ? userResult.reason : `Status ${userResult.value.status}`;
                console.error(`Failed to fetch users: ${errorReason}`);
                // Decide if you want to throw or continue
                // throw new Error(`Failed to fetch users: ${errorReason}`);
            }
            console.log("Fetched Users (Map Size):", fetchedUsersMap.size);

            // Process Offers
            let fetchedOffers = [];
            if (offerResult.status === 'fulfilled' && offerResult.value.data?.success && Array.isArray(offerResult.value.data.offers)) {
                fetchedOffers = offerResult.value.data.offers;
            } else if (offerResult.status === 'rejected' && axios.isAxiosError(offerResult.reason) && offerResult.reason.response?.status === 404) {
                console.log("No credit line offers found (404).");
            } else if (offerResult.status === 'rejected' || !offerResult.value.data?.success) {
                const errorReason = offerResult.status === 'rejected' ? offerResult.reason : `Success false or invalid data`;
                console.warn(`Failed to fetch offers: ${errorReason}`);
                // Continue with empty offers array
            }
            console.log("Fetched Offers:", fetchedOffers.length);

            // Set raw data state
            setAssessments(fetchedCreditLines);
            setOffers(fetchedOffers);
            setUsersMap(fetchedUsersMap);

            // Filter and set tab-specific data
            filterAndSetData(fetchedCreditLines, fetchedUsersMap, fetchedOffers, loggedInLenderId);

        } catch (error) {
            console.error('Error during fetchData:', error);
            setFetchError(error.message || "An unknown error occurred during data fetching.");
            setApplicationAssessments([]);
            setOfferAssessments([]);
            setRejectedAssessments([]);
        } finally {
            setLoading(false);
            console.log("Data fetch process finished.");
        }
    }, [filterAndSetData]); // Depend on the memoized filter function

    // Initial Fetch Effect
    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleViewKycDetails = useCallback((assessment) => {
        if (!assessment?.userDetails?._id) {
            alert("Error: Could not load borrower details for KYC review.");
            return;
        }
        const userId = assessment.userDetails._id;
        try {
            // Store the assessment object in sessionStorage
            // Using a key that includes userId can help if dealing with multiple items,
            // though sessionStorage is tab-specific.
            sessionStorage.setItem(`assessmentDataForUser_${userId}`, JSON.stringify(assessment));
        } catch (error) {
            console.error("Error saving assessment data to sessionStorage:", error);
            // If saving to sessionStorage fails, the KycDetailsPage will rely on its API fallback.
            // You might want to alert the user or handle this error more explicitly if needed.
        }

        router.push({
            pathname: '/kycDetails', // Assuming this is the correct path
            query: {
                userId: userId, // Only pass userId in the query
            },
        });
    }, [router]);

    const handleOpenOfferModal = useCallback((assessment) => {
        if (!assessment?.userDetails?._id) {
            alert("Error: Could not load borrower details."); return;
        }
        // Validation checks (already done by filtering, but good safety net)
        if (String(getNested(assessment, 'kycStatus', '')).toUpperCase() !== 'APPROVED') {
            alert("KYC not approved. Cannot make offer."); return;
        }
        if (assessment.hasAcceptedOffer) {
            alert("An offer has already been accepted by the borrower."); return;
        }
        if (assessment.thisLenderOfferDetails) {
            alert("You have already submitted an offer for this application."); return;
        }

        setSelectedAssessmentForOffer(assessment);
        setOfferData({ // Reset form for a new offer
            creditLimit: '', tenureDays: '', interestRate: '', processingFeeValue: '',
            processingFeeType: 'flat', riskProfile: 'MEDIUM', notes: '',
        });
        setShowOfferModal(true);
    }, []);

    const handleOpenRejectionModal = useCallback((assessment) => {
        if (!assessment?._id || !assessment?.userId) {
            alert("Error: Missing assessment info for rejection."); return;
        }
        // Validation checks
        if (String(getNested(assessment, 'kycStatus', '')).toUpperCase() !== 'APPROVED') {
            alert("Cannot reject application before KYC is approved."); return; // Or adjust logic if rejection is allowed earlier
        }
        if (assessment.hasAcceptedOffer) {
            alert("An offer has already been accepted by the borrower. Cannot reject now."); return;
        }
        if (assessment.thisLenderOfferDetails) {
            alert("You have already submitted an offer. Cannot reject now."); return; // Prevent rejection if offer already made
        }
        setSelectedAssessmentForRejection(assessment);
        setRejectionReason('');
        setShowRejectionModal(true);
    }, []);

    // --- Submission Functions ---
    const handleSubmitOffer = useCallback(async () => {
        const assessment = selectedAssessmentForOffer;
        // --- Start: Validation Checks ---
        if (!assessment || String(getNested(assessment, 'kycStatus', '')).toUpperCase() !== 'APPROVED' || assessment.hasAcceptedOffer || assessment.thisLenderOfferDetails) {
            alert('Cannot submit offer. Ensure KYC is approved, no offer is accepted, and you have not already submitted an offer.');
            return;
        }
        const lenderId = localStorage.getItem('userId');
        const borrowerUserId = getNested(assessment, 'userDetails._id');
        if (!borrowerUserId || !lenderId) {
            alert('Borrower/Lender ID missing.'); return;
        }
        const currentLimit = Number(offerData.creditLimit);
        const currentTenure = Number(offerData.tenureDays);
        const currentInterest = Number(offerData.interestRate);
        const currentFeeValue = Number(offerData.processingFeeValue);
        if (!(currentLimit > 0) || !(currentTenure > 0) || !(currentInterest >= 0) || !(currentFeeValue >= 0)) {
            alert('Please fill in Credit Limit (>0), Tenure (>0), Service Fee (>=0), and Processing Fee (>=0) with valid numbers.'); return;
        }
        const currentFee = calculateCurrentFee(offerData.processingFeeType, currentFeeValue, currentLimit);
        const maxAllowed = calculateMaxAllowedFee(currentLimit);
        // Note: The maxAllowed calculation might still have the +999999999 from previous logs - ensure that's intended or remove it.
        if (currentFee > maxAllowed) {
            alert(`Processing Fee (QAR ${currentFee.toFixed(2)}) exceeds the maximum allowed (QAR ${maxAllowed.toFixed(2)}). Please adjust.`); return;
        }
        // --- End: Validation Checks ---

        // --- Close Modal Immediately and Set Loading ---
        setShowOfferModal(false);
        setSelectedAssessmentForOffer(null);
        setIsSubmittingOffer(true);
        // --- End Close Modal ---

        // Construct payload (ensure this matches backend expectations)
        const payload = {
            merchantId: borrowerUserId,
            lenderId: lenderId,
            offerType: "creditLineOffer",
            creditLimit: currentLimit,
            tenureDays: currentTenure,
            interestRate: String(currentInterest), // Ensure backend handles String if needed
            currency: 'QAR',
            processingFee: { type: offerData.processingFeeType, value: currentFeeValue },
            riskProfile: offerData.riskProfile || 'MEDIUM',
            notes: offerData.notes || '',
            status: 'PENDING' // Initial status
        };
        if (!payload.notes?.trim()) delete payload.notes;


        try {
            console.log("Attempting to submit offer...");
            const response = await axios.post(`${config.apiUrl}/ops/invoiceFinancing/createOffer`, payload);
            console.log("Offer API Response:", response.data);

            // ***** CORRECTED SUCCESS CHECK *****
            // Check for the presence of the offerId instead of a 'success' boolean
            if (!response.data?.offerId) {
                // Throw error if backend didn't return the expected offerId
                // Use the message from the response if available, otherwise a generic error
                throw new Error(response.data?.message || "Failed to create offer: No offer ID received from backend.");
            }
            // ***** END CORRECTED CHECK *****


            // --- Success Path ---
            console.log("Offer submission successful. Refreshing data...");
            alert(`Offer submitted successfully.`); // Inform user

            // *** Call fetchData to refresh the page content ***
            await fetchData();
            // *** End Refresh Call ***

            console.log("Data refresh called after successful offer submission.");
            // --- End Success Path ---

        } catch (error) {
            // Log the actual error object for better debugging
            console.error('Error submitting offer:', error);
            // Display the error message from the caught error
            alert(`Error submitting offer: ${error.message || 'An unknown error occurred'}`);
        } finally {
            // Always reset loading state
            setIsSubmittingOffer(false);
            console.log("Offer submission process finished (finally block).");
        }
    }, [
        selectedAssessmentForOffer,
        offerData,
        fetchData, // fetchData dependency is correct
        calculateCurrentFee,
        calculateMaxAllowedFee,
        setShowOfferModal,
        setIsSubmittingOffer,
        setSelectedAssessmentForOffer
    ]); // Removed filterAndSetData as it's handled within fetchData

    // --- NEW SUBMISSION FUNCTION FOR MORE INFO (SUSPENDED) ---
    const handleSubmitMoreInfo = useCallback(async () => {
        if (!selectedAssessmentForMoreInfo || !moreInfoNotes.trim()) {
            alert('Please provide notes explaining what information is required.'); return;
        }
        const assessmentToSuspend = selectedAssessmentForMoreInfo;
        const creditLineIdToUpdate = assessmentToSuspend._id;
        const loggedInLenderId = localStorage.getItem('userId'); // Get the logged-in user's ID

        if (!loggedInLenderId) {
            alert('Error: Could not identify logged-in user. Please log in again.');
            return;
        }

        setIsSubmittingMoreInfo(true);

        // --- Payload for the new route (includes reviewedBy) ---
        const payload = {
            status: 'SUSPENDED',
            notes: moreInfoNotes,
            reviewedBy: loggedInLenderId // Add the user ID here
        };
        console.log("Add Review (Suspend) Payload:", JSON.stringify(payload, null, 2));
        // --- END PAYLOAD ---

        try {
            // Use the NEW dedicated route
            const response = await axios.post(
                `${config.apiUrl}/ops/invoiceFinancing/creditLine/${creditLineIdToUpdate}/addReview`,
                payload
            );

            if (!response.data?.success) {
                throw new Error(response.data?.message || "Backend reported failure during suspension update.");
            }
            alert("Request for more information submitted successfully (Status: More Information Required).");
            setShowMoreInfoModal(false);
            await fetchData(); // Refresh data

        } catch (error) {
            console.error('Error submitting request for more info:', error.response?.data || error.message);
            alert(`Error submitting request: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
        } finally {
            setIsSubmittingMoreInfo(false);
            setSelectedAssessmentForMoreInfo(null);
            setMoreInfoNotes('');
        }
    }, [selectedAssessmentForMoreInfo, moreInfoNotes, fetchData]);

    const handleSubmitRejection = useCallback(async () => {
        if (!selectedAssessmentForRejection || !rejectionReason.trim()) {
            alert('Please provide a rejection reason.'); return;
        }
        const assessmentToReject = selectedAssessmentForRejection;

        // --- Updated Validation ---
        // Check if it's already been rejected by this lender according to our filtered data
        if (assessmentToReject.isRejectedByThisLender) {
            alert('You have already rejected this application.');
            return;
        }
        // Check other conditions like KYC status, accepted offers, etc.
        const kycIsApproved = String(getNested(assessmentToReject, 'kycStatus', '')).toUpperCase() === 'APPROVED';
        if (!kycIsApproved || assessmentToReject.hasAcceptedOffer || assessmentToReject.thisLenderOfferDetails) {
            let reason = "Cannot reject application: ";
            if (!kycIsApproved) reason += "KYC not approved. ";
            if (assessmentToReject.hasAcceptedOffer) reason += "An offer is already accepted. ";
            if (assessmentToReject.thisLenderOfferDetails) reason += "You already submitted an offer. ";
            alert(reason.trim());
            return;
        }
        // --- End Updated Validation ---

        setIsSubmittingRejection(true);
        const lenderId = localStorage.getItem('userId');
        const userId = assessmentToReject.userId;
        const creditLineIdToUpdate = assessmentToReject._id;

        if (!lenderId || !userId || !creditLineIdToUpdate) {
            alert('Missing required information for rejection.'); setIsSubmittingRejection(false); return;
        }

        // --- MODIFIED PAYLOAD ---
        // Payload now ONLY includes the ID and the new reviewHistory entry.
        // The backend MUST handle adding this entry to the array ($push in Mongoose)
        // without changing the main creditLineStatus.

        const payload = {
            status: 'REJECTED',
            notes: rejectionReason,
            reviewedBy: lenderId,
            rejectionReasons: [rejectionReason]
        };
        console.log("Rejection Payload (Adding Review Entry Only):", JSON.stringify(payload, null, 2));
        // --- END MODIFIED PAYLOAD ---

        try {
            // IMPORTANT: Ensure this backend endpoint correctly ADDS to the reviewHistory array
            // and does NOT change the main creditLineStatus.
            const response = await axios.post(
                `${config.apiUrl}/ops/invoiceFinancing/creditLine/${creditLineIdToUpdate}/addReview`,
                payload
            );
            if (!response.data?.success) {
                throw new Error(response.data?.message || "Backend reported failure during rejection update.");
            }
            alert("Application rejection recorded successfully.");
            setShowRejectionModal(false);
            await fetchData(); // Refresh data to update tab contents

        } catch (error) {
            console.error('Error submitting rejection:', error.response?.data || error.message);
            alert(`Error rejecting application: ${getNested(error, 'response.data.message', error.message) || 'An unknown error occurred'}`);
        } finally {
            setIsSubmittingRejection(false);
        }
    }, [selectedAssessmentForRejection, rejectionReason, fetchData]); // Dependencies include the selected assessment and reason


    // --- Render Offer Form Section (Uses Tailwind) ---
    const renderOfferFormSection = () => {
        if (!selectedAssessmentForOffer) return null;
        const assessment = selectedAssessmentForOffer;
        const userDetails = assessment.userDetails;
        const kycApproved = String(getNested(assessment, 'kycStatus', '')).toUpperCase() === 'APPROVED';
        const offerAlreadyAccepted = assessment.hasAcceptedOffer;
        const offerAlreadySubmitted = assessment.thisLenderOfferDetails;
        // Determine if the form should be submittable
        const canSubmit = kycApproved && !offerAlreadyAccepted && !offerAlreadySubmitted;

        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                    {/* Header */}
                    <div className="flex items-center justify-between p-6 border-b border-gray-200">
                        <h2 className="text-xl font-semibold text-gray-900">Create Credit Line Offer</h2>
                        <button
                            onClick={() => setSelectedAssessmentForOffer(null)}
                            className="text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                        {/* User Info Section */}
                        <div className="mb-6">
                            <p className="text-sm text-gray-600 mb-2">
                                Creating Offer for <span className="font-medium text-gray-900">{getNested(userDetails, 'firstName')} {getNested(userDetails, 'lastName')}</span>
                            </p>
                            <div className="flex items-center">
                                <span className="text-sm text-gray-600 mr-2">Overall KYC Status:</span>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${kycApproved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                    {getNested(assessment, 'kycStatus', 'N/A')}
                                </span>
                            </div>
                        </div>

                        {/* Warning Messages */}
                        {offerAlreadyAccepted &&
                            <div className="mb-6 p-3 bg-red-50 border-l-4 border-red-500 rounded-md">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm text-red-700">An offer has already been accepted by the borrower.</p>
                                    </div>
                                </div>
                            </div>
                        }
                        {offerAlreadySubmitted &&
                            <div className="mb-6 p-3 bg-yellow-50 border-l-4 border-yellow-500 rounded-md">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm text-yellow-700">You have already submitted an offer for this application.</p>
                                    </div>
                                </div>
                            </div>
                        }

                        {/* Form Content */}
                        <div className="bg-slate-50 p-6 rounded-lg">
                            <form onSubmit={(e) => { e.preventDefault(); if (canSubmit) { handleSubmitOffer(); } }}>
                                {/* Credit Limit and Tenure Row */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    {/* Credit Limit */}
                                    <div>
                                        <label htmlFor="offerCreditLimit" className="block text-sm font-medium text-gray-700 mb-2">
                                            Credit Limit (QAR)<span className="text-red-500 ml-1">*</span>
                                        </label>
                                        <div className="relative">
                                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                                                QAR
                                            </span>
                                            <input
                                                type="number"
                                                id="offerCreditLimit"
                                                name="creditLimit"
                                                min="1"
                                                step="any"
                                                value={offerData.creditLimit}
                                                onChange={(e) => setOfferData({ ...offerData, creditLimit: e.target.value })}
                                                className="w-full pl-12 pr-3 py-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white disabled:bg-gray-50 disabled:text-gray-500"
                                                placeholder="50,000"
                                                required
                                                disabled={!canSubmit || isSubmittingOffer}
                                            />
                                        </div>
                                    </div>

                                    {/* Tenure */}
                                    <div>
                                        <label htmlFor="offerTenure" className="block text-sm font-medium text-gray-700 mb-2">
                                            Tenure (Days)<span className="text-red-500 ml-1">*</span>
                                        </label>
                                        <div className="relative">
                                            <input
                                                type="number"
                                                id="offerTenure"
                                                name="tenureDays"
                                                min="1"
                                                step="1"
                                                value={offerData.tenureDays}
                                                onChange={(e) => setOfferData({ ...offerData, tenureDays: e.target.value })}
                                                className="w-full pr-12 pl-3 py-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white disabled:bg-gray-50 disabled:text-gray-500"
                                                placeholder="90"
                                                required
                                                disabled={!canSubmit || isSubmittingOffer}
                                            />
                                            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                                                days
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Service Fee and Processing Fee Row */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    {/* Service Fee */}
                                    <div>
                                        <label htmlFor="offerInterestRate" className="block text-sm font-medium text-gray-700 mb-2">
                                            Service Fee %<span className="text-red-500 ml-1">*</span>
                                        </label>
                                        <div className="relative">
                                            <input
                                                type="number"
                                                id="offerInterestRate"
                                                name="interestRate"
                                                min="0"
                                                step="0.01"
                                                value={offerData.interestRate}
                                                onChange={(e) => setOfferData({ ...offerData, interestRate: e.target.value })}
                                                className="w-full pr-8 pl-3 py-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white disabled:bg-gray-50 disabled:text-gray-500"
                                                placeholder="12.5"
                                                required
                                                disabled={!canSubmit || isSubmittingOffer}
                                            />
                                            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                                                %
                                            </span>
                                        </div>
                                    </div>

                                    {/* Processing Fee */}
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Processing Fee<span className="text-red-500 ml-1">*</span>
                                        </label>
                                        <div className="flex gap-2">
                                            <input
                                                type="number"
                                                name="processingFeeValue"
                                                min="0"
                                                step={offerData.processingFeeType === 'percentage' ? '0.01' : '1'}
                                                value={offerData.processingFeeValue}
                                                onChange={(e) => setOfferData({ ...offerData, processingFeeValue: e.target.value })}
                                                className="flex-1 px-3 py-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white disabled:bg-gray-50 disabled:text-gray-500"
                                                placeholder="Fee value"
                                                aria-label="Processing Fee Value"
                                                required
                                                disabled={!canSubmit || isSubmittingOffer}
                                            />
                                            <select
                                                name="processingFeeType"
                                                value={offerData.processingFeeType}
                                                onChange={(e) => setOfferData({ ...offerData, processingFeeType: e.target.value })}
                                                className="px-3 py-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white disabled:bg-gray-50 disabled:text-gray-500"
                                                aria-label="Processing Fee Type"
                                                disabled={!canSubmit || isSubmittingOffer}
                                            >
                                                <option value="flat">QAR</option>
                                                <option value="percentage">%</option>
                                            </select>
                                        </div>
                                        {/* Fee Calculation Display */}
                                        {(() => {
                                            const currentFee = calculateCurrentFee(offerData.processingFeeType, offerData.processingFeeValue, offerData.creditLimit);
                                            const maxAllowed = calculateMaxAllowedFee(offerData.creditLimit);
                                            const isExceeded = currentFee > maxAllowed && offerData.creditLimit > 0;
                                            return (
                                                <p className={`mt-2 text-xs ${isExceeded ? 'text-red-600 font-semibold' : 'text-green-600'}`}>
                                                    Calculated Fee: QAR {currentFee.toFixed(2)}
                                                    {isExceeded &&
                                                        <span className="inline-flex items-center ml-1">
                                                            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                                                            </svg>
                                                            Exceeds Max!
                                                        </span>
                                                    }
                                                </p>
                                            );
                                        })()}
                                    </div>
                                </div>

                                {/* Internal Notes */}
                                <div>
                                    <label htmlFor="offerNotes" className="block text-sm font-medium text-gray-700 mb-2">
                                        Internal Notes (Optional)
                                    </label>
                                    <textarea
                                        id="offerNotes"
                                        name="notes"
                                        value={offerData.notes}
                                        onChange={(e) => setOfferData({ ...offerData, notes: e.target.value })}
                                        rows={3}
                                        className="w-full px-3 py-2.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none bg-white disabled:bg-gray-50 disabled:text-gray-500"
                                        placeholder="Add internal notes about this offer..."
                                        disabled={!canSubmit || isSubmittingOffer}
                                    />
                                </div>
                            </form>
                        </div>

                        {/* Footer Buttons */}
                        <div className="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                            <button
                                type="button"
                                onClick={() => setSelectedAssessmentForOffer(null)}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            >
                                Close
                            </button>
                            <button
                                onClick={() => { if (canSubmit) { handleSubmitOffer(); } }}
                                disabled={(() => {
                                    const requiredMissing = !offerData.creditLimit || !offerData.tenureDays || offerData.interestRate === '' || offerData.processingFeeValue === '';
                                    const feeExceeded = calculateCurrentFee(offerData.processingFeeType, offerData.processingFeeValue, offerData.creditLimit) > calculateMaxAllowedFee(offerData.creditLimit) && offerData.creditLimit > 0;
                                    return !canSubmit || isSubmittingOffer || requiredMissing || feeExceeded;
                                })()}
                                className={`px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${(() => {
                                    const requiredMissing = !offerData.creditLimit || !offerData.tenureDays || offerData.interestRate === '' || offerData.processingFeeValue === '';
                                    const feeExceeded = calculateCurrentFee(offerData.processingFeeType, offerData.processingFeeValue, offerData.creditLimit) > calculateMaxAllowedFee(offerData.creditLimit) && offerData.creditLimit > 0;
                                    const isDisabled = !canSubmit || isSubmittingOffer || requiredMissing || feeExceeded;
                                    return isDisabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-emerald-600 hover:bg-emerald-700';
                                })()
                                    }`}
                            >
                                {isSubmittingOffer ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Submitting...
                                    </>
                                ) : (
                                    'Submit Offer'
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    // --- KYC Status Badge Component ---
    const KycStatusBadge = ({ status }) => {
        return (
            <StatusBadge status={status} />
        );
    };

    // --- Offer/Marketplace Status Badge Component ---
    const MarketplaceStatusBadge = ({ status }) => {
        return <StatusBadge status={status} />;
    };

    // Place this inside your CreditAssessmentPage component or in a scope where it can access helpers

    const flattenCreditAssessmentData = (assessment, maxShareholders, maxBuyers, getNestedFn, formatDateFn, calculateAgeFn, calculateCurrentFeeFn, addDocFn) => {
        const rowData = {};
        const user = assessment.userDetails || {}; // Main user object containing KYC etc.
        const kyc = user.kyc || {}; // KYC object within user
        const businessDetails = kyc.businessDetails || {};
        const incomeDetails = kyc.incomeDetails || {};

        // --- I. Assessment / Credit Line Level Info (from main table context) ---
        rowData['Assessment ID (CL ID)'] = getNestedFn(assessment, '_id', 'N/A');
        rowData['Application Date'] = formatDateFn(getNestedFn(assessment, 'createdAt'));
        rowData['Application Age'] = calculateAgeFn(getNestedFn(assessment, 'createdAt'));
        rowData['Overall Credit Line Status'] = getNestedFn(assessment, 'creditLineStatus', 'N/A'); // Status of the CL application itself
        rowData['Display Status on Page'] = getNestedFn(assessment, 'marketplaceStatus', getNestedFn(assessment, 'displayStatus', 'N/A')); // The status shown in the UI table row
        rowData['User ID (Borrower)'] = getNestedFn(assessment, 'userId', 'N/A');

        // --- II. User/Borrower Basic Info (from assessment.userDetails) ---
        rowData['Borrower First Name'] = getNestedFn(user, 'firstName', 'N/A');
        rowData['Borrower Middle Name'] = getNestedFn(user, 'middleName', 'N/A');
        rowData['Borrower Last Name'] = getNestedFn(user, 'lastName', 'N/A');
        rowData['Borrower Email'] = getNestedFn(user, 'email', 'N/A');
        rowData['Borrower Mobile No'] = getNestedFn(user, 'mobileNo', 'N/A');
        rowData['Borrower Account Active'] = getNestedFn(user, 'isActive', 'N/A');

        // --- III. Detailed KYC Information (from assessment.userDetails, matching KycDetailsViewModal) ---
        rowData['Overall KYC Status (User)'] = getNestedFn(assessment, 'kycStatus', 'N/A'); // Derived status on assessment
        rowData['KYC Verified/Processed On'] = formatDateFn(getNestedFn(kyc, 'verifiedOn'));
        rowData['KYC Overall Notes'] = getNestedFn(kyc, 'verificationNotes', '');
        // Add CAM file info (from table view)
        addDocFn(rowData, getNestedFn(user, 'camFile'), 'CAM Report', getNestedFn, formatDateFn);

        rowData['KYC Bank Account Number'] = getNestedFn(incomeDetails, 'accountNumber', 'N/A');
        rowData['KYC Bank IBAN/IFSC'] = getNestedFn(incomeDetails, 'ifscCode', 'N/A');

        // BusinessDetails Tab in Modal
        rowData['Business Name (KYC)'] = getNestedFn(businessDetails, 'businessName', 'N/A');
        rowData['Legal Entity Name (KYC)'] = getNestedFn(businessDetails, 'legalEntityName', 'N/A');
        rowData['Establishment Name (KYC)'] = getNestedFn(businessDetails, 'establishmentName', 'N/A');
        rowData['Legal Form (KYC)'] = getNestedFn(businessDetails, 'legalForm', 'N/A');
        rowData['Ownership Type (KYC)'] = getNestedFn(businessDetails, 'ownershipType', 'N/A');
        rowData['Sector (KYC)'] = getNestedFn(businessDetails, 'sector', 'N/A');
        rowData['Firm Nationality (KYC)'] = getNestedFn(businessDetails, 'firmNationality', 'N/A');
        rowData['CR Number (KYC)'] = getNestedFn(businessDetails, 'crNumber', 'N/A');
        rowData['CR Issue Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'crIssueDate'));
        rowData['CR Expiry Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'crExpiryDate'));
        rowData['Trade License (TL) Number (User Root)'] = getNestedFn(user, 'licenseNumber', 'N/A');
        rowData['TL Issue Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'tlIssueDate'));
        rowData['TL Expiry Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'tlExpiryDate'));
        rowData['Tax Reg No (TRN) (KYC)'] = getNestedFn(businessDetails, 'taxRegNo', 'N/A');
        rowData['TIN Number (KYC)'] = getNestedFn(businessDetails, 'tinNumber', 'N/A');
        rowData['Establishment ID (KYC)'] = getNestedFn(businessDetails, 'establishmentId', 'N/A');
        rowData['Establishment ID Issue Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'establishmentIdIssueDate'));
        rowData['Establishment ID Expiry Date (KYC)'] = formatDateFn(getNestedFn(businessDetails, 'establishmentIdExpiryDate'));
        rowData['Branch Count (KYC)'] = getNestedFn(businessDetails, 'branchCount', 'N/A');
        rowData['Business Address Line 1 (KYC)'] = getNestedFn(businessDetails, 'businessAddressLine1', 'N/A');
        rowData['Business Address Line 2 (KYC)'] = getNestedFn(businessDetails, 'businessAddressLine2', 'N/A');
        rowData['Business City (KYC)'] = getNestedFn(businessDetails, 'businessCity', 'N/A');
        rowData['Business Country (KYC)'] = getNestedFn(businessDetails, 'businessCountry', 'N/A');

        // BusinessDocs Tab (documents from user root, as per KycDetailsViewModal's rootBusinessDocFields)
        const kycModalBusinessDocFields = [ // Defined here as they are specific to KycDetailsViewModal structure
            { key: 'commercialRegistration', label: 'Commercial Registration (CR)' }, { key: 'tradeLicense', label: 'Trade License' },
            { key: 'taxCard', label: 'Tax Card' }, { key: 'establishmentCard', label: 'Establishment Card' },
            { key: 'memorandumOfAssociation', label: 'MOA' }, { key: 'articleOfAssociation', label: 'AOA' },
            { key: 'otherDocument', label: 'Other User Doc 1' }, { key: 'otherDocumentTwo', label: 'Other User Doc 2' }, // Differentiate from invoice docs
            // Add up to otherDocument10 if they exist in modal
        ];
        kycModalBusinessDocFields.forEach(field => {
            addDocFn(rowData, getNestedFn(user, field.key), `User Business Doc - ${field.label}`, getNestedFn, formatDateFn);
        });

        // FinancialDocs Tab (documents from user root, as per KycDetailsViewModal's rootFinancialDocFields)
        const kycModalFinancialDocFields = [
            { key: 'bankStatement', label: 'Bank Statements (User)' }, { key: 'auditedFinancialReport', label: 'Audited Financial Report (User)' },
            { key: 'commercialCreditReport', label: 'CCR (User)' }, { key: 'cashFlowLedger', label: 'Cash Flow Ledger (User)' },
        ];
        kycModalFinancialDocFields.forEach(field => {
            addDocFn(rowData, getNestedFn(user, field.key), `User Financial Doc - ${field.label}`, getNestedFn, formatDateFn);
        });

        // Shareholders Tab (from user.shareholders)
        const shareholders = getNestedFn(user, 'shareholders', []) || [];
        for (let i = 0; i < maxShareholders; i++) {
            const sh = shareholders[i];
            const prefix = `Shareholder ${i + 1}`;
            if (sh) {
                rowData[`${prefix} - First Name`] = getNestedFn(sh, 'firstName', 'N/A');
                rowData[`${prefix} - Last Name`] = getNestedFn(sh, 'lastName', 'N/A');
                rowData[`${prefix} - Middle Name`] = getNestedFn(sh, 'middleName', 'N/A');
                rowData[`${prefix} - Email`] = getNestedFn(sh, 'email', 'N/A');
                rowData[`${prefix} - KYC Status`] = getNestedFn(sh, 'kycVerificationStatus', 'N/A');
                rowData[`${prefix} - Address Zone`] = getNestedFn(sh, 'address.zone', 'N/A');
                rowData[`${prefix} - Address Street`] = getNestedFn(sh, 'address.streetNo', 'N/A');
                rowData[`${prefix} - Address Building`] = getNestedFn(sh, 'address.buildingNo', 'N/A');
                rowData[`${prefix} - Address Floor`] = getNestedFn(sh, 'address.floorNo', 'N/A');
                rowData[`${prefix} - Added On`] = formatDateFn(getNestedFn(sh, 'addedOn'));
                rowData[`${prefix} - Modified On`] = formatDateFn(getNestedFn(sh, 'modifiedOn'));
                // Shareholder documents
                addDocFn(rowData, getNestedFn(sh, 'passport'), `${prefix} - Passport`, getNestedFn, formatDateFn);
                addDocFn(rowData, getNestedFn(sh, 'qid'), `${prefix} - QID`, getNestedFn, formatDateFn);
                addDocFn(rowData, getNestedFn(sh, 'proofOfAddress'), `${prefix} - Proof of Address`, getNestedFn, formatDateFn);
            } else {
                ['First Name', 'Last Name', 'Middle Name', 'Email', 'KYC Status', 'Address Zone', 'Address Street', 'Address Building', 'Address Floor', 'Address Unit', 'Added On', 'Modified On'].forEach(f => rowData[`${prefix} - ${f}`] = '');
                ['Passport', 'QID', 'Proof of Address'].forEach(docType => {
                    ['File Name', 'Signed URL', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'MIME Type'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = '');
                });
            }
        }

        // Buyers Tab (from user.kyc.buyers)
        const buyers = getNestedFn(kyc, 'buyers', []) || [];
        for (let i = 0; i < maxBuyers; i++) {
            const buyer = buyers[i];
            const prefix = `User Top Buyer ${i + 1}`;
            if (buyer) {
                rowData[`${prefix} - Name`] = getNestedFn(buyer, 'buyerName', 'N/A');
                rowData[`${prefix} - Contact Person`] = getNestedFn(buyer, 'contactPerson', 'N/A');
                rowData[`${prefix} - Contact Phone`] = getNestedFn(buyer, 'contactPhone', 'N/A');
                rowData[`${prefix} - Contact Email`] = getNestedFn(buyer, 'contactEmail', 'N/A');
                rowData[`${prefix} - Reg No`] = getNestedFn(buyer, 'registrationNumber', 'N/A');
                addDocFn(rowData, getNestedFn(buyer, 'companyDocument'), `${prefix} - Company Doc`, getNestedFn, formatDateFn);
            } else {
                ['Name', 'Contact Person', 'Contact Phone', 'Contact Email', 'Reg No'].forEach(f => rowData[`${prefix} - ${f}`] = '');
                ['Company Doc'].forEach(docType => {
                    ['File Name', 'Signed URL', 'Uploaded On', 'Status', 'Notes', 'Verification Date', 'MIME Type'].forEach(field => rowData[`${prefix} - ${docType} - ${field}`] = '');
                });
            }
        }

        // --- IV. Offer Details (from assessment object properties like acceptedOrActiveOfferDetails or thisLenderOfferDetails) ---
        const offerDetails = assessment.acceptedOrActiveOfferDetails || assessment.thisLenderOfferDetails; // Choose the most relevant offer
        if (offerDetails) {
            rowData['Offer ID'] = getNestedFn(offerDetails, '_id', 'N/A');
            rowData['Offer Status'] = getNestedFn(offerDetails, 'status', 'N/A');
            rowData['Offered Credit Limit (QAR)'] = getNestedFn(offerDetails, 'creditLimit', 'N/A');
            rowData['Offered Tenure (Days)'] = getNestedFn(offerDetails, 'tenureDays', 'N/A');
            rowData['Offered Interest Rate (APR %)'] = getNestedFn(offerDetails, 'interestRate', 'N/A');
            rowData['Offer Processing Fee Type'] = getNestedFn(offerDetails, 'processingFee.type', 'N/A');
            rowData['Offer Processing Fee Value'] = getNestedFn(offerDetails, 'processingFee.value', 'N/A');
            if (calculateCurrentFeeFn) { // Check if function is passed
                const calculatedFee = calculateCurrentFeeFn(getNestedFn(offerDetails, 'processingFee.type'), getNestedFn(offerDetails, 'processingFee.value'), getNestedFn(offerDetails, 'creditLimit'));
                rowData['Offer Calculated Processing Fee (QAR)'] = isNaN(calculatedFee) ? 'N/A' : calculatedFee.toFixed(2);
            } else {
                rowData['Offer Calculated Processing Fee (QAR)'] = 'N/A (calc func missing)';
            }
            rowData['Offer Risk Profile'] = getNestedFn(offerDetails, 'riskProfile', 'N/A');
            rowData['Offer Currency'] = getNestedFn(offerDetails, 'currency', 'N/A');
            rowData['Offer Internal Notes'] = getNestedFn(offerDetails, 'notes', '');
            rowData['Offer Created At'] = formatDateFn(getNestedFn(offerDetails, 'createdAt'));
            rowData['Offer Updated At'] = formatDateFn(getNestedFn(offerDetails, 'updatedAt'));
            rowData['Offer Expiry Date'] = formatDateFn(getNestedFn(offerDetails, 'expiryDate'));
            rowData['Offer Accepted Date'] = formatDateFn(getNestedFn(offerDetails, 'acceptedDate'));
            rowData['Offer Contract Accepted Date'] = formatDateFn(getNestedFn(offerDetails, 'loanContractAcceptedDate'));
            rowData['Offer Lender ID'] = getNestedFn(offerDetails, 'lenderId', 'N/A'); // Important to see who made the offer
        } else {
            // Add blank offer fields if no offer is associated or relevant
            ['Offer ID', 'Offer Status', 'Offered Credit Limit (QAR)', 'Offered Tenure (Days)', 'Offered Interest Rate (APR %)',
                'Offer Processing Fee Type', 'Offer Processing Fee Value', 'Offer Calculated Processing Fee (QAR)',
                'Offer Risk Profile', 'Offer Currency', 'Offer Internal Notes', 'Offer Created At', 'Offer Updated At',
                'Offer Expiry Date', 'Offer Accepted Date', 'Offer Contract Accepted Date', 'Offer Lender ID'
            ].forEach(f => rowData[f] = 'N/A');
        }

        // --- V. Rejection/Suspension Details (from assessment.lastReviewDetails or assessment.rejectionDetails) ---
        const reviewInfo = assessment.lastReviewDetails || assessment.rejectionDetails; // Prioritize lastReviewDetails if available
        if (reviewInfo) {
            rowData['Last Review/Rejection Status by This Lender'] = getNestedFn(reviewInfo, 'status', 'N/A');
            rowData['Last Review/Rejection Date by This Lender'] = formatDateFn(getNestedFn(reviewInfo, 'date'));
            rowData['Last Review/Rejection Notes/Reason by This Lender'] = getNestedFn(reviewInfo, 'notes', getNestedFn(reviewInfo, 'reason', 'N/A'));
            rowData['Last Reviewed/Rejected By (ID)'] = getNestedFn(reviewInfo, 'reviewedBy', getNestedFn(reviewInfo, 'rejectedBy', 'N/A'));
        } else {
            ['Last Review/Rejection Status by This Lender', 'Last Review/Rejection Date by This Lender', 'Last Review/Rejection Notes/Reason by This Lender', 'Last Reviewed/Rejected By (ID)'].forEach(f => rowData[f] = 'N/A');
        }

        return rowData;
    };

    // Place this function inside your CreditAssessmentPage component
    const handleExportAssessments = async () => {
        let itemsToExport = [];
        let exportFileNameBase = "CreditAssessments";

        if (activeTopLevelTab === 'applications') {
            itemsToExport = filteredApplicationAssessments;
            exportFileNameBase = "Applications_Under_Review";
        } else if (activeTopLevelTab === 'creditOffers') {
            itemsToExport = filteredOfferAssessments;
            exportFileNameBase = "Credit_Offers";
        } else if (activeTopLevelTab === 'suspended') { // Assuming you have a 'suspended' tab and 'filteredSuspendedAssessments'
            itemsToExport = filteredSuspendedAssessments;
            exportFileNameBase = "Suspended_Applications";
        } else if (activeTopLevelTab === 'rejected') {
            itemsToExport = filteredRejectedAssessments;
            exportFileNameBase = "Rejected_Cases";
        }

        if (!itemsToExport || itemsToExport.length === 0) {
            alert("No data in the current view to export.");
            return;
        }
        setIsExportingExcel(true);

        try {
            let maxShareholders = 0;
            let maxDirectors = 0;
            let maxBuyers = 0;
            // You might need other max counts if assessment.userDetails.kyc has other arrays displayed in KycDetailsViewModal

            itemsToExport.forEach(assessment => {
                const user = assessment.userDetails || {};
                const kyc = user.kyc || {};

                const shareholders = getNested(user, 'shareholders', []) || [];
                if (shareholders.length > maxShareholders) maxShareholders = shareholders.length;

                const buyers = getNested(kyc, 'buyers', []) || [];
                if (buyers.length > maxBuyers) maxBuyers = buyers.length;
            });

            if (maxShareholders === 0) maxShareholders = 1; // For header consistency
            if (maxDirectors === 0) maxDirectors = 1;
            if (maxBuyers === 0) maxBuyers = 1;

            const excelData = itemsToExport.map(assessment =>
                flattenCreditAssessmentData(assessment, maxShareholders, maxBuyers, getNested, formatDate, calculateAge, calculateCurrentFee, addDocumentFieldsToRow)
            );

            if (excelData.length === 0) {
                alert("No data to export after processing.");
                setIsExportingExcel(false);
                return;
            }

            const worksheet = XLSX.utils.json_to_sheet(excelData);

            if (excelData.length > 0 && excelData[0]) {
                const headers = Object.keys(excelData[0]);
                const colWidths = headers.map(header => {
                    const headerLength = header ? header.toString().length : 10;
                    return { wch: headerLength + 2 }; // Padding of +2
                });
                worksheet['!cols'] = colWidths;
            }

            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, exportFileNameBase.replace(/_/g, " ")); // Use a clean sheet name

            const now = new Date();
            const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}`;
            XLSX.writeFile(workbook, `${exportFileNameBase}_${timestamp}.xlsx`);

        } catch (error) {
            console.error("Error exporting assessment data to Excel:", error);
            alert("An error occurred while exporting assessment data. Please check the console.");
        } finally {
            setIsExportingExcel(false);
        }
    };

    // --- MAIN COMPONENT RENDER ---
    return (
        <div className="p-4 md:p-6 min-h-screen">
            <div className="flex justify-between items-center mb-6"> {/* Flex container for title and button */}
                <h1 className="text-2xl font-bold text-gray-800">Credit Applications</h1>
                {/* EXPORT BUTTON - ADD THIS */}
                {((activeTopLevelTab === 'applications' && filteredApplicationAssessments.length > 0) ||
                    (activeTopLevelTab === 'creditOffers' && filteredOfferAssessments.length > 0) ||
                    (activeTopLevelTab === 'suspended' && filteredSuspendedAssessments.length > 0) ||
                    (activeTopLevelTab === 'rejected' && filteredRejectedAssessments.length > 0)) && (
                        <button
                            onClick={handleExportAssessments} // We will define this function
                            disabled={isExportingExcel || loading || isSubmittingOffer || isSubmittingRejection || isSubmittingMoreInfo}
                            className={`ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${(isExportingExcel || loading || isSubmittingOffer || isSubmittingRejection || isSubmittingMoreInfo) ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                        >
                            {isExportingExcel ? (
                                <> {/* SVG Spinner */} <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Exporting... </>
                            ) : (
                                <> {/* Download Icon SVG */} <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2"><path strokeLinecap="round" strokeLinejoin="round" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg> Export Data </>
                            )}
                        </button>
                    )}
                {/* End Export Button */}
            </div>
            <AssessmentFilterSection
                filters={assessmentFilters}
                setFilters={setAssessmentFilters}
                resetFilters={resetAssessmentFilters}
            />

            {/* Top Level Tab Navigation */}
            <div className="mb-6 border-b border-gray-200">
                <nav className="-mb-px flex space-x-6 overflow-x-auto" aria-label="Main Tabs">
                    <button onClick={() => setActiveTopLevelTab('applications')}
                        className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTopLevelTab === 'applications'
                                ? 'border-[#b0cfbf] text-black bg-white'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                        Applications ({loading ? '...' : applicationAssessments.length})
                    </button>
                    <button onClick={() => setActiveTopLevelTab('creditOffers')}
                        className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTopLevelTab === 'creditOffers'
                                ? 'border-[#b0cfbf] text-black bg-white'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                        Credit Offers ({loading ? '...' : offerAssessments.length})
                    </button>
                    {/* --- NEW Suspended Tab --- */}
                    <button onClick={() => setActiveTopLevelTab('suspended')}
                        className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none ${activeTopLevelTab === 'suspended' ? 'border-[#b0cfbf] bg-white text-black' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                        More Information Required ({loading ? '...' : suspendedAssessments.length}) {/* Use new state count */}
                    </button>
                    {/* --- END Suspended Tab --- */}

                    <button onClick={() => setActiveTopLevelTab('rejected')}
                        className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeTopLevelTab === 'rejected'
                                ? 'border-[#b0cfbf] text-black bg-white'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                        Rejected Cases ({loading ? '...' : rejectedAssessments.length})
                    </button>
                </nav>
            </div>

            {/* Loading & Error Display */}
            {loading && (
                <LoadingModal />
            )}
            {fetchError && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong className="font-bold">Error: </strong>
                    <span className="block sm:inline">{fetchError}</span>
                    <button onClick={fetchData} className="ml-4 py-1 px-2 border border-red-300 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200">
                        Retry Fetch
                    </button>
                </div>
            )}

            {/* Content Area */}
            {!loading && !fetchError && (
                <div className='bg-[#f0f0f0] p-6'>
                    {/* Tab 1: Applications */}
                    {activeTopLevelTab === 'applications' && (
                        <div className="space-y-4">
                            <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded text-sm">
                                Review applications with 'APPROVED' KYC status that require a credit line offer. Use 'KYC Details' to view info,
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline align-text-bottom mx-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
                                to submit an offer, or
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline align-text-bottom mx-1 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                                to reject.
                            </div>
                            <div className="shadow border-b border-gray-200 sm:rounded-lg overflow-x-scroll">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application Age</th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">KYC Status</th>
                                            <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Report</th>
                                            <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                            <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {filteredApplicationAssessments.length > 0 ? filteredApplicationAssessments.map((assessment) => {
                                            const kycStatus = getNested(assessment, 'kycStatus', 'N/A');
                                            // Action possible if KYC approved and no offer accepted or submitted by this lender
                                            const canTakeAction = kycStatus === 'APPROVED' && !assessment.hasAcceptedOffer && !assessment.thisLenderOfferDetails;

                                            return (
                                                <tr key={assessment._id} className="hover:bg-gray-50">
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{getNested(assessment, 'userDetails.kyc.businessDetails.businessName', 'N/A')}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                        <div className="font-medium">{[getNested(assessment, 'userDetails.firstName'), getNested(assessment, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
                                                        <div className="text-xs text-gray-500">{getNested(assessment, 'userDetails.email', 'N/A')}</div>
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(assessment, 'userDetails.mobileNo', 'N/A')}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(assessment.createdAt)}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{calculateAge(assessment.createdAt)}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                                        <KycStatusBadge status={kycStatus} />
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                        {assessment.userDetails?.camFile?.signedUrl ? (
                                                            <a
                                                                href={assessment.userDetails.camFile.signedUrl}
                                                                target="_blank"
                                                                rel="noopener noreferrer"
                                                                className="text-[#1B9D51] hover:text-green-600 text-xs flex items-center justify-center space-x-1 underline"
                                                                title="View CAM Report"
                                                            >
                                                                <span>View</span>
                                                                <ArrowTopRightOnSquareIcon className="w-4 h-4 text-[#1B9D51] hover:text-green-600" />
                                                            </a>
                                                        ) : (
                                                            <div className="flex flex-col items-center">
                                                                <span className="text-xs text-gray-400 italic mb-1">N/A</span>
                                                            </div>
                                                        )}
                                                    </td>

                                                    <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                        <button
                                                            onClick={() => handleViewKycDetails(assessment)}
                                                            className="text-[#1B9D51] hover:text-green-600 disabled:text-gray-400 disabled:cursor-not-allowed"
                                                            title="View KYC Details"
                                                            disabled={!assessment.userDetails}
                                                        >
                                                            KYC Details
                                                        </button>
                                                    </td>

                                                    <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                        {canTakeAction ? (
                                                            <div className="flex justify-center items-center space-x-2">
                                                                {/* Reject Button */}
                                                                <button
                                                                    onClick={() => handleOpenRejectionModal(assessment)}
                                                                    className="p-1 rounded-full border-2 border-red-600 text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-red-500"
                                                                    title="Reject Application"
                                                                >
                                                                    <XMarkIcon className="h-3.5 w-3.5" strokeWidth={3} />
                                                                </button>

                                                                {/* More Info Button */}
                                                                <button
                                                                    onClick={() => handleOpenMoreInfoModal(assessment)}
                                                                    className="p-1 rounded-full border-2 border-yellow-500 text-yellow-500 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-yellow-500"
                                                                    title="Request More Information"
                                                                >
                                                                    <DocumentIcon className="h-3.5 w-3.5" strokeWidth={2} />
                                                                </button>

                                                                {/* Offer Button */}
                                                                <button
                                                                    onClick={() => handleOpenOfferModal(assessment)}
                                                                    className="p-1 rounded-full border-2 border-green-600 text-green-600 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-green-500"
                                                                    title="Submit Offer"
                                                                >
                                                                    <CheckIcon className="h-3.5 w-3.5" strokeWidth={2} />
                                                                </button>
                                                            </div>
                                                        ) : (
                                                            <span className="text-xs text-gray-400 italic">N/A</span>
                                                        )}
                                                    </td>


                                                </tr>
                                            );
                                        }) : (
                                            <tr><td colSpan="9" className="text-center py-10 px-4 text-sm text-gray-500 italic">No applications currently require an offer.</td></tr>)}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {/* Tab 2: Credit Offers */}
                    {activeTopLevelTab === 'creditOffers' && (
                        <div className="space-y-4">
                            <div className="bg-blue-50 border border-blue-200 text-blue-800 px-4 py-3 rounded text-sm">
                                This section lists credit lines where you have submitted an offer, or where an offer (from any lender) has been accepted/activated. This is a read-only view.
                            </div>
                            <div className="shadow border-b border-gray-200 sm:rounded-lg overflow-x-scroll">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Age</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Offer Limit (QAR)</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Service Charge (%)</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Offer Tenure (Days)</th>
                                            <th scope="col" className="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase tracking-wider">Credit Report</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            {/* No Action Column */}
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {filteredOfferAssessments.length > 0 ? filteredOfferAssessments.map((assessment) => {
                                            // This self-invoking function keeps the logic scoped to each row
                                            return (() => {
                                                const loggedInLenderId = localStorage.getItem('userId');
                                                const offerToShow = assessment.acceptedOrActiveOfferDetails || assessment.thisLenderOfferDetails;
                                                const isAcceptedByAnotherLender =
                                                    assessment.hasAcceptedOffer &&
                                                    assessment.acceptedOrActiveOfferDetails?.lenderId &&
                                                    String(assessment.acceptedOrActiveOfferDetails.lenderId) !== loggedInLenderId;

                                                return (
                                                    <tr
                                                        key={assessment._id}
                                                        // The 'relative' class is crucial for the overlay positioning
                                                        className={`relative hover:bg-gray-50 ${isAcceptedByAnotherLender ? 'opacity-90' : (assessment.hasAcceptedOffer ? 'bg-green-50' : '')}`}
                                                    >
                                                        {/* The first <td> now holds the overlay logic */}
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                            {/* The overlay is now correctly placed inside a <td> */}
                                                            {isAcceptedByAnotherLender && (
                                                                <div
                                                                    className="absolute inset-0 flex items-center justify-center z-10"
                                                                    style={{ backgroundColor: 'rgba(249, 250, 251, 0.75)' }}
                                                                >
                                                                    <p className="bg-gray-700 text-white font-semibold px-4 py-2 rounded-md shadow-lg text-center">
                                                                        This MSME has accepted an offer by another lender
                                                                    </p>
                                                                </div>
                                                            )}
                                                            {/* The original content of the first cell is still here */}
                                                            {getNested(assessment, 'userDetails.kyc.businessDetails.businessName', 'N/A')}
                                                        </td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                            <div className="font-medium">{[getNested(assessment, 'userDetails.firstName'), getNested(assessment, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
                                                            <div className="text-sm text-gray-500">{getNested(assessment, 'userDetails.email', 'N/A')}</div>
                                                        </td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(assessment.createdAt)}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{calculateAge(assessment.createdAt)}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(offerToShow, 'creditLimit', 'N/A')}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(offerToShow, 'interestRate', 'N/A')}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(offerToShow, 'tenureDays', 'N/A')}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                            {assessment.userDetails?.camFile?.signedUrl ? (
                                                                <a
                                                                    href={assessment.userDetails.camFile.signedUrl}
                                                                    target="_blank"
                                                                    rel="noopener noreferrer"
                                                                    className="text-[#1B9D51] hover:text-green-600 text-xs flex items-center justify-center space-x-1 underline"
                                                                    title="View CAM Report"
                                                                >
                                                                    <span>View</span>
                                                                    <ArrowTopRightOnSquareIcon className="w-4 h-4 text-[#1B9D51] hover:text-green-600" />
                                                                </a>
                                                            ) : (
                                                                <div className="flex flex-col items-center">
                                                                    <span className="text-xs text-gray-400 italic mb-1">N/A</span>
                                                                </div>
                                                            )}
                                                        </td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                            <button
                                                                onClick={() => handleViewKycDetails(assessment)}
                                                                className="text-indigo-600 hover:text-indigo-900 disabled:text-gray-400 disabled:cursor-not-allowed"
                                                                title="View KYC Details"
                                                                disabled={!assessment.userDetails}
                                                            >
                                                                KYC Details
                                                            </button>
                                                        </td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                                                            <MarketplaceStatusBadge status={assessment.marketplaceStatus} />
                                                        </td>
                                                    </tr>
                                                );
                                            })();
                                        }) : (
                                            // The colspan here should be 10 to match the number of headers
                                            <tr><td colSpan="10" className="text-center py-10 px-4 text-sm text-gray-500 italic">No submitted or accepted credit offers found.</td></tr>
                                        )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {/* --- NEW Tab 2: Suspended Cases --- */}
                    {activeTopLevelTab === 'suspended' && (
                        <div className="space-y-4">
                            <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded text-sm">
                                These applications are marked as more information needed. Review the notes, view KYC details, and proceed with Offer / Reject actions once requirements are met.
                            </div>
                            <div className="shadow border-b border-gray-200 sm:rounded-lg overflow-x-scroll">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Marked On</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Marking Notes</th>
                                            <th scope="col" className="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                            <th scope="col" className="px-4 py-3 text-center text-sm font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {/* Use the filtered array for suspended cases */}
                                        {filteredSuspendedAssessments.length > 0 ? filteredSuspendedAssessments.map((assessment) => {
                                            // Logic to determine if actions can be taken (might be same as 'applications' or slightly different)
                                            // For now, let's assume same conditions apply: KYC approved, no accepted offer, no offer submitted by *this* lender.
                                            // This allows moving from Suspended -> Offer or Suspended -> Reject after review.

                                            return (() => {
                                                const loggedInLenderId = localStorage.getItem('userId');
                                                const isAcceptedByAnotherLender =
                                                    assessment.hasAcceptedOffer &&
                                                    assessment.acceptedOrActiveOfferDetails?.lenderId &&
                                                    String(assessment.acceptedOrActiveOfferDetails.lenderId) !== loggedInLenderId;
                                                const kycStatus = getNested(assessment, 'kycStatus', 'N/A');
                                                const canTakeAction = kycStatus === 'APPROVED' && !assessment.hasAcceptedOffer && !assessment.thisLenderOfferDetails;

                                                return (
                                                    <tr key={assessment._id} className={`relative hover:bg-yellow-50 ${isAcceptedByAnotherLender ? 'opacity-90' : ''}`}>
                                                        {isAcceptedByAnotherLender && (
                                                            <div
                                                                className="absolute inset-0 flex items-center justify-center z-10 rounded-lg"
                                                                style={{ backgroundColor: 'rgba(240, 240, 240, 0.8)' }}
                                                            >
                                                                <span className="bg-gray-700 text-white font-semibold px-4 py-2 rounded-md shadow-lg text-center">
                                                                    This MSME has accepted an offer by another lender
                                                                </span>
                                                            </div>
                                                        )}
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{getNested(assessment, 'userDetails.kyc.businessDetails.businessName', 'N/A')}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                            <div className="font-medium">{[getNested(assessment, 'userDetails.firstName'), getNested(assessment, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
                                                            <div className="text-sm text-gray-500">{getNested(assessment, 'userDetails.email', 'N/A')}</div>
                                                        </td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(assessment, 'userDetails.mobileNo', 'N/A')}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(assessment.createdAt)}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(getNested(assessment, 'lastReviewDetails.date'))}</td>
                                                        <td className="px-4 py-3 text-sm text-gray-500 max-w-xs break-words whitespace-normal">{getNested(assessment, 'lastReviewDetails.notes', 'N/A')}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                            <button
                                                                onClick={() => handleViewKycDetails(assessment)}
                                                                className="text-indigo-600 hover:text-indigo-900 disabled:text-gray-400 disabled:cursor-not-allowed"
                                                                title="View KYC Details"
                                                                disabled={!assessment.userDetails}
                                                            >
                                                                KYC Details
                                                            </button>
                                                        </td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                            {canTakeAction ? (
                                                                <div className="flex justify-center items-center space-x-2">
                                                                    <button onClick={() => handleOpenRejectionModal(assessment)} className="p-1 rounded-full border-2 border-red-600 text-red-600 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-red-500" title="Reject Application">
                                                                        <XMarkIcon className="h-3.5 w-3.5" strokeWidth={3} />
                                                                    </button>
                                                                    <button onClick={() => handleOpenMoreInfoModal(assessment)} className="p-1 rounded-full border-2 border-yellow-500 text-yellow-500 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-yellow-500" title="Request More Information">
                                                                        <DocumentIcon className="h-3.5 w-3.5" strokeWidth={2} />
                                                                    </button>
                                                                    <button onClick={() => handleOpenOfferModal(assessment)} className="p-1 rounded-full border-2 border-green-600 text-green-600 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-green-500" title="Submit Offer">
                                                                        <CheckIcon className="h-3.5 w-3.5" strokeWidth={2} />
                                                                    </button>
                                                                </div>
                                                            ) : <span className="text-sm text-gray-400 italic">N/A</span>}
                                                        </td>
                                                    </tr>
                                                );
                                            })();
                                        }) : (
                                            <tr><td colSpan="8" className="text-center py-10 px-4 text-sm text-gray-500 italic">No applications currently marked as "More Information Needed" by you.</td></tr>
                                        )}
                                    </tbody>
                                </table>
                            </div> {/* End Shadow/Table Container */}
                        </div> // End space-y-4 for suspended tab
                    )}
                    {/* --- END Suspended Tab --- */}


                    {/* Tab 3: Rejected Cases */}
                    {activeTopLevelTab === 'rejected' && (
                        <div className="space-y-4">
                            <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded text-sm">
                                This section lists applications that were rejected specifically by you. This is a read-only view.
                            </div>
                            <div className="shadow border-b border-gray-200 sm:rounded-lg overflow-x-scroll">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Business Name</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Applied</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Details</th>
                                            <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Rejected On</th>
                                            {/* <th scope="col" className="px-4 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">Rejection Reason</th> */}
                                            {/* No Action Column */}
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {filteredRejectedAssessments.length > 0 ? filteredRejectedAssessments.map((assessment) => {
                                            // WRAP THE RETURN IN AN IIFE TO DEFINE CONSTANTS
                                            return (() => {
                                                const loggedInLenderId = localStorage.getItem('userId');
                                                const isAcceptedByAnotherLender =
                                                    assessment.hasAcceptedOffer &&
                                                    assessment.acceptedOrActiveOfferDetails?.lenderId &&
                                                    String(assessment.acceptedOrActiveOfferDetails.lenderId) !== loggedInLenderId;

                                                return (
                                                    <tr
                                                        key={assessment._id}
                                                        // ADD 'relative' AND a conditional 'opacity-50'
                                                        className={`relative bg-red-50 hover:bg-red-100 ${isAcceptedByAnotherLender ? 'opacity-90' : ''}`}
                                                    >
                                                        {/* ADD THE CONDITIONAL OVERLAY DIV */}
                                                        {isAcceptedByAnotherLender && (
                                                            <div
                                                                className="absolute inset-0 flex items-center justify-center z-10 rounded-lg"
                                                                style={{ backgroundColor: 'rgba(240, 240, 240, 0.8)' }}
                                                            >
                                                                <span className="bg-gray-700 text-white font-semibold px-4 py-2 rounded-md shadow-lg text-center">
                                                                    This MSME has accepted an offer by another lender
                                                                </span>
                                                            </div>
                                                        )}
                                                        {/* ALL ORIGINAL <td> ELEMENTS ARE PRESERVED BELOW */}
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{getNested(assessment, 'userDetails.kyc.businessDetails.businessName', 'N/A')}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                            <div className="font-medium">{[getNested(assessment, 'userDetails.firstName'), getNested(assessment, 'userDetails.lastName')].filter(Boolean).join(' ')}</div>
                                                            <div className="text-sm text-gray-500">{getNested(assessment, 'userDetails.email', 'N/A')}</div>
                                                        </td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{getNested(assessment, 'userDetails.mobileNo', 'N/A')}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(assessment.createdAt)}</td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-center text-sm font-medium">
                                                            <button
                                                                onClick={() => handleViewKycDetails(assessment)}
                                                                className="text-indigo-600 hover:text-indigo-900 disabled:text-gray-400 disabled:cursor-not-allowed"
                                                                title="View KYC Details"
                                                                disabled={!assessment.userDetails}
                                                            >
                                                                KYC Details
                                                            </button>
                                                        </td>
                                                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{formatDate(getNested(assessment, 'updatedAt'))}</td>
                                                    </tr>
                                                );
                                            })();
                                        }) : (
                                            <tr><td colSpan="6" className="text-center py-10 px-4 text-sm text-gray-500 italic">No applications rejected by you found.</td></tr>
                                        )}
                                    </tbody>
                                </table>
                            </div> {/* End Shadow/Table Container */}
                        </div> // End space-y-4 for rejected tab
                    )} {/* End Rejected Cases Tab Content */}

                </div> // End conditional rendering for !loading && !fetchError
            )} {/* End Loading/Error Check */}

            {/* --- MODALS --- */}

            {/* KYC Details View Modal */}
            {showKycDetailsModal && selectedUserForKycModal && (
                <KycDetailsViewModal
                    show={showKycDetailsModal}
                    user={selectedUserForKycModal}
                    onClose={() => { setShowKycDetailsModal(false); setSelectedUserForKycModal(null); }}
                    activeTabState={[activeKycModalTab, setActiveKycModalTab]} // Pass state and setter
                    isGeneratingPdf={isGeneratingPdf}
                    setIsGeneratingPdf={setIsGeneratingPdf}
                />
            )}

            {showMoreInfoModal && selectedAssessmentForMoreInfo && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
                    {/* Modal Container */}
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden">
                        {/* Modal Header */}
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h2 className="text-lg font-semibold text-gray-800">
                                Request More Information
                            </h2>
                        </div>

                        {/* Modal Body */}
                        <div className="p-6 space-y-4">
                            <p className="text-sm text-gray-600">
                                Application for borrower: <span className="font-medium">{getNested(selectedAssessmentForMoreInfo, 'userDetails.firstName')} {getNested(selectedAssessmentForMoreInfo, 'userDetails.lastName')}</span> will be marked as 'More Information Needed' pending further information.
                            </p>
                            <div>
                                <label htmlFor="moreInfoNotes" className="block text-sm font-medium text-gray-700 mb-1">
                                    Required Information / Notes <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    id="moreInfoNotes"
                                    rows="5" // Increased rows slightly
                                    value={moreInfoNotes}
                                    onChange={(e) => setMoreInfoNotes(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100"
                                    placeholder="Specify what additional information or clarification is needed from the borrower..."
                                    disabled={isSubmittingMoreInfo}
                                ></textarea>
                                {moreInfoNotes.trim().length === 0 && <p className="text-sm text-red-500 mt-1">Notes are required.</p>}
                            </div>
                        </div>

                        {/* Modal Footer */}
                        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                            <button
                                onClick={() => { setShowMoreInfoModal(false); setSelectedAssessmentForMoreInfo(null); setMoreInfoNotes(''); }}
                                type="button"
                                className="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                disabled={isSubmittingMoreInfo}
                            >
                                Close
                            </button>
                            <button
                                onClick={handleSubmitMoreInfo} // Use the new handler
                                type="button"
                                className="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={!moreInfoNotes.trim() || isSubmittingMoreInfo}
                            >
                                {isSubmittingMoreInfo ? 'Submitting...' : 'Submit Request'}
                            </button>
                        </div>
                    </div> {/* End Modal Container */}
                </div> // End Modal Backdrop
            )}

            {showOfferModal && selectedAssessmentForOffer && (
                renderOfferFormSection()
            )}

            {/* Reject Application Modal */}
            {showRejectionModal && selectedAssessmentForRejection && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
                    {/* Modal Container */}
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden">
                        {/* Modal Header */}
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h2 className="text-lg font-semibold text-gray-800">
                                Reject Credit Line Application
                            </h2>
                        </div>

                        {/* Modal Body */}
                        <div className="p-6 space-y-4">
                            <p className="text-sm text-gray-600">
                                Please provide a reason for rejecting the application for borrower: <span className="font-medium">{getNested(selectedAssessmentForRejection, 'userDetails.firstName')} {getNested(selectedAssessmentForRejection, 'userDetails.lastName')}</span>.
                            </p>
                            <div>
                                <label htmlFor="rejectionReason" className="block text-sm font-medium text-gray-700 mb-1">
                                    Rejection Reason <span className="text-red-500">*</span>
                                </label>
                                <textarea
                                    id="rejectionReason"
                                    rows="4"
                                    value={rejectionReason}
                                    onChange={(e) => setRejectionReason(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100"
                                    placeholder="Enter reason..."
                                    disabled={isSubmittingRejection}
                                ></textarea>
                                {rejectionReason.trim().length === 0 && <p className="text-sm text-red-500 mt-1">Rejection reason is required.</p>}
                            </div>
                        </div>

                        {/* Modal Footer */}
                        <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                            <button
                                onClick={() => { setShowRejectionModal(false); setSelectedAssessmentForRejection(null); setRejectionReason(''); }}
                                type="button"
                                className="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                disabled={isSubmittingRejection}
                            >
                                Close
                            </button>
                            <button
                                onClick={handleSubmitRejection}
                                type="button"
                                className="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={!rejectionReason.trim() || isSubmittingRejection}
                            >
                                {isSubmittingRejection ? 'Rejecting...' : 'Reject Application'}
                            </button>
                        </div>
                    </div> {/* End Modal Container */}
                </div> // End Modal Backdrop
            )}

        </div> // End Main Page Container
    );
}
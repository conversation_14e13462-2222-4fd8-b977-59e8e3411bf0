import React from 'react';
// import {
//   CheckCircleIcon,
//   ClockIcon,
//   ArrowPathIcon,
//   ExclamationTriangleIcon,
//   QuestionMarkCircleIcon,
//   XCircleIcon
// } from '@heroicons/react/24/outline';

// Shared status color mapping function
export const getStatusClass = (status) => {
  status = status?.toUpperCase() || 'UNKNOWN';
  switch (status) {
    // Offer Statuses
    case 'ACTIVE': return 'bg-[#50c985] text-white';
    case 'DISBURSED': return 'bg-[#27a460] text-white';
    case 'LOAN_CONTRACT_ACCEPTED': return 'bg-blue-100 text-blue-800';
    case 'READY_FOR_DISBURSAL': return 'bg-[#46a8f1] text-white';
    case 'INITIATED_FUND_TRANSFER': return 'bg-cyan-100 text-cyan-800';
    case 'LOAN_IN_PROGRESS': return 'bg-indigo-100 text-indigo-800';
    case 'PAID': return 'bg-[#50c985] text-white';
    case 'REJECTED':
    case 'LOAN_CANCELLED': return 'bg-red-100 text-red-800';
    case 'EXPIRED':
    case 'WRITTEN_OFF': return 'bg-gray-400 text-gray-800';

    // EMI Statuses
    case 'PENDING': return 'bg-orange-100 text-orange-800';
    case 'PARTIAL': return 'bg-purple-100 text-purple-800';

    // Invoice Statuses
    case 'VERIFICATION_PENDING_ANCHOR': return 'bg-gray-100 text-gray-500';
    case 'VERIFIED_ANCHOR': return 'bg-lime-100 text-lime-800';
    case 'VERIFICATION_PENDING_LENDER': return 'bg-teal-100 text-teal-800';
    case 'ACCEPTED_LENDER':  return 'bg-[#50c985] text-white';
    case 'REJECTED_ANCHOR':
    case 'REJECTED_LENDER': return 'bg-pink-100 text-pink-800';

    // KYC Statuses
    case 'APPROVED': return 'bg-[#50c985] text-white';
    case 'VERIFIED': return 'bg-[#50c985] text-white';
    case 'UNDER_REVIEW': return 'bg-blue-100 text-blue-800';
    case 'REVIEW': return 'bg-yellow-100 text-yellow-800';
    case 'INFO_NEEDED': return 'bg-yellow-100 text-yellow-800';
    case 'SUBMITTED': return 'bg-blue-100 text-blue-800';
    case 'INITIATED': return 'bg-gray-100 text-gray-600';
    case 'REINITIATED': return 'bg-gray-100 text-gray-600';

    // Default
    default: return 'bg-gray-200 text-gray-700';
  }
};

// Shared StatusBadge component
const StatusBadge = ({ status }) => {
  const upperStatus = status?.toUpperCase() || 'UNKNOWN';
  let colorClass = getStatusClass(upperStatus);
  // let icon = <QuestionMarkCircleIcon className="w-5 h-5 mr-1.5" aria-hidden="true" />;
  let displayText = status?.replace(/_/g, ' ') || 'Unknown';

  // Determine appropriate icon based on status
  switch (upperStatus) {
    // Completed/Success statuses
    case 'PAID':
    case 'ACTIVE':
    case 'VERIFIED_ANCHOR':
    case 'ACCEPTED_LENDER':
    case 'APPROVED':
    case 'VERIFIED':
    case 'DISBURSED':
      // icon = <CheckCircleIcon className="w-5 h-5 mr-1.5" aria-hidden="true" />;
      break;

    // In-progress statuses
    case 'LOAN_IN_PROGRESS':
    case 'LOAN_CONTRACT_ACCEPTED':
    case 'UNDER_REVIEW':
    case 'SUBMITTED':
      // icon = <ArrowPathIcon className="w-5 h-5 mr-1.5" aria-hidden="true" />;
      break;

    // Waiting statuses
    case 'PENDING':
    case 'READY_FOR_DISBURSAL':
    case 'VERIFICATION_PENDING_LENDER':
    case 'VERIFICATION_PENDING_ANCHOR':
    case 'INITIATED_FUND_TRANSFER':
    case 'INITIATED':
    case 'REINITIATED':
      // icon = <ClockIcon className="w-5 h-5 mr-1.5" aria-hidden="true" />;
      break;

    // Warning statuses
    case 'PARTIAL':
    case 'INFO_NEEDED':
    case 'REVIEW':
    case 'MORE_INFO_NEEDED_LENDER':
      // icon = <ExclamationTriangleIcon className="w-5 h-5 mr-1.5" aria-hidden="true" />;
      break;

    // Error/Rejected statuses
    case 'REJECTED':
    case 'REJECTED_ANCHOR':
    case 'REJECTED_LENDER':
    case 'LOAN_CANCELLED':
    case 'EXPIRED':
    case 'WRITTEN_OFF':
      // icon = <XCircleIcon className="w-5 h-5 mr-1.5" aria-hidden="true" />;
      break;

    // Default for unknown statuses
    default:
      // icon = <QuestionMarkCircleIcon className="w-5 h-5 mr-1.5" aria-hidden="true" />;
      break;
  }

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-medium ${colorClass} whitespace-nowrap`}>
      {displayText}
    </span>
  );
};

export default StatusBadge;

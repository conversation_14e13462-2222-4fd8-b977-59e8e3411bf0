import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router"; // << CHANGED: Using Next.js router
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import {
  ArrowLeftIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import LoadingModal from "../../components/Loading"; // Adjust path as needed for your project structure
import StatusBadge from "../../components/StatusBadge"; // Adjust path as needed
import config from "../../../config.json"; // Adjust path as needed

// formatDate and formatCurrency functions remain UNCHANGED
const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid Date";
    return date.toLocaleDateString("en-CA"); // Format YYYY-MM-DD
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return "Invalid Date";
  }
};

const formatCurrency = (value, currency = "QAR") => {
  const numValue =
    typeof value === "string" ? parseFloat(value.replace(/,/g, "")) : value;
  if (numValue === null || numValue === undefined || isNaN(numValue))
    return "N/A";
  return `${currency} ${Number(numValue).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

// isValidObjectId function (as you provided it, assuming it's used)
const isValidObjectId = (id) => /^[0-9a-fA-F]{24}$/.test(id);

// Define this path based on your actual payments overview page
const PAYMENTS_OVERVIEW_PATH = '/payments';

export default function UpdateEmiPage() {
  const router = useRouter(); // << CHANGED: Using Next.js router instance

  // const location = useLocation(); // << REMOVED
  // const { offer: selectedOfferForPage } = location.state || {}; // << REMOVED

  const [selectedOffer, setSelectedOffer] = useState(null);
  const [isLoadingOffer, setIsLoadingOffer] = useState(true); // Added for better UX
  const [feedbackMessage, setFeedbackMessage] = useState({
    type: "",
    text: "",
    title: "",
  });
  const [showUploadPromptModal, setShowUploadPromptModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const repaymentFileRef = useRef(null);
  const [currentUploadType] = useState("repayment");
  console.log(currentUploadType); // Your existing console.log

  useEffect(() => {
    // router.isReady ensures that router.query is populated in Next.js on client-side render
    if (!router.isReady) {
      setIsLoadingOffer(true); // Keep loading if router is not ready
      return;
    }

    const { offerId } = router.query; // Get offerId from dynamic route segment or query param

    if (offerId) {
      try {
        const storedOfferDataString = sessionStorage.getItem(`selectedOfferData_${offerId}`);
        if (storedOfferDataString) {
          const offerData = JSON.parse(storedOfferDataString);
          if (offerData && offerData.offerInfo && offerData.offerInfo.offerId === offerId) {
            setSelectedOffer(offerData);
          } else {
            console.warn("Data in session storage does not match offerId or is invalid:", offerId);
            setFeedbackMessage({
              type: "error",
              title: "Data Mismatch",
              text: `Could not load correct details for offer ${offerId}. Please try navigating again.`,
            });
            setSelectedOffer(null); // Ensure no stale data is shown
          }
        } else {
          setFeedbackMessage({
            type: "error",
            title: "Error Loading Offer",
            text: `Details for offer ${offerId} not found in session. This can happen if you refreshed the page or navigated directly. Please try navigating from the payments page again.`,
          });
          setSelectedOffer(null);
          // PRODUCTION CONSIDERATION: Implement an API call here to fetch offer details by `offerId`
          // as a fallback if session storage is empty.
          // e.g., fetchOfferFromAPI(offerId).then(data => setSelectedOffer(data)).catch(err => setFeedbackMessage(...));
        }
      } catch (error) {
        console.error("Error reading or parsing offer data from sessionStorage:", error);
        setFeedbackMessage({
          type: "error",
          title: "Storage Error",
          text: "Could not load offer details due to a local storage issue.",
        });
        setSelectedOffer(null);
      }
    } else {
      setFeedbackMessage({
        type: "error",
        title: "Missing Information",
        text: "Offer ID not found in URL. Cannot load details.",
      });
      setSelectedOffer(null);
    }
    setIsLoadingOffer(false);
  }, [router.isReady, router.query]); // Depend on router.isReady and router.query

  const handleGoBack = () => {
    router.push(PAYMENTS_OVERVIEW_PATH); // << CHANGED: Using router.push
  };

  // ALL THE FOLLOWING FUNCTIONS AND JSX STRUCTURE REMAIN EXACTLY AS YOU PROVIDED,
  // ONLY ENSURING THEY USE `selectedOffer` state correctly.

  const handleDownloadRepaymentTemplate = () => {
    const currentTimestamp = new Date().toISOString();

    const worksheet = XLSX.utils.aoa_to_sheet([
      ["offerId", "emiNumber", "UTR", "paymentTimestamp", "paidAmount"],
      ["", "", "", currentTimestamp, ""], // First row with current timestamp
    ]);

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Repayment Data");

    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });

    const dataBlob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
    });

    saveAs(dataBlob, `repayment_upload_template_${selectedOffer?.offerInfo?.offerId || 'offer'}.xlsx`);

    setFeedbackMessage({
      type: "info",
      text: "Repayment template downloaded.",
      title: "Download Started"
    });
  };


  const handleRepaymentUpload = async (event) => {
    const file = event.target.files[0];
    setShowUploadPromptModal(false);

    if (!file) {
      if (repaymentFileRef.current) repaymentFileRef.current.value = "";
      return;
    }

    if (!selectedOffer) {
      setFeedbackMessage({
        type: "error",
        title: "Upload Error",
        text: "Offer context is missing. Please ensure offer details are loaded.",
      });
      setShowStatusModal(true);
      setIsUploading(false);
      if (repaymentFileRef.current) repaymentFileRef.current.value = "";
      return;
    }

    setIsUploading(true);

    const reader = new FileReader();
    reader.onload = async (e) => {
      const validationErrors = [];
      let payload = [];

      try {
        const currentEmiDetails = selectedOffer?.offerInfo?.emiDetails;
        if (!Array.isArray(currentEmiDetails)) {
          throw new Error(
            `EMI details not found or invalid for selected offer ${selectedOffer?.offerInfo?.offerId}`
          );
        }
        const sortedCurrentEmiDetails = [...currentEmiDetails].sort(
          (a, b) => (a.emiNumber || 0) - (b.emiNumber || 0)
        );

        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array", cellDates: true });
        const sheetName = workbook.SheetNames[0];
        if (!sheetName) throw new Error("Cannot find sheet in Excel file.");
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: null,
          raw: false,
        });

        if (!jsonData || jsonData.length < 2)
          throw new Error("Excel file appears empty or lacks a header row.");

        const header = jsonData[0].map((h) => String(h || "").trim());
        const expectedHeader = [
          "offerId",
          "emiNumber",
          "UTR",
          "paymentTimestamp",
          "paidAmount",
        ];
        if (JSON.stringify(header) !== JSON.stringify(expectedHeader)) {
          throw new Error(
            `Invalid header row. Expected columns: ${expectedHeader.join(", ")}`
          );
        }

        const dataRows = jsonData.slice(1);
        const parseNumericValue = (value) => {
          if (
            value === null ||
            value === undefined ||
            String(value).trim() === ""
          ) {
            return NaN;
          }
          const stringValue = String(value).trim();
          const normalizedValue = stringValue.replace(/,/g, "");
          return parseFloat(normalizedValue);
        };

        dataRows.forEach((row, index) => {
          const rowNum = index + 2;
          const offerIdFromFile = row?.[0];
          const excelEmiNumberInput = row?.[1];
          const utr = row?.[2];
          const timestampInput = row?.[3];
          const paidAmountFromFile = row?.[4];

          let rowIsValid = true;
          let rowErrors = [];
          let parsedTimestamp = null;
          let parsedExcelEmiNum = NaN;
          let parsedPaidAmt = NaN;
          let targetEmiNumber = null;

          if (
            !offerIdFromFile ||
            !isValidObjectId(String(offerIdFromFile).trim()) ||
            String(offerIdFromFile).trim() !== selectedOffer.offerInfo.offerId
          ) {
            rowErrors.push(
              `Invalid/Missing offerId or doesn't match selected offer (${selectedOffer.offerInfo.offerId})`
            );
            rowIsValid = false;
          }
          parsedExcelEmiNum = parseNumericValue(excelEmiNumberInput);
          if (isNaN(parsedExcelEmiNum) || parsedExcelEmiNum <= 0) {
            rowErrors.push("Invalid/Missing positive emiNumber in Excel file");
            rowIsValid = false;
          }
          if (!utr || String(utr).trim() === "") {
            rowErrors.push("Missing UTR");
            rowIsValid = false;
          }
          if (
            timestampInput === null ||
            timestampInput === undefined ||
            String(timestampInput).trim() === ""
          ) {
            rowErrors.push("Missing paymentTimestamp");
            rowIsValid = false;
          } else {
            let date = new Date(timestampInput);
            if (
              isNaN(date.getTime()) &&
              typeof timestampInput === "number" &&
              timestampInput > 25569 // Excel date serial number check
            ) {
              const excelEpoch = new Date(1899, 11, 30);
              const jsTimestamp =
                excelEpoch.getTime() + timestampInput * 24 * 60 * 60 * 1000;
              date = new Date(jsTimestamp);
            }
            if (isNaN(date.getTime())) {
              rowErrors.push(
                "Invalid paymentTimestamp format (use YYYY-MM-DD, MM/DD/YYYY, or Excel date number)"
              );
              rowIsValid = false;
            } else {
              parsedTimestamp = date.toISOString();
            }
          }
          parsedPaidAmt = parseNumericValue(paidAmountFromFile);
          if (isNaN(parsedPaidAmt) || parsedPaidAmt < 0) {
            rowErrors.push("Invalid or missing non-negative paidAmount");
            rowIsValid = false;
          }

          if (rowIsValid) {
            const firstPendingEmi = sortedCurrentEmiDetails.find(
              (emi) => emi.rePaymentStatus === "PENDING"
            );
            if (!firstPendingEmi) {
              rowErrors.push(
                "Cannot apply payment: No pending EMIs found for this offer."
              );
              rowIsValid = false;
            } else {
              targetEmiNumber = firstPendingEmi.emiNumber;
              if (targetEmiNumber !== parsedExcelEmiNum) {
                console.log(
                  `FIFO Applied: Row ${rowNum} (Excel EMI# ${parsedExcelEmiNum}) payment is being applied to first pending EMI #${targetEmiNumber}`
                );
              }
            }
          }

          if (rowIsValid && targetEmiNumber !== null) {
            payload.push({
              offerId: String(offerIdFromFile).trim(),
              emiNumber: targetEmiNumber,
              utr: String(utr).trim(),
              paymentTimestamp: parsedTimestamp,
              paidAmount: parsedPaidAmt,
            });
          } else {
            if (rowErrors.length > 0) {
              validationErrors.push({
                row: rowNum,
                errors: rowErrors.join("; "),
              });
            }
          }
        });

        if (payload.length === 0) {
          let errorMsg = `No valid data rows found for offer ${selectedOffer.offerInfo.offerId}.`;
          if (validationErrors.length > 0) {
            const clientErrors = validationErrors
              .map((err) => `(Row ${err.row}) ${err.errors}`)
              .slice(0, 5);
            errorMsg += ` Validation Errors Found: ${clientErrors.join("; ")}`;
            if (validationErrors.length > 5)
              errorMsg += "; ... (more errors exist)";
          }
          throw new Error(errorMsg);
        }

        const apiResponse = await fetch( // Changed variable name to avoid conflict with global `response`
          `${config.apiUrl}/ops/invoiceFinancing/offers/update-emi-repayments`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          }
        );
        const result = await apiResponse.json();

        if (!apiResponse.ok || !result.success) {
          const combinedErrors = [
            ...validationErrors,
            ...(result.errors || []).map((apiErr) => ({
              row: apiErr.recordIndex ?? "N/A",
              errors:
                apiErr.message ||
                apiErr.error ||
                "Unknown API error on this record",
            })),
          ];
          const errorMsg =
            result.message ||
            `Repayment Update API Error (Status: ${apiResponse.status})`;
          throw new Error(errorMsg, { cause: combinedErrors });
        }

        let successMsg =
          result.message ||
          `${result.updatedEmiCount || payload.length} EMIs processed successfully for offer ${selectedOffer.offerInfo.offerId}.`;
        if (validationErrors.length > 0)
          successMsg += ` (${validationErrors.length} row(s) in Excel had validation errors).`;

        setFeedbackMessage({
          type: "success",
          title: "Repayment File Submitted!",
          text: successMsg,
        });
        // To refresh data on this page after successful upload:
        // You would typically re-fetch the selectedOffer data from your API
        // and update the `selectedOffer` state.
        // For example:
        // const updatedOfferData = await fetchOfferByIdFromAPI(selectedOffer.offerInfo.offerId);
        // if (updatedOfferData) setSelectedOffer(updatedOfferData);
        // sessionStorage.setItem(`selectedOfferData_${selectedOffer.offerInfo.offerId}`, JSON.stringify(updatedOfferData)); // Update session too
      } catch (err) {
        console.error("[Upload Error Scope] handleRepaymentUpload:", err);
        let userMessage = "Repayment upload failed. ";
        try {
          let mainReason = err?.message || "An unknown error occurred.";
          let specificDetails = [];

          if (err?.cause && Array.isArray(err.cause)) {
            mainReason = err.message || "API or Validation errors found.";
            specificDetails = err.cause
              .map(
                (e) =>
                  `(Row ${e?.row ?? "N/A"}) ${e?.errors || e?.message || e?.error || "Details unavailable"}`
              )
              .slice(0, 5);
            if (err.cause.length > 5)
              specificDetails.push("... (more in console)");
          } else if (
            Array.isArray(validationErrors) &&
            validationErrors.length > 0
          ) {
            mainReason = mainReason.includes("Validation Errors")
              ? mainReason
              : "File validation failed.";
            specificDetails = validationErrors
              .map(
                (e) =>
                  `(Row ${e?.row ?? "N/A"}) ${e?.errors || "Details unavailable"}`
              )
              .slice(0, 5);
            if (validationErrors.length > 5)
              specificDetails.push("... (more errors in file)");
          }
          userMessage = `Upload Failed: ${mainReason}`;
          if (specificDetails.length > 0) {
            userMessage += ` Details: ${specificDetails.join("; ")}`;
          }
          if (mainReason.toLowerCase().includes("invalid header")) {
            userMessage += " Check template column names.";
          } else if (mainReason.includes("No valid data rows")) {
            userMessage += " Check file data format/Offer ID match.";
          } else if (typeof navigator !== 'undefined' && !navigator.onLine) { // Check for navigator
            userMessage += " Check internet connection.";
          }
        } catch (formattingError) {
          console.error("[Upload Error] Formatting Error:", formattingError);
          userMessage = `Upload failed: ${err?.message || "An critical unknown error occurred."}`;
        }
        setFeedbackMessage({
          type: "error",
          title: "Repayment Upload Failed",
          text: userMessage,
        });
      } finally {
        setIsUploading(false);
        setShowStatusModal(true);
        try {
          if (repaymentFileRef?.current) repaymentFileRef.current.value = "";
        } catch (resetError) {
          console.error("Error resetting repayment file input:", resetError);
        }
      }
    };

    reader.onerror = (err) => {
      console.error("[Upload Error] File Reading Error:", err);
      setFeedbackMessage({
        type: "error",
        title: "File Reading Error",
        text: `Error reading file: ${err?.message || "Could not read the selected file."}`,
      });
      setIsUploading(false);
      setShowStatusModal(true);
      try {
        if (repaymentFileRef?.current) repaymentFileRef.current.value = "";
      } catch (e) {
        console.error("Error resetting repayment file input:", e);
      }
    };
    reader.readAsArrayBuffer(file);
  };


  if (isLoadingOffer) {
    return (
      <div className="p-4 md:p-6 bg-gray-50 min-h-screen w-full flex flex-col">
        <div
          className="flex items-center text-sm text-gray-700 hover:text-gray-900 cursor-pointer mb-6"
          onClick={handleGoBack}
        >
          <ArrowLeftIcon className="w-5 h-5 mr-2" />
          Back to Payments
        </div>
        <div className="flex-grow flex items-center justify-center">
          <LoadingModal />
        </div>
      </div>
    );
  }

  if (!selectedOffer) {
    return (
      <div className="p-4 md:p-6 bg-gray-50 min-h-screen w-full">
        <div
          className="flex items-center text-sm text-gray-700 hover:text-gray-900 cursor-pointer mb-6"
          onClick={handleGoBack}
        >
          <ArrowLeftIcon className="w-5 h-5 mr-2" />
          Back to Payments
        </div>
        <h1 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4">
          Update Repayment Status
        </h1>
        {/* Display feedback message if offer couldn't be loaded */}
        {feedbackMessage.text && (
          <div
            className={`p-4 mb-4 mt-4 rounded-md text-sm shadow ${feedbackMessage.type === "error"
                ? "bg-red-100 text-red-700"
                : feedbackMessage.type === "warning"
                  ? "bg-yellow-100 text-yellow-700"
                  : "bg-blue-100 text-blue-700" // Default or info
              }`}
          >
            {feedbackMessage.title && <p className="font-semibold">{feedbackMessage.title}</p>}
            {feedbackMessage.text}
          </div>
        )}
        {!feedbackMessage.text && ( // Generic message if no specific feedback yet
          <div className="p-4 mb-4 mt-4 rounded-md text-sm shadow bg-yellow-100 text-yellow-700">
            Offer details could not be loaded. Please ensure you navigated correctly or try again.
          </div>
        )}
      </div>
    );
  }

  // Main component render when selectedOffer is available
  return (
    <div className="p-4 md:p-6 bg-gray-50 min-h-screen w-full">
      {isUploading && <LoadingModal />}

      {/* Back to Payments Link */}
      <div
        className="flex items-center text-sm text-gray-700 hover:text-gray-900 cursor-pointer mb-6"
        onClick={handleGoBack} // << CHANGED to use router.push via handleGoBack
      >
        <ArrowLeftIcon className="w-5 h-5 mr-2" />
        Back to Payments
      </div>

      {/* Header */}
      <div className="mb-6">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
          Update Repayment Status
        </h1>
        {/* Conditional rendering for selectedOffer properties */}
        {selectedOffer && selectedOffer.offerInfo && selectedOffer.merchantInfo && selectedOffer.invoiceInfo && (
          <p className="text-sm text-gray-600">
            Offer ID:{" "}
            <span className="font-mono">
              {selectedOffer.offerInfo.offerId}
            </span>
            <br />
            Merchant: {selectedOffer.merchantInfo.businessName ||
              `${selectedOffer.merchantInfo.firstName || ''} ${selectedOffer.merchantInfo.lastName || ''}`}{" "}
            | Invoice: {selectedOffer.invoiceInfo.invoiceNumber}
          </p>
        )}
      </div>

      {/* Feedback Area: Show general feedback if not related to a modal action */}
      {feedbackMessage.text && !showStatusModal && (
        <div
          className={`p-4 mb-4 rounded-md text-sm shadow ${feedbackMessage.type === "error"
            ? "bg-red-100 text-red-700"
            : feedbackMessage.type === "success"
              ? "bg-green-100 text-green-700"
              : "bg-blue-100 text-blue-700" // Default for info
            }`}
        >
          {feedbackMessage.title && <p className="font-semibold">{feedbackMessage.title}</p>}
          {feedbackMessage.text}
        </div>
      )}

      {/* Action Buttons */}
      <div className="bg-white p-4 rounded-lg shadow border border-gray-300 mb-6">
        <div className="flex flex-wrap lg:flex-nowrap items-center gap-3 justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-700">EMI Details</h2>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleDownloadRepaymentTemplate}
              className="px-4 py-2 bg-white text-gray-800 border border-gray-300 rounded shadow hover:bg-gray-100 transition duration-150 text-sm font-medium flex items-center"
            >
              <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
              Download Template
            </button>
            <button
              onClick={() => {
                // setCurrentUploadType('repayment'); // Already set
                setShowUploadPromptModal(true);
              }}
              className="px-4 py-2 bg-[#1a9c54] text-white rounded shadow hover:bg-[#157a44] transition duration-150 text-sm font-medium flex items-center"
            >
              <ArrowUpTrayIcon className="w-4 h-4 mr-2" />
              Upload Repayments
            </button>
            <input
              id="repayment-upload-page"
              ref={repaymentFileRef}
              type="file"
              accept=".xlsx, .xls, .csv"
              className="hidden"
              onChange={handleRepaymentUpload}
            />
          </div>
        </div>
      </div>


      {/* EMI Table - Reusing styling from original page's modal */}
      <div className="p-3 bg-gray-200 rounded-lg shadow">
        <div className="overflow-x-auto w-full custom-h-scrollbar border border-gray-300 rounded-md">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-white">
              {/* Exact same table heading as in the original modal */}
              <tr>
                <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  EMI #
                </th>
                <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Due Date
                </th>
                <th className="px-2 md:px-4 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Amount Due
                </th>
                <th className="px-2 md:px-4 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Principal
                </th>
                <th className="px-2 md:px-4 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Service Fee
                </th>
                <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Paid Date
                </th>
                <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Details (UTR)
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {selectedOffer && selectedOffer.offerInfo && Array.isArray(selectedOffer.offerInfo.emiDetails) &&
                selectedOffer.offerInfo.emiDetails.length > 0 ? (
                [...selectedOffer.offerInfo.emiDetails]
                  .sort((a, b) => (a.emiNumber || 0) - (b.emiNumber || 0)) // Added null check for emiNumber
                  .map((emi, index) => ( // Added index for key fallback
                    <tr
                      key={emi._id || `emi-${emi.emiNumber || index}`} // Improved key
                      className="hover:bg-blue-50 text-xs"
                    >
                      <td className="px-2 md:px-3 py-2 whitespace-nowrap font-medium text-gray-700">
                        {emi.emiNumber}
                      </td>
                      <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600">
                        {formatDate(emi.rePaymentDate)}
                      </td>
                      <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-800 font-medium text-right">
                        {formatCurrency(emi.rePaymentAmount)}
                      </td>
                      <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600 text-right">
                        {formatCurrency(emi.principalRecovered, "")}
                      </td>
                      <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600 text-right">
                        {formatCurrency(emi.interestAmount, "")}
                      </td>
                      <td className="px-2 md:px-3 py-2 whitespace-nowrap text-center">
                        <StatusBadge
                          status={emi.rePaymentStatus || "N/A"}
                        />
                      </td>
                      <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600">
                        {formatDate(emi.rePaymentActualDate)}
                      </td>
                      <td
                        className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-500 text-ellipsis overflow-hidden max-w-xs" // Consider max-w-xs or similar for very long UTRs
                        title={emi.rePaymentReceivedSource || ""}
                      >
                        {emi.rePaymentReceivedSource || "N/A"}
                      </td>
                    </tr>
                  ))
              ) : (
                <tr>
                  <td
                    colSpan="8" // Corrected colspan
                    className="text-center py-4 text-gray-500 text-sm"
                  >
                    No EMI details available for this offer.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>


      {/* Upload Prompt Modal (Your existing modal code) */}
      {showUploadPromptModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex justify-center items-center z-50 p-4 transition-opacity duration-300 ease-out" style={{ opacity: 1 }}>
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md transform transition-all duration-300 ease-out scale-100">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">
                Upload Repayment File
              </h2>
              <button
                onClick={() => setShowUploadPromptModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="p-6 text-center">
              <p className="text-sm text-gray-500 mb-6">
                Please upload the updated template.
              </p>
              <div
                className="mb-6 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center gap-4 p-6 hover:border-gray-400 cursor-pointer transition-colors"
                style={{ backgroundColor: '#eef6f8' }}
                onClick={() => {
                  if (repaymentFileRef.current) {
                    repaymentFileRef.current.click();
                  }
                }}
              >
                <ArrowUpTrayIcon className="w-6 h-6 text-gray-500" />
                <div className="text-left">
                  <p className="text-base font-semibold text-gray-700">Upload</p>
                  <p className="text-xs text-gray-500">Max filesize 10MB</p>
                </div>
              </div>
              <p className="text-sm text-gray-500 mb-4">
                Please use below template file to update the repayments.
              </p>
              <button
                onClick={handleDownloadRepaymentTemplate}
                className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Repayment File Template (.xlsx)
                <ArrowDownTrayIcon className="ml-2 h-5 w-5 text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Status Modal (Your existing modal code) */}
      {showStatusModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex justify-center items-center z-[100] p-4 transition-opacity duration-300 ease-out" style={{ opacity: 1 }}> {/* Ensure z-index is high enough */}
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md text-center transform transition-all duration-300 ease-out scale-100">
            <div className="pt-5 pr-5 flex justify-end items-center">
              <button
                onClick={() => {
                  setShowStatusModal(false);
                  setFeedbackMessage({ type: "", text: "", title: "" }); // Reset feedback on close
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="p-6 pt-0">
              {feedbackMessage.type === 'success' && (
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                  <svg className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              )}
              {feedbackMessage.type === 'error' && (
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                  <svg className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                  </svg>
                </div>
              )}
              {feedbackMessage.type === 'warning' && (
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
                  <svg className="h-10 w-10 text-yellow-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                  </svg>
                </div>
              )}
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {feedbackMessage.title || (feedbackMessage.type === 'success' ? "Success!" : feedbackMessage.type === 'warning' ? "Warning" : "Error!")}
              </h3>
              <p className="text-sm text-gray-600 mb-6 whitespace-pre-wrap">
                {feedbackMessage.text}
              </p>
              <button
                onClick={() => {
                  setShowStatusModal(false);
                  setFeedbackMessage({ type: "", text: "", title: "" }); // Reset feedback on close
                }}
                className="w-full sm:w-auto px-6 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-800 transition duration-150 font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
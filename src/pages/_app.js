import '@/styles/globals.css';
import Sidebar from '@/components/Sidebar';
import { useRouter } from 'next/router';
import Header from '../components/Header';
import { useEffect, useState } from 'react';
import Cookies from 'js-cookie';

export default function MyApp({ Component, pageProps }) {
    const router = useRouter();
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false); // State for mobile sidebar

    useEffect(() => {
        const token = Cookies.get('token');
        const isLoginPage = router.pathname === '/';

        if (!token && !isLoginPage) {
            router.push('/');
        } else if (token && isLoginPage) {
            router.push('/dashboard');
        } else {
            setIsAuthenticated(!!token);
        }
        setIsLoading(false);
    }, [router, router.pathname]);

    if (isLoading) {
        return null; // or a loading spinner
    }

    if (router.pathname === '/') {
        return <Component {...pageProps} />;
    }

    if (!isAuthenticated) {
        return null; // or a loading spinner
    }

    return (
        <div className="flex min-h-screen"> {/* Main flex container for the whole screen */}
            {/* Sidebar - Hidden on mobile by default, shown via mobile menu button */}
            {/* On medium screens and up (md:), it becomes a fixed block */}
            <Sidebar
                activePage={router.pathname}
                isMobileMenuOpen={isMobileMenuOpen}
                setIsMobileMenuOpen={setIsMobileMenuOpen}
            />

            {/* Right section: Header + Main Content */}
            {/* This column takes remaining width and ensures content wraps correctly */}
            <div className="flex-1 flex flex-col overflow-hidden"> {/* Added overflow-hidden to prevent horizontal scroll issues */}
                {/* Header for the right section */}
                {/* Pass setIsMobileMenuOpen to Header so it can trigger the sidebar toggle on mobile */}
                <Header setIsMobileMenuOpen={setIsMobileMenuOpen} />

                {/* Main content area */}
                {/* Adjusted padding for mobile screens (p-4) and added overflow for scrolling */}
                <main className="flex-1 p-4 md:p-6 lg:p-8 overflow-x-hidden overflow-y-auto bg-gray-50">
                    <Component {...pageProps} />
                </main>
            </div>
        </div>
    );
}
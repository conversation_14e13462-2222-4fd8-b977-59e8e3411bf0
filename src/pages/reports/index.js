import { useState } from 'react';
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  ResponsiveContainer 
} from 'recharts';

export default function Reports() {
  const [reportType, setReportType] = useState('portfolio');
  const [timeframe, setTimeframe] = useState('monthly');

  const mockData = {
    portfolio: {
      collections: [
        { month: 'Jan', amount: 1000000, count: 45 },
        { month: 'Feb', amount: 1200000, count: 50 },
        { month: 'Mar', amount: 1400000, count: 55 },
      ],
      volume: [
        { month: 'Jan', invoices: 85000000, loans: 65000000 },
        { month: 'Feb', invoices: 95000000, loans: 75000000 },
        { month: 'Mar', invoices: 105000000, loans: 85000000 },
      ],
      npa: [
        { month: 'Jan', percentage: 1.2 },
        { month: 'Feb', percentage: 1.1 },
        { month: 'Mar', percentage: 0.9 },
      ]
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Reports</h1>
        <div className="flex space-x-4">
          <select 
            className="border rounded-md p-2"
            value={reportType}
            onChange={(e) => setReportType(e.target.value)}
          >
            <option value="portfolio">Portfolio Overview</option>
            <option value="regulatory">Regulatory Reports</option>
            <option value="risk">Risk Analysis</option>
          </select>
          <select 
            className="border rounded-md p-2"
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
          </select>
        </div>
      </div>

      {/* Portfolio Metrics */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Total Portfolio Value</h3>
          <p className="text-2xl font-bold">QAR 10,00,000</p>
          <p className="text-green-600 text-sm">↑ 15% vs last month</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Active Loans</h3>
          <p className="text-2xl font-bold">156</p>
          <p className="text-green-600 text-sm">↑ 8% vs last month</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">NPA Percentage</h3>
          <p className="text-2xl font-bold">0.9%</p>
          <p className="text-green-600 text-sm">↓ 0.2% vs last month</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-md">
          <h3 className="text-gray-600 text-sm">Return Rate</h3>
          <p className="text-2xl font-bold">12.5%</p>
          <p className="text-gray-600 text-sm">Annual Percentage Rate (APR)</p>
        </div>
      </div>

      {/* Collections Trend */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Collections Trend</h2>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={mockData.portfolio.collections}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="amount" stroke="#2563eb" name="Collection Amount" />
            <Line type="monotone" dataKey="count" stroke="#16a34a" name="Number of Collections" />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Volume Analysis */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Volume Analysis</h2>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={mockData.portfolio.volume}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="invoices" fill="#2563eb" name="Invoice Volume" />
            <Bar dataKey="loans" fill="#16a34a" name="Loan Volume" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* NPA Trend */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">NPA Trend</h2>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={mockData.portfolio.npa}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="percentage" stroke="#dc2626" name="NPA %" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
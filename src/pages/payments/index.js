import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from "react";
import { useRouter } from "next/router";
import * as XLSX from "xlsx"; // SheetJS library for Excel
import { saveAs } from "file-saver"; // For triggering file download
import { Disclosure } from "@headlessui/react";
import {
  FunnelIcon,
  ChevronUpIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
} from "@heroicons/react/24/outline";
import LoadingModal from "../../components/Loading";
import axios from "axios";
import StatusBadge from "../../components/StatusBadge";
import config from "../../../config.json";

const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid Date";
    return date.toLocaleDateString("en-CA"); // Format YYYY-MM-DD
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return "Invalid Date";
  }
};

// --- DisbursalRepaymentFilterSection Component ---
const DisbursalRepaymentFilterSection = ({
  filters,
  setFilters,
  resetFilters,
  uniqueOfferStatuses = [],
  uniqueInvoiceStatuses = [],
}) => {
  // Input styling
  const inputBaseClass =
    "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  const numberInputClass = `${inputBaseClass} px-2 py-1`;
  const dateInputClass = `${inputBaseClass} px-2 py-1`;
  const textInputClass = `${inputBaseClass} px-3 py-1.5`;
  const selectInputClass = `${inputBaseClass} px-3 py-2`;

  const handleFilterChangeInternal = (event) => {
    const { name, value } = event.target;
    setFilters((prevFilters) => ({ ...prevFilters, [name]: value }));
  };

  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(
      (v) => v !== "" && v !== null && v !== undefined
    ).length;
  }, [filters]);

  const handleResetInternal = (event) => {
    event.stopPropagation();
    resetFilters();
  };

  return (
    <div className="mt-6">
      <Disclosure
        as="div"
        className="border border-gray-200 rounded-lg shadow-sm bg-white"
      >
        {({ open }) => (
          <>
            {/* Header Bar */}
            <div className="flow-root">
              <Disclosure.Button className="flex w-full items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none">
                <span className="flex items-center">
                  <FunnelIcon
                    className="mr-2 h-5 w-5 text-gray-400"
                    aria-hidden="true"
                  />
                  Filters
                  {activeFilterCount > 0 && (
                    <span className="ml-2 rounded-full bg-gray-200 px-2 py-0.5 text-xs font-medium text-gray-800">
                      {activeFilterCount}
                    </span>
                  )}
                </span>
                <span className="ml-6 flex items-center">
                  <ChevronUpIcon
                    className={`${open ? "rotate-180" : ""
                      } h-5 w-5 text-gray-500 transition-transform`}
                  />
                </span>
              </Disclosure.Button>
            </div>

            {/* Separator */}
            {open && <div className="border-t border-gray-200"></div>}

            {/* Panel */}
            <Disclosure.Panel className="px-4 py-5 sm:px-6 lg:px-8">
              {/* Top Row: Search & Clear */}
              <div className="mb-6 flex items-start justify-between gap-4">
                <div className="flex-1">
                  <label htmlFor="mainSearchText" className="sr-only">
                    Search
                  </label>
                  <input
                    type="text"
                    name="searchText"
                    id="mainSearchText"
                    value={filters.searchText}
                    onChange={handleFilterChangeInternal}
                    className={textInputClass}
                    placeholder="Search Offer ID, Merchant, Invoice..."
                  />
                </div>
                <button
                  type="button"
                  onClick={handleResetInternal}
                  className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                >
                  <XMarkIcon className="-ml-1 mr-1.5 h-4 w-4 text-gray-400" />{" "}
                  Clear all
                </button>
              </div>

              {/* Filter Grid - Adjust columns as needed */}
              <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
                {/* Offer Amount Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Offer Amount (QAR)
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      name="minAmount"
                      value={filters.minAmount}
                      onChange={handleFilterChangeInternal}
                      placeholder="Min"
                      className={numberInputClass}
                    />
                    <input
                      type="number"
                      name="maxAmount"
                      value={filters.maxAmount}
                      onChange={handleFilterChangeInternal}
                      placeholder="Max"
                      className={numberInputClass}
                    />
                  </div>
                </div>

                {/* Offer Status Dropdown */}
                <div>
                  <label
                    htmlFor="offerStatusFilter"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Offer Status
                  </label>
                  <select
                    id="offerStatusFilter"
                    name="offerStatus"
                    value={filters.offerStatus}
                    onChange={handleFilterChangeInternal}
                    className={selectInputClass}
                  >
                    <option value="">All Offers</option>
                    {uniqueOfferStatuses.map((status) => (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Invoice Status Dropdown */}
                <div>
                  <label
                    htmlFor="invoiceStatusFilter"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Invoice Status
                  </label>
                  <select
                    id="invoiceStatusFilter"
                    name="invoiceStatus"
                    value={filters.invoiceStatus}
                    onChange={handleFilterChangeInternal}
                    className={selectInputClass}
                  >
                    <option value="">All Invoices</option>
                    {uniqueInvoiceStatuses.map((status) => (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Disbursal Date Range */}
                <div className="sm:col-span-2 md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Disbursal Date
                  </label>
                  <div className="flex flex-col space-y-2">
                    <input
                      type="date"
                      name="disbursalDateFrom"
                      value={filters.disbursalDateFrom}
                      onChange={handleFilterChangeInternal}
                      className={dateInputClass}
                      aria-label="Disbursal Start Date"
                    />
                    <input
                      type="date"
                      name="disbursalDateTo"
                      value={filters.disbursalDateTo}
                      onChange={handleFilterChangeInternal}
                      className={dateInputClass}
                      aria-label="Disbursal End Date"
                    />
                  </div>
                </div>

                {/* Next Due Date Range */}
                <div className="sm:col-span-2 md:col-span-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Next Due Date
                  </label>
                  <div className="flex flex-col space-y-2">
                    <input
                      type="date"
                      name="nextDueDateFrom"
                      value={filters.nextDueDateFrom}
                      onChange={handleFilterChangeInternal}
                      className={dateInputClass}
                      aria-label="Next Due Start Date"
                    />
                    <input
                      type="date"
                      name="nextDueDateTo"
                      value={filters.nextDueDateTo}
                      onChange={handleFilterChangeInternal}
                      className={dateInputClass}
                      aria-label="Next Due End Date"
                    />
                  </div>
                </div>
              </div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>
    </div>
  );
};

const formatCurrency = (value, currency = "QAR") => {
  const numValue =
    typeof value === "string" ? parseFloat(value.replace(/,/g, "")) : value;
  if (numValue === null || numValue === undefined || isNaN(numValue))
    return "N/A";
  return `${currency} ${Number(numValue).toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

// Using the shared StatusBadge component from src/components/StatusBadge.js

// --- Info Icon Component ---
const InfoIcon = ({ tooltipText }) => {
  const [showTooltip, setShowTooltip] = useState(false);
  return (
    <div className="relative inline-flex items-center ml-2">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 text-gray-400 hover:text-blue-500 cursor-pointer"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        strokeWidth={2}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      {showTooltip && (
        <div
          className="absolute z-20 bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs font-medium text-white bg-gray-900 rounded-md shadow-lg w-72 whitespace-normal"
          style={{ pointerEvents: "none" }}
        >
          {tooltipText}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-x-4 border-x-transparent border-t-4 border-t-gray-900"></div>{" "}
          {/* Arrow */}
        </div>
      )}
    </div>
  );
};

// --- Main Component ---
export default function DisbursalRepaymentPage() {
  const [offersData, setOffersData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeSection, setActiveSection] = useState("disbursal");
  const [showEmiModal, setShowEmiModal] = useState(false);
  const [selectedOfferForModal, setSelectedOfferForModal] = useState(null);
  const [feedbackMessage, setFeedbackMessage] = useState({
    type: "",
    text: "",
    title: "",
  });
  const router = useRouter();
  const [showUploadPromptModal, setShowUploadPromptModal] = useState(false);
  const [currentUploadType, setCurrentUploadType] = useState(''); // 'disbursal' or 'repayment'
  const [showStatusModal, setShowStatusModal] = useState(false); // For success/error confirmation modal

  const [lenderId, setLenderId] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const disbursalFileRef = useRef(null);
  const repaymentFileRef = useRef(null);

  const navigateToUpdateEmiPage = (offer) => {
    if (!offer || !offer.offerInfo || !offer.offerInfo.offerId) {
      console.error("Cannot navigate: Offer or Offer ID is missing", offer);
      // setFeedbackMessage for UI update if this state is managed here
      alert("Cannot open EMI details: Offer information is incomplete.");
      return;
    }

    const offerId = offer.offerInfo.offerId;

    try {
      sessionStorage.setItem(`selectedOfferData_${offerId}`, JSON.stringify(offer));
      console.log(`selectedOfferData_${offerId}`, sessionStorage.getItem(`selectedOfferData_${offerId}`));
      router.push({
        pathname: '/updateEmiStatus', // Ensure this path matches your file structure/routing
        query: { offerId: offerId },
      });

    } catch (error) { console.error("Error saving offer data to sessionStorage:", error); };
    return; // Or handle error more gracefully
  };

  // --- Commercial State for Filters ---
  const [filters, setFilters] = useState({
    // Common filters
    searchText: "", // For Offer ID, Merchant Name/ID, Invoice #
    offerStatus: "", // Single select
    minAmount: "", // Filter by Offer Amount (e.g., creditLimit)
    maxAmount: "", // Filter by Offer Amount

    // Disbursal specific (but keep in main state for simplicity)
    invoiceStatus: "", // Single select
    disbursalDateFrom: "",
    disbursalDateTo: "",

    // Repayment specific (but keep in main state)
    nextDueDateFrom: "",
    nextDueDateTo: "",
  });

  // Combined Filter Change Handler
  // const handleFilterChange = (event) => {
  //   const { name, value } = event.target;
  //   setFilters(prevFilters => ({
  //     ...prevFilters,
  //     [name]: value
  //   }));
  // };

  // Reset Function for all filters
  const resetFilters = useCallback(() => {
    if (isUploading) return; // Prevent reset while uploading
    setFilters({
      searchText: "",
      offerStatus: "",
      minAmount: "",
      maxAmount: "",
      invoiceStatus: "",
      disbursalDateFrom: "",
      disbursalDateTo: "",
      nextDueDateFrom: "",
      nextDueDateTo: "",
    });
  }, [isUploading]); // No dependencies needed

  const uniqueOfferStatuses = useMemo(() => {
    const statuses = new Set(
      offersData.map((item) => item.offerInfo?.status).filter(Boolean)
    );
    return [...statuses].sort();
  }, [offersData]);

  const uniqueInvoiceStatuses = useMemo(() => {
    // Filter out null/undefined/N/A statuses for invoices
    const statuses = new Set(
      offersData
        .map((item) => item.invoiceInfo?.invoiceStatus)
        .filter((status) => status && status !== "N/A")
    );
    return [...statuses].sort();
  }, [offersData]);

  // Effect to load lenderId from localStorage
  useEffect(() => {
    const storedLenderId = localStorage.getItem("userId");
    if (storedLenderId) {
      setLenderId(storedLenderId);
    } else {
      console.error("Lender ID not found in localStorage. Cannot fetch data.");
      setError("Lender ID not found. Please ensure you are logged in.");
      setLoading(false);
    }
  }, []);

  // Fetch Data Function
  const fetchData = useCallback(async () => {
    if (isUploading) return; // Prevent fetch while uploading
    try {
      setLoading(true);
      const lenderId = localStorage.getItem("userId");
      if (!lenderId) {
        throw new Error("Lender ID not found");
      }
      setLenderId(lenderId);

      const response = await axios.get(
        `${config.apiUrl}/ops/invoiceFinancing/lender/${lenderId}/repayment-disbursal-data`
      );
      if (!response) {
        throw new Error("Failed to fetch offers");
      }
      console.log(response, response.data, response.data.data);
      const data = await response.data.data;
      setOffersData(data);
    } catch (error) {
      console.error("Error fetching offers:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  }, [isUploading]);

  const isValidObjectId = (id) => /^[0-9a-fA-F]{24}$/.test(id);

  // --- Complete Disbursal Upload Handler ---
  const handleDisbursalUpload = async (event) => {
    const file = event.target.files[0];
    setShowUploadPromptModal(false); // Close prompt once file dialog interaction is done

    if (!file) {
      if (disbursalFileRef.current) disbursalFileRef.current.value = ""; // Reset if user cancelled
      return;
    }

    setIsUploading(true);
    // Initial info message for loading phase (optional if LoadingModal is just a spinner and feedback is shown elsewhere)
    // setFeedbackMessage({ type: "info", title: "Processing...", text: "Reading and validating disbursal file..." });

    const reader = new FileReader();
    reader.onload = async (e) => {
      const validationErrors = []; // Ensure this is scoped correctly or passed if needed by deeper functions
      let payload = [];           // Ensure this is scoped correctly

      try {
        // Start: File Reading and Basic Checks
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array", cellDates: true });
        const sheetName = workbook.SheetNames[0];
        if (!sheetName) throw new Error("Cannot find sheet in Excel file.");
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: null,
          raw: false,
        });

        if (!jsonData || jsonData.length < 2)
          throw new Error("Excel file appears empty or lacks a header row.");

        const header = jsonData[0].map((h) => String(h || "").trim());
        const expectedHeader = [
          "UTR",
          "invoiceOfferId",
          "disbursementTimestamp",
          "disbursalAmount",
        ];
        if (JSON.stringify(header) !== JSON.stringify(expectedHeader)) {
          throw new Error(
            `Invalid header row. Expected columns: ${expectedHeader.join(", ")}`
          );
        }
        // End: File Reading

        // Start: Detailed Row Validation
        const dataRows = jsonData.slice(1);
        dataRows.forEach((row, index) => {
          const rowNum = index + 2;
          const utr = row?.[0];
          const invoiceOfferId = row?.[1];
          let timestampInput = row?.[2];
          const disbursalAmount = row?.[3];

          let rowIsValid = true;
          let rowErrors = [];
          let parsedTimestamp = null;
          let parsedAmount = NaN;

          if (
            !invoiceOfferId ||
            String(invoiceOfferId).trim() === "" ||
            !isValidObjectId(String(invoiceOfferId).trim())
          ) {
            rowErrors.push(
              "Invalid/Missing invoiceOfferId (must be a valid 24-char ID)"
            );
            rowIsValid = false;
          }
          if (!utr || String(utr).trim() === "") {
            rowErrors.push("Missing UTR");
            rowIsValid = false;
          }
          if (
            timestampInput === null ||
            timestampInput === undefined ||
            String(timestampInput).trim() === ""
          ) {
            rowErrors.push("Missing disbursementTimestamp");
            rowIsValid = false;
          } else {
            let date = new Date(timestampInput);
            if (
              isNaN(date.getTime()) &&
              typeof timestampInput === "number" &&
              timestampInput > 25569
            ) {
              const excelEpoch = new Date(1899, 11, 30);
              const jsTimestamp =
                excelEpoch.getTime() + timestampInput * 24 * 60 * 60 * 1000;
              date = new Date(jsTimestamp);
            }
            if (isNaN(date.getTime())) {
              rowErrors.push(
                "Invalid disbursementTimestamp format (use YYYY-MM-DD or MM/DD/YYYY)"
              );
              rowIsValid = false;
            } else {
              parsedTimestamp = date.toISOString();
            }
          }
          parsedAmount = parseFloat(disbursalAmount);
          if (isNaN(parsedAmount) || parsedAmount <= 0) {
            rowErrors.push("Invalid/Missing/Non-positive disbursalAmount");
            rowIsValid = false;
          }

          if (rowIsValid) {
            payload.push({
              invoiceOfferId: String(invoiceOfferId).trim(),
              utr: String(utr).trim(),
              disbursementTimestamp: parsedTimestamp,
              disbursalAmount: parsedAmount,
            });
          } else {
            validationErrors.push({
              row: rowNum,
              errors: rowErrors.join("; "),
            });
          }
        });
        // End: Detailed Row Validation

        if (payload.length === 0) {
          let errorMsg = `No valid data rows found to process.`;
          if (validationErrors.length > 0) {
            const clientErrors = validationErrors
              .map((err) => `(Row ${err.row}) ${err.errors}`)
              .slice(0, 5);
            errorMsg += ` Validation Errors Found: ${clientErrors.join("; ")}`;
            if (validationErrors.length > 5)
              errorMsg += "; ... (more errors exist)";
          }
          throw new Error(errorMsg);
        }

        // setFeedbackMessage({ // This is an interim message, can be removed if loading modal is sufficient
        //   type: "info",
        //   title: "Processing",
        //   text: `Client validation complete. Sending ${payload.length} valid record(s)...`,
        // });

        // Start: API Calls and Subsequent Processing
        const offerUpdateResponse = await fetch(
          `${config.apiUrl}/ops/invoiceFinancing/offers/update-disbursal-status`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          }
        );
        const offerUpdateResult = await offerUpdateResponse.json();

        if (!offerUpdateResponse.ok || !offerUpdateResult.success) {
          const combinedErrors = [
            ...validationErrors,
            ...(offerUpdateResult.errors || []).map((apiErr) => ({
              row: apiErr.recordIndex ?? "N/A",
              errors:
                apiErr.message ||
                apiErr.error ||
                "Unknown API error on this record",
            })),
          ];
          const errorMsg =
            offerUpdateResult.message ||
            `Offer Update API Error (Status: ${offerUpdateResponse.status})`;
          throw new Error(errorMsg, { cause: combinedErrors });
        }

        let initialSuccessMsg =
          offerUpdateResult.message ||
          `${offerUpdateResult.updatedCount || payload.length} offer(s) processed.`;
        if (validationErrors.length > 0) // validationErrors from the initial file read
          initialSuccessMsg += ` (${validationErrors.length} initial row validation errors detected).`;

        // setFeedbackMessage({ // Interim message
        //   type: "info",
        //   title: "Processing",
        //   text: `${initialSuccessMsg} Updating related invoice statuses...`,
        // });

        let invoiceUpdateSuccessCount = 0;
        const invoiceUpdateErrors = [];
        const successfullyUpdatedOfferIds =
          offerUpdateResult.successDetails?.map(
            (detail) => detail.invoiceOfferId
          ) ?? payload.map((p) => p.invoiceOfferId);

        await Promise.all(
          payload
            .filter((p) =>
              successfullyUpdatedOfferIds.includes(p.invoiceOfferId)
            )
            .map(async (processedOffer) => {
              const offerId = processedOffer.invoiceOfferId;
              const originalOfferData = offersData.find(
                (item) => item.offerInfo.offerId === offerId
              );

              if (
                !originalOfferData ||
                !originalOfferData.invoiceInfo?.invoiceId
              ) {
                console.warn(
                  `Could not find invoiceId for processed offerId: ${offerId}. Skipping invoice update.`
                );
                invoiceUpdateErrors.push({
                  offerId: offerId,
                  message: "Invoice ID not found.",
                });
                return;
              }
              const invoiceIdToUpdate = originalOfferData.invoiceInfo.invoiceId;
              try {
                const invoiceUpdateResponse = await fetch(
                  `${config.apiUrl}/ops/invoiceFinancing/updateInvoice/${invoiceIdToUpdate}`,
                  {
                    method: "PUT",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ status: "DISBURSED" }),
                  }
                );
                if (!invoiceUpdateResponse.ok) {
                  const errorData = await invoiceUpdateResponse
                    .json()
                    .catch(() => ({}));
                  throw new Error(
                    errorData.message || `HTTP ${invoiceUpdateResponse.status}`
                  );
                }
                invoiceUpdateSuccessCount++;
              } catch (invoiceErr) {
                console.error(
                  `Error updating invoice status for invoice ${invoiceIdToUpdate} (Offer ${offerId}):`,
                  invoiceErr
                );
                invoiceUpdateErrors.push({
                  offerId: offerId,
                  invoiceId: invoiceIdToUpdate,
                  message: invoiceErr.message,
                });
              }
            })
        );
        // End: API Calls

        // Final Feedback Message Construction
        let finalMsg = initialSuccessMsg;
        let finalType = "success";

        if (successfullyUpdatedOfferIds.length > 0) {
          if (invoiceUpdateErrors.length === 0) {
            finalMsg += ` All ${invoiceUpdateSuccessCount} corresponding invoice statuses updated.`;
          } else {
            finalMsg += ` Updated ${invoiceUpdateSuccessCount} invoice statuses.`;
            let errorSummary = ` Failed to update ${invoiceUpdateErrors.length}.`;
            let errorDetails = invoiceUpdateErrors
              .map(
                (e) =>
                  `(Offer ${e.offerId}/Inv ${e.invoiceId || "N/A"}) ${e.message}`
              )
              .slice(0, 3)
              .join("; ");
            if (invoiceUpdateErrors.length > 3)
              errorDetails += "; ... (see console)";
            finalMsg += `${errorSummary} Details: ${errorDetails}`;
            finalType = "warning"; // Or 'success' if some part succeeded, 'warning' if mixed results
          }
        } else {
          finalType = validationErrors.length > 0 ? "warning" : "success";
        }

        setFeedbackMessage({
          type: finalType, // 'success' or 'warning'
          title: "Disbursal File Processed", // Or "Disbursal File Submitted!" for pure success
          text: finalMsg,
        });
        fetchData();
      } catch (err) {
        console.error("[Upload Error Scope] handleDisbursalUpload:", err);
        let userMessage = "Disbursal upload failed. ";
        try {
          let mainReason = err?.message || "An unknown error occurred.";
          let specificDetails = [];

          if (err?.cause && Array.isArray(err.cause)) {
            mainReason = err.message || "API or Validation errors found.";
            specificDetails = err.cause
              .map(
                (e) =>
                  `(Row ${e?.row ?? "N/A"}) ${e?.errors || e?.message || e?.error || "Details unavailable"}`
              )
              .slice(0, 5);
            if (err.cause.length > 5)
              specificDetails.push("... (more in console)");
          } else if (
            // Check 'validationErrors' from the try block's scope
            Array.isArray(validationErrors) && // 'validationErrors' here refers to the one defined at the start of reader.onload
            validationErrors.length > 0
          ) {
            mainReason = mainReason.includes("Validation Errors")
              ? mainReason
              : "File validation failed.";
            specificDetails = validationErrors
              .map(
                (e) =>
                  `(Row ${e?.row ?? "N/A"}) ${e?.errors || "Details unavailable"}`
              )
              .slice(0, 5);
            if (validationErrors.length > 5)
              specificDetails.push("... (more errors in file)");
          }

          userMessage = `Upload Failed: ${mainReason}`;
          if (specificDetails.length > 0) {
            userMessage += ` Details: ${specificDetails.join("; ")}`;
          }

          if (mainReason.toLowerCase().includes("invalid header")) {
            userMessage += " Check template column names.";
          } else if (mainReason.includes("No valid data rows")) {
            userMessage += " Check file data format.";
          } else if (!navigator.onLine) {
            userMessage += " Check internet connection.";
          }
        } catch (formattingError) {
          console.error("[Upload Error] Formatting Error:", formattingError);
          userMessage = `Upload failed: ${err?.message || "An critical unknown error occurred."}`;
        }
        setFeedbackMessage({
          type: "error",
          title: "Disbursal Upload Failed",
          text: userMessage,
        });
      } finally {
        setIsUploading(false);
        setShowStatusModal(true); // Show confirmation/error modal
        try {
          if (disbursalFileRef?.current) disbursalFileRef.current.value = "";
        } catch (resetError) {
          console.error("Error resetting disbursal file input:", resetError);
        }
      }
    }; // end reader.onload

    reader.onerror = (err) => {
      console.error("[Upload Error] File Reading Error:", err);
      setFeedbackMessage({
        type: "error",
        title: "File Reading Error",
        text: `Error reading file: ${err?.message || "Could not read the selected file."}`,
      });
      setIsUploading(false);
      setShowStatusModal(true); // Show error modal
      try {
        if (disbursalFileRef?.current) disbursalFileRef.current.value = "";
      } catch (e) {
        console.error("Error resetting disbursal file input:", e);
      }
    };
    reader.readAsArrayBuffer(file);
  }; // --- END handleDisbursalUpload ---

  // --- Complete Repayment Upload Handler with Fixed Number Parsing ---
  const handleRepaymentUpload = async (event) => {
    const file = event.target.files[0];
    setShowUploadPromptModal(false); // Close prompt once file dialog interaction is done

    if (!file) {
      if (repaymentFileRef.current) repaymentFileRef.current.value = ""; // Reset if user cancelled
      return;
    }

    // Critical check for repayment context
    if (!selectedOfferForModal) { // This check is crucial
      setFeedbackMessage({
        type: "error",
        title: "Upload Error",
        text: "Repayment context is missing. Please ensure the EMI details modal is open and try again.",
      });
      setShowStatusModal(true);
      setIsUploading(false); // Stop loading indicator
      if (repaymentFileRef.current) repaymentFileRef.current.value = "";
      return;
    }

    setIsUploading(true);
    // setFeedbackMessage({ type: "info", title: "Processing...", text: "Reading and validating repayment file..." });

    const reader = new FileReader();
    reader.onload = async (e) => {
      const validationErrors = []; // Ensure this is scoped correctly
      let payload = [];           // Ensure this is scoped correctly

      try {
        const currentEmiDetails = selectedOfferForModal?.offerInfo?.emiDetails;
        if (!Array.isArray(currentEmiDetails)) {
          throw new Error(
            `EMI details not found or invalid for selected offer ${selectedOfferForModal?.offerInfo?.offerId}`
          );
        }
        const sortedCurrentEmiDetails = [...currentEmiDetails].sort(
          (a, b) => (a.emiNumber || 0) - (b.emiNumber || 0)
        );

        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array", cellDates: true });
        const sheetName = workbook.SheetNames[0];
        if (!sheetName) throw new Error("Cannot find sheet in Excel file.");
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          defval: null,
          raw: false,
        });

        if (!jsonData || jsonData.length < 2)
          throw new Error("Excel file appears empty or lacks a header row.");

        const header = jsonData[0].map((h) => String(h || "").trim());
        const expectedHeader = [
          "offerId",
          "emiNumber",
          "UTR",
          "paymentTimestamp",
          "paidAmount",
        ];
        if (JSON.stringify(header) !== JSON.stringify(expectedHeader)) {
          throw new Error(
            `Invalid header row. Expected columns: ${expectedHeader.join(", ")}`
          );
        }

        const dataRows = jsonData.slice(1);
        const parseNumericValue = (value) => {
          // ... (your existing parseNumericValue function)
          if (
            value === null ||
            value === undefined ||
            String(value).trim() === ""
          ) {
            return NaN;
          }
          const stringValue = String(value).trim();
          const normalizedValue = stringValue.replace(/,/g, "");
          return parseFloat(normalizedValue);
        };

        dataRows.forEach((row, index) => {
          const rowNum = index + 2;
          const offerIdFromFile = row?.[0]; // Renamed to avoid conflict
          const excelEmiNumberInput = row?.[1];
          const utr = row?.[2];
          const timestampInput = row?.[3];
          const paidAmountFromFile = row?.[4]; // Renamed

          let rowIsValid = true;
          let rowErrors = [];
          let parsedTimestamp = null;
          let parsedExcelEmiNum = NaN;
          let parsedPaidAmt = NaN;
          let targetEmiNumber = null;

          if (
            !offerIdFromFile ||
            !isValidObjectId(String(offerIdFromFile).trim()) ||
            String(offerIdFromFile).trim() !== selectedOfferForModal.offerInfo.offerId
          ) {
            rowErrors.push(
              `Invalid/Missing offerId or doesn't match selected offer (${selectedOfferForModal.offerInfo.offerId})`
            );
            rowIsValid = false;
          }
          parsedExcelEmiNum = parseNumericValue(excelEmiNumberInput);
          if (isNaN(parsedExcelEmiNum) || parsedExcelEmiNum <= 0) {
            rowErrors.push("Invalid/Missing positive emiNumber in Excel file");
            rowIsValid = false;
          }
          if (!utr || String(utr).trim() === "") {
            rowErrors.push("Missing UTR");
            rowIsValid = false;
          }
          if (
            timestampInput === null ||
            timestampInput === undefined ||
            String(timestampInput).trim() === ""
          ) {
            rowErrors.push("Missing paymentTimestamp");
            rowIsValid = false;
          } else {
            let date = new Date(timestampInput);
            if (
              isNaN(date.getTime()) &&
              typeof timestampInput === "number" &&
              timestampInput > 25569
            ) {
              const excelEpoch = new Date(1899, 11, 30);
              const jsTimestamp =
                excelEpoch.getTime() + timestampInput * 24 * 60 * 60 * 1000;
              date = new Date(jsTimestamp);
            }
            if (isNaN(date.getTime())) {
              rowErrors.push(
                "Invalid paymentTimestamp format (use YYYY-MM-DD, MM/DD/YYYY, or Excel date number)"
              );
              rowIsValid = false;
            } else {
              parsedTimestamp = date.toISOString();
            }
          }
          parsedPaidAmt = parseNumericValue(paidAmountFromFile);
          if (isNaN(parsedPaidAmt) || parsedPaidAmt < 0) {
            rowErrors.push("Invalid or missing non-negative paidAmount");
            rowIsValid = false;
          }

          if (rowIsValid) {
            const firstPendingEmi = sortedCurrentEmiDetails.find(
              (emi) => emi.rePaymentStatus === "PENDING"
            );
            if (!firstPendingEmi) {
              rowErrors.push(
                "Cannot apply payment: No pending EMIs found for this offer."
              );
              rowIsValid = false;
            } else {
              targetEmiNumber = firstPendingEmi.emiNumber;
              if (targetEmiNumber !== parsedExcelEmiNum) {
                console.log(
                  `FIFO Applied: Row ${rowNum} (Excel EMI# ${parsedExcelEmiNum}) payment is being applied to first pending EMI #${targetEmiNumber}`
                );
              }
            }
          }

          if (rowIsValid && targetEmiNumber !== null) {
            payload.push({
              offerId: String(offerIdFromFile).trim(),
              emiNumber: targetEmiNumber,
              utr: String(utr).trim(),
              paymentTimestamp: parsedTimestamp,
              paidAmount: parsedPaidAmt,
            });
          } else {
            if (rowErrors.length > 0) { // Only add to validationErrors if there are actual errors
              validationErrors.push({
                row: rowNum,
                errors: rowErrors.join("; "),
              });
            }
          }
        });

        if (payload.length === 0) {
          let errorMsg = `No valid data rows found for offer ${selectedOfferForModal.offerInfo.offerId}.`;
          if (validationErrors.length > 0) {
            const clientErrors = validationErrors
              .map((err) => `(Row ${err.row}) ${err.errors}`)
              .slice(0, 5);
            errorMsg += ` Validation Errors Found: ${clientErrors.join("; ")}`;
            if (validationErrors.length > 5)
              errorMsg += "; ... (more errors exist)";
          }
          throw new Error(errorMsg);
        }

        // setFeedbackMessage({ // Interim
        //   type: "info",
        //   title: "Processing",
        //   text: `Validation complete. Sending ${payload.length} valid record(s) to server...`,
        // });

        const response = await fetch(
          `${config.apiUrl}/ops/invoiceFinancing/offers/update-emi-repayments`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          }
        );
        const result = await response.json();

        if (!response.ok || !result.success) {
          const combinedErrors = [
            ...validationErrors,
            ...(result.errors || []).map((apiErr) => ({
              row: apiErr.recordIndex ?? "N/A",
              errors:
                apiErr.message ||
                apiErr.error ||
                "Unknown API error on this record",
            })),
          ];
          const errorMsg =
            result.message ||
            `Repayment Update API Error (Status: ${response.status})`;
          throw new Error(errorMsg, { cause: combinedErrors });
        }

        let successMsg =
          result.message ||
          `${result.updatedEmiCount || payload.length} EMIs processed successfully for offer ${selectedOfferForModal.offerInfo.offerId}.`;
        if (validationErrors.length > 0) // validationErrors from the initial file read
          successMsg += ` (${validationErrors.length} row(s) had validation errors before sending).`;

        setFeedbackMessage({
          type: "success",
          title: "Repayment File Submitted!",
          text: successMsg,
        });
        closeEmiModal();
        fetchData();
      } catch (err) {
        console.error("[Upload Error Scope] handleRepaymentUpload:", err);
        let userMessage = "Repayment upload failed. ";
        try {
          let mainReason = err?.message || "An unknown error occurred.";
          let specificDetails = [];

          if (err?.cause && Array.isArray(err.cause)) {
            mainReason = err.message || "API or Validation errors found.";
            specificDetails = err.cause
              .map(
                (e) =>
                  `(Row ${e?.row ?? "N/A"}) ${e?.errors || e?.message || e?.error || "Details unavailable"}`
              )
              .slice(0, 5);
            if (err.cause.length > 5)
              specificDetails.push("... (more in console)");
          } else if (
            Array.isArray(validationErrors) && // 'validationErrors' from the start of reader.onload
            validationErrors.length > 0
          ) {
            mainReason = mainReason.includes("Validation Errors")
              ? mainReason
              : "File validation failed.";
            specificDetails = validationErrors
              .map(
                (e) =>
                  `(Row ${e?.row ?? "N/A"}) ${e?.errors || "Details unavailable"}`
              )
              .slice(0, 5);
            if (validationErrors.length > 5)
              specificDetails.push("... (more errors in file)");
          }
          userMessage = `Upload Failed: ${mainReason}`;
          if (specificDetails.length > 0) {
            userMessage += ` Details: ${specificDetails.join("; ")}`;
          }
          if (mainReason.toLowerCase().includes("invalid header")) {
            userMessage += " Check template column names.";
          } else if (mainReason.includes("No valid data rows")) {
            userMessage += " Check file data format/Offer ID match.";
          } else if (!navigator.onLine) {
            userMessage += " Check internet connection.";
          }
        } catch (formattingError) {
          console.error("[Upload Error] Formatting Error:", formattingError);
          userMessage = `Upload failed: ${err?.message || "An critical unknown error occurred."}`;
        }
        setFeedbackMessage({
          type: "error",
          title: "Repayment Upload Failed",
          text: userMessage,
        });
      } finally {
        setIsUploading(false);
        setShowStatusModal(true); // Show confirmation/error modal
        try {
          if (repaymentFileRef?.current) repaymentFileRef.current.value = "";
        } catch (resetError) {
          console.error("Error resetting repayment file input:", resetError);
        }
      }
    }; // end reader.onload

    reader.onerror = (err) => {
      console.error("[Upload Error] File Reading Error:", err);
      setFeedbackMessage({
        type: "error",
        title: "File Reading Error",
        text: `Error reading file: ${err?.message || "Could not read the selected file."}`,
      });
      setIsUploading(false);
      setShowStatusModal(true); // Show error modal
      try {
        if (repaymentFileRef?.current) repaymentFileRef.current.value = "";
      } catch (e) {
        console.error("Error resetting repayment file input:", e);
      }
    };
    reader.readAsArrayBuffer(file);
  }; // --- END handleRepaymentUpload ---

  useEffect(() => {
    if (lenderId) {
      fetchData();
    }
  }, [lenderId, fetchData]);

  // --- Sorting and Filtering Logic ---
  // Define the desired status order arrays
  const disbursalStatusOrder = useMemo(
    () => [
      "LOAN_CONTRACT_ACCEPTED",
      "INITIATED_FUND_TRANSFER", // If used
      "READY_FOR_DISBURSAL", // If used
      "LOAN_IN_PROGRESS",
      "PAID",
      "LOAN_CANCELLED",
      "WRITTEN_OFF",
      "REJECTED",
      "EXPIRED",
      // PENDING is already excluded by API
    ],
    []
  );

  const repaymentStatusOrder = useMemo(
    () => [
      "LOAN_IN_PROGRESS",
      "PAID", // Added 'PAID' here based on requirement 'WRITTEN_OFF_PAID' likely meaning PAID or WRITTEN_OFF
      "WRITTEN_OFF",
      "LOAN_CANCELLED",
      // Less relevant but included for completeness if needed
      "READY_FOR_DISBURSAL",
      "INITIATED_FUND_TRANSFER",
      "LOAN_CONTRACT_ACCEPTED",
      "REJECTED",
      "EXPIRED",
    ],
    []
  );

  const getSortIndex = (status, orderArray) => {
    const upperStatus = status?.toUpperCase();
    const index = orderArray.indexOf(upperStatus);
    return index === -1 ? orderArray.length : index; // Put unknown/unlisted statuses at the end
  };

  // Memoized filtered and sorted data for Disbursal section
  const filteredSortedDisbursalData = useMemo(() => {
    // Destructure ALL filters from the single state object
    const {
      searchText,
      offerStatus,
      invoiceStatus,
      disbursalDateFrom,
      disbursalDateTo,
      minAmount,
      maxAmount,
    } = filters;
    const searchTextLower = searchText.toLowerCase().trim();
    const offerStatusLower = offerStatus?.toLowerCase();
    const invoiceStatusLower = invoiceStatus?.toLowerCase();
    const disbursalFromTs = disbursalDateFrom
      ? new Date(disbursalDateFrom).setHours(0, 0, 0, 0)
      : null;
    const disbursalToTs = disbursalDateTo
      ? new Date(disbursalDateTo).setHours(23, 59, 59, 999)
      : null;
    const numMinAmount = minAmount === "" ? -Infinity : parseFloat(minAmount);
    const numMaxAmount = maxAmount === "" ? Infinity : parseFloat(maxAmount);

    return offersData
      .filter((item) => {
        if (!item?.offerInfo?.status) return false; // Ensure offerInfo and status exist
        if (!disbursalStatusOrder.includes(item.offerInfo.status)) return false;

        // Apply Text Search Filter
        if (searchTextLower) {
          const merchantName = (
            item.merchantInfo?.businessName ||
            `${item.merchantInfo?.firstName || ""} ${item.merchantInfo?.lastName || ""
            }`
          ).toLowerCase();
          const matchesText =
            item.offerInfo?.offerId?.toLowerCase().includes(searchTextLower) ||
            item.merchantInfo?.merchantId
              ?.toLowerCase()
              .includes(searchTextLower) ||
            merchantName.includes(searchTextLower) ||
            item.invoiceInfo?.invoiceNumber
              ?.toLowerCase()
              .includes(searchTextLower);
          if (!matchesText) return false;
        }

        // Apply Offer Status Filter
        if (
          offerStatus &&
          item.offerInfo?.status?.toLowerCase() !== offerStatusLower
        ) {
          return false;
        }

        // Apply Invoice Status Filter
        if (
          invoiceStatus &&
          item.invoiceInfo?.invoiceStatus?.toLowerCase() !== invoiceStatusLower
        ) {
          return false;
        }

        // Apply Offer Amount Filter (using creditLimit as the offer amount field)
        const offerAmount = parseFloat(item.offerInfo?.creditLimit);
        if (
          !isNaN(numMinAmount) &&
          (isNaN(offerAmount) || offerAmount < numMinAmount)
        )
          return false;
        if (
          !isNaN(numMaxAmount) &&
          (isNaN(offerAmount) || offerAmount > numMaxAmount)
        )
          return false;

        // Apply Disbursal Date Filter
        const disbursementDate = item.offerInfo.disbursementDetails?.timestamp
          ? new Date(item.offerInfo.disbursementDetails.timestamp)
          : null;
        if (disbursementDate) {
          const disbursementTs = disbursementDate.getTime();
          if (disbursalFromTs && disbursementTs < disbursalFromTs) return false;
          if (disbursalToTs && disbursementTs > disbursalToTs) return false;
        } else {
          if (disbursalFromTs || disbursalToTs) return false; // Exclude if filtering by date but no date exists
        }

        return true;
      })
      .sort((a, b) => {
        // Keep existing sort logic
        const statusAIndex = getSortIndex(
          a.offerInfo.status,
          disbursalStatusOrder
        );
        const statusBIndex = getSortIndex(
          b.offerInfo.status,
          disbursalStatusOrder
        );
        if (statusAIndex !== statusBIndex) {
          return statusAIndex - statusBIndex;
        }
        const dateA =
          a.offerInfo.disbursementDetails?.timestamp || a.offerInfo.createdAt;
        const dateB =
          b.offerInfo.disbursementDetails?.timestamp || b.offerInfo.createdAt;
        try {
          return new Date(dateB) - new Date(dateA);
        } catch {
          // Newest first
          return 0;
        } // Fallback sort
      });
  }, [offersData, disbursalStatusOrder, filters]); // DEPEND ON THE SINGLE 'filters' OBJECT

  // Memoized filtered and sorted data for Repayment section
  const filteredSortedRepaymentData = useMemo(() => {
    // Destructure ALL filters
    const {
      searchText,
      offerStatus,
      nextDueDateFrom,
      nextDueDateTo,
      minAmount,
      maxAmount,
    } = filters; // InvoiceStatus/DisbursalDate not relevant here
    const searchTextLower = searchText.toLowerCase().trim();
    const offerStatusLower = offerStatus?.toLowerCase();
    const nextDueFromTs = nextDueDateFrom
      ? new Date(nextDueDateFrom).setHours(0, 0, 0, 0)
      : null;
    const nextDueToTs = nextDueDateTo
      ? new Date(nextDueDateTo).setHours(23, 59, 59, 999)
      : null;
    const numMinAmount = minAmount === "" ? -Infinity : parseFloat(minAmount);
    const numMaxAmount = maxAmount === "" ? Infinity : parseFloat(maxAmount);

    return offersData
      .filter((item) => {
        if (!item?.offerInfo?.status) return false; // Ensure offerInfo and status exist
        if (!repaymentStatusOrder.includes(item.offerInfo.status)) return false;

        // Apply Text Search Filter (same as above)
        if (searchTextLower) {
          const merchantName = (
            item.merchantInfo?.businessName ||
            `${item.merchantInfo?.firstName || ""} ${item.merchantInfo?.lastName || ""
            }`
          ).toLowerCase();
          const matchesText =
            item.offerInfo?.offerId?.toLowerCase().includes(searchTextLower) ||
            item.merchantInfo?.merchantId
              ?.toLowerCase()
              .includes(searchTextLower) ||
            merchantName.includes(searchTextLower) ||
            item.invoiceInfo?.invoiceNumber
              ?.toLowerCase()
              .includes(searchTextLower);
          if (!matchesText) return false;
        }

        // Apply Offer Status Filter
        if (
          offerStatus &&
          item.offerInfo?.status?.toLowerCase() !== offerStatusLower
        ) {
          return false;
        }

        // Apply Offer Amount Filter
        const offerAmount = parseFloat(item.offerInfo?.creditLimit);
        if (
          !isNaN(numMinAmount) &&
          (isNaN(offerAmount) || offerAmount < numMinAmount)
        )
          return false;
        if (
          !isNaN(numMaxAmount) &&
          (isNaN(offerAmount) || offerAmount > numMaxAmount)
        )
          return false;

        // Apply Next Due Date Filter
        const sortedEmis =
          item.offerInfo.emiDetails?.sort(
            (a, b) => (a.emiNumber ?? 0) - (b.emiNumber ?? 0)
          ) || [];
        const nextPendingEmi = sortedEmis.find(
          (emi) => emi.rePaymentStatus === "PENDING"
        );
        let nextDueDateTs = null;
        try {
          if (nextPendingEmi?.rePaymentDate)
            nextDueDateTs = new Date(nextPendingEmi.rePaymentDate).getTime();
        } catch {
          nextDueDateTs = null;
        }

        if (nextDueDateTs) {
          if (nextDueFromTs && nextDueDateTs < nextDueFromTs) return false;
          if (nextDueToTs && nextDueDateTs > nextDueToTs) return false;
        } else {
          if (nextDueFromTs || nextDueToTs) return false; // Exclude if filtering by date but no date exists
        }

        return true; // Item passes all filters
      })
      .sort((a, b) => {
        // Keep existing sort logic
        const statusAIndex = getSortIndex(
          a.offerInfo.status,
          repaymentStatusOrder
        );
        const statusBIndex = getSortIndex(
          b.offerInfo.status,
          repaymentStatusOrder
        );
        if (statusAIndex !== statusBIndex) {
          return statusAIndex - statusBIndex;
        }
        const dateA =
          a.offerInfo.disbursementDetails?.timestamp || a.offerInfo.createdAt;
        const dateB =
          b.offerInfo.disbursementDetails?.timestamp || b.offerInfo.createdAt;
        try {
          return new Date(dateB) - new Date(dateA);
        } catch {
          // Newest first
          return 0;
        } // Fallback sort
      });
  }, [offersData, repaymentStatusOrder, filters]); // DEPEND ON THE SINGLE 'filters' OBJECT

  // --- Contract PDF View Handler ---
  const handleViewContractClick = (signedUrl) => {
    if (!signedUrl) {
      setFeedbackMessage({
        type: "error",
        text: "Contract signed URL is missing.",
      });
      return;
    }
    try {
      window.open(signedUrl, "_blank", "noopener,noreferrer");
    } catch (err) {
      console.error("Error opening signed URL:", err);
      setFeedbackMessage({
        type: "error",
        text: "Could not open contract link.",
      });
    }
  };

  // --- Excel Download Handlers ---
  // ... (keep existing handleDownloadDisbursalTemplate and handleDownloadRepaymentTemplate)

  const handleDownloadPendingDisbursals = () => {
    setFeedbackMessage({
      type: "info",
      text: "Generating pending disbursals report...",
    });

    // Filter data for offers pending disbursal (adjust statuses as needed)
    const pendingDisbursals = offersData.filter(
      (item) =>
        item.invoiceInfo?.invoiceStatus?.toUpperCase() ===
        "READY_FOR_DISBURSAL" &&
        item.offerInfo?.status?.toUpperCase() === "LOAN_CONTRACT_ACCEPTED"
    );

    if (pendingDisbursals.length === 0) {
      setFeedbackMessage({
        type: "warning",
        text: "No offers currently pending disbursal.",
      });
      return;
    }

    // Prepare data for Excel
    const dataForExcel = [
      // UPDATED: Added "Account Number", "IFSC Code" headers
      [
        "Offer ID",
        "Merchant Name",
        "Merchant ID",
        "Invoice #",
        "Disbursal Amount",
        "Offer Status",
        "Contract Accepted Date",
        "Account Number",
        "IBAN",
      ],
    ];

    pendingDisbursals.forEach((item) => {
      dataForExcel.push([
        item.offerInfo.offerId ?? "N/A",
        item.merchantInfo.businessName ||
        `${item.merchantInfo.firstName || ""} ${item.merchantInfo.lastName || ""
        }`,
        item.merchantInfo.merchantId ?? "N/A",
        item.invoiceInfo.invoiceNumber ?? "N/A",
        // Use the expected disbursal amount from contract details or fallback
        item.offerInfo.contractDetails?.disbursementAmount ??
        item.offerInfo.contractDetails?.discountedAmount ??
        "N/A",
        item.offerInfo.status ?? "N/A",
        // Use contract sign date or offer creation date as fallback for contract accepted date
        formatDate(
          item.offerInfo.contractDetails?.contractSignDate ??
          item.offerInfo.createdAt
        ),
        // ADDED: Populate Account Number and IFSC Code from merchantInfo
        item.merchantInfo.bankAccountNumber ?? "N/A",
        item.merchantInfo.ifscCode ?? "N/A",
      ]);
    });

    // Create worksheet and workbook
    const worksheet = XLSX.utils.aoa_to_sheet(dataForExcel);
    // UPDATED: Adjusted column widths array to include the new columns
    worksheet["!cols"] = [
      { wch: 25 }, // Offer ID
      { wch: 25 }, // Merchant Name
      { wch: 25 }, // Merchant ID
      { wch: 15 }, // Invoice #
      { wch: 18 }, // Disbursal Amount
      { wch: 20 }, // Offer Status
      { wch: 15 }, // Contract Accepted Date
      { wch: 20 }, // Account Number (NEW)
      { wch: 20 }, // IFSC Code (NEW)
    ];

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Pending Disbursals");

    // Generate Excel buffer and trigger download
    try {
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const dataBlob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
      });
      saveAs(dataBlob, "pending_disbursals_report.xlsx");
      setFeedbackMessage({
        type: "success",
        text: "Pending disbursals report downloaded successfully.",
      });
    } catch (error) {
      console.error("Error generating pending disbursals Excel:", error);
      setFeedbackMessage({
        type: "error",
        text: "Failed to generate pending disbursals report.",
      });
    }
  };
  const handleDownloadPendingEmis = () => {
    setFeedbackMessage({
      type: "info",
      text: "Generating pending EMIs report...",
    });

    const pendingEmisData = [
      // Headers
      [
        "Offer ID",
        "Merchant Name",
        "Merchant ID",
        "Invoice #",
        "EMI #",
        "Due Date",
        "Amount Due",
        "Offer Status",
      ],
    ];

    let foundPending = false;
    // Iterate through offers relevant for repayment
    filteredSortedRepaymentData.forEach((item) => {
      if (
        item.offerInfo.emiDetails &&
        Array.isArray(item.offerInfo.emiDetails)
      ) {
        item.offerInfo.emiDetails.forEach((emi) => {
          if (emi.rePaymentStatus?.toUpperCase() === "PENDING") {
            foundPending = true;
            pendingEmisData.push([
              item.offerInfo.offerId,
              item.merchantInfo.businessName ||
              `${item.merchantInfo.firstName} ${item.merchantInfo.lastName}`,
              item.merchantInfo.merchantId,
              item.invoiceInfo.invoiceNumber,
              emi.emiNumber,
              formatDate(emi.rePaymentDate),
              emi.rePaymentAmount ?? "N/A", // Assuming this field holds the amount due
              item.offerInfo.status, // Include offer status for context
            ]);
          }
        });
      }
    });

    if (!foundPending) {
      setFeedbackMessage({
        type: "warning",
        text: "No pending EMIs found across active loans.",
      });
      return;
    }

    // Create worksheet and workbook
    const worksheet = XLSX.utils.aoa_to_sheet(pendingEmisData);
    // Optional: Set column widths (example)
    worksheet["!cols"] = [
      { wch: 25 },
      { wch: 25 },
      { wch: 15 },
      { wch: 15 },
      { wch: 8 },
      { wch: 12 },
      { wch: 15 },
      { wch: 20 },
    ];

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Pending EMIs");

    // Generate Excel buffer and trigger download
    try {
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const dataBlob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
      });
      saveAs(dataBlob, "pending_emis_report.xlsx");
      setFeedbackMessage({
        type: "success",
        text: "Pending EMIs report downloaded successfully.",
      });
    } catch (error) {
      console.error("Error generating pending EMIs Excel:", error);
      setFeedbackMessage({
        type: "error",
        text: "Failed to generate pending EMIs report.",
      });
    }
  };

  // --- Excel Download Handlers ---
  const handleDownloadDisbursalTemplate = () => {
    const currentTimestamp = new Date().toISOString(); // ISO format: yyyy-mm-ddThh:mm:ss.sssZ

    // Define header and first row with current timestamp
    const worksheet = XLSX.utils.aoa_to_sheet([
      ["UTR", "invoiceOfferId", "disbursementTimestamp", "disbursalAmount"],
      ["", "", currentTimestamp, ""], // First row of data
    ]);

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Disbursal Data");

    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });

    const dataBlob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
    });

    saveAs(dataBlob, "disbursal_upload_template.xlsx");

    setFeedbackMessage({
      type: "info",
      text: "Disbursal template downloaded.",
    });
  };

  const handleDownloadRepaymentTemplate = () => {
    const worksheet = XLSX.utils.aoa_to_sheet([
      ["offerId", "emiNumber", "UTR", "paymentTimestamp", "paidAmount"], // Header row
      ["", "", "", "2024-02-11T18:31:44", ""] // First data row with the specified paymentTimestamp
    ]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Repayment Data");
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const dataBlob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
    });
    saveAs(dataBlob, "repayment_upload_template.xlsx"); // More specific name
    setFeedbackMessage({
      type: "info",
      text: "Repayment template downloaded.",
    });
  };

  // --- Modal Control ---
  const closeEmiModal = () => {
    setShowEmiModal(false);
    setSelectedOfferForModal(null);
  };
  // --- Render Logic ---
  // Loading State
  if (loading) {
    return (
      <LoadingModal />
    );
  }
  // Error State
  if (error) {
    return (
      <div className="p-6 bg-red-100 text-red-800 rounded-lg shadow-md max-w-lg mx-auto mt-10">
        {" "}
        <h2 className="text-xl font-semibold mb-3">Error</h2>{" "}
        <p className="mb-4">{error}</p>{" "}
        <button
          onClick={fetchData}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition duration-150"
          disabled={!lenderId}
        >
          {" "}
          Retry{" "}
        </button>{" "}
      </div>
    );
  }

  // Main component render
  return (
    <div className="p-4 md:p-6 bg-gray-50 min-h-screen w-[83vw] overflow-hidden">
      {isUploading && <LoadingModal />} {/* THIS LINE IS KEY FOR THE LOADING SPINNER DURING UPLOAD */}
      <h1 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2 sm:mb-0">
        Disbursal and Repayment
      </h1>
      {/* Feedback Area */}
      {feedbackMessage.text && (
        <div
          className={`p-4 mb-4 mt-4 rounded-md text-sm shadow ${feedbackMessage.type === "error"
            ? "bg-red-100 text-red-700"
            : feedbackMessage.type === "success"
              ? "bg-green-100 text-green-700"
              : "bg-blue-100 text-blue-700"
            }`}
        >
          {feedbackMessage.text}
        </div>
      )}

      <DisbursalRepaymentFilterSection
        filters={filters}
        setFilters={setFilters} // Pass the main setFilters
        resetFilters={resetFilters} // Pass the main resetFilters
        uniqueOfferStatuses={uniqueOfferStatuses} // Pass pre-calculated unique statuses
        uniqueInvoiceStatuses={uniqueInvoiceStatuses} // Pass pre-calculated unique statuses
      />

      {/* Section Tabs */}
      <div className="mt-6 mb-6 flex space-x-2 w-full">
        <button
          className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeSection === "disbursal"
              ? "border-[#b0cfbf] text-black bg-white"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          onClick={() => setActiveSection("disbursal")}
        >
          Disbursal Management
        </button>
        <button
          className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm focus:outline-none
                        ${activeSection === "repayment"
              ? "border-[#b0cfbf] text-black bg-white"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          onClick={() => setActiveSection("repayment")}
        >
          Repayment Tracking
        </button>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-lg w-full max-w-full border border-gray-300">
        {/* Disbursal Section */}
        {activeSection === "disbursal" && (
          <div className="w-full">
            <div className="flex flex-col p-4 md:flex-row items-start md:items-center justify-between pb-4 border-b border-gray-300">
              <h2 className="text-xl font-semibold text-gray-700 flex items-center mb-3 md:mb-0">
                Disbursals Overview
                <InfoIcon tooltipText="Upload Excel with columns: UTR, invoiceOfferId, disbursementTimestamp, disbursalAmount. Updates status to LOAN_IN_PROGRESS (client-side)." />
              </h2>
              <div className="flex flex-wrap items-center gap-3">
                <button
                  onClick={handleDownloadDisbursalTemplate}
                  className="px-4 py-2 bg-white text-gray-800 border border-gray-200 rounded shadow hover:bg-gray-100 transition duration-150 text-sm font-medium whitespace-nowrap flex items-center"
                >
                  <ArrowDownTrayIcon className="w-4 h-4 mr-2" /> Download Template (.xlsx)
                </button>

                <button
                  onClick={handleDownloadPendingDisbursals}
                  className="px-4 py-2 bg-white text-gray-800 border border-gray-200 rounded shadow hover:bg-gray-100 transition duration-150 text-sm font-medium whitespace-nowrap flex items-center"
                >
                  <ArrowDownTrayIcon className="w-4 h-4 mr-2" /> Download Pending Disbursals
                </button>

                <div>
                  <button
                    onClick={() => {
                      setCurrentUploadType('disbursal');
                      setShowUploadPromptModal(true);
                      // DO NOT call disbursalFileRef.current.click() here anymore
                    }}
                    className="px-4 py-2 bg-[#1a9c54] text-white rounded shadow hover:bg-[#157a44] transition duration-150 text-sm font-medium cursor-pointer whitespace-nowrap flex items-center"
                  >
                    <ArrowUpTrayIcon className="w-4 h-4 mr-2" /> Upload Disbursal File
                  </button>
                  <input
                    id="disbursal-upload"
                    ref={disbursalFileRef}
                    type="file"
                    accept=".xlsx, .xls, .csv"
                    className="hidden"
                    onChange={handleDisbursalUpload} // This handler remains
                  />
                </div>
              </div>
            </div>

            <div className="p-3 bg-gray-200 rounded-b-lg"> {/* Added padding and gray background */}
              <div className="overflow-x-auto w-full custom-h-scrollbar">
                <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
                  <thead className="bg-white">
                    <tr>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Offer ID
                      </th>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Merchant
                      </th>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Contact
                      </th>
                      <th className="px-2 md:px-4 py-3 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Disbursal Amt.
                      </th>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Invoice#
                      </th>
                      <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Invoice Status
                      </th>
                      <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Offer Status
                      </th>
                      <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Contract
                      </th>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Disbursed Info
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSortedDisbursalData.length > 0 ? (
                      filteredSortedDisbursalData.map((item) => (
                        <tr key={item.clientId} className="hover:bg-blue-50">
                          <td
                            className="px-2 md:px-4 py-4 whitespace-nowrap text-xs text-gray-500 font-mono"
                            title={item.offerInfo.offerId}
                          >
                            {item.offerInfo.offerId}
                          </td>
                          <td className="px-2 md:px-4 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {item.merchantInfo.businessName ||
                                `${item.merchantInfo.firstName} ${item.merchantInfo.lastName}`}
                            </div>
                            <div className="text-xs text-gray-500">
                              {item.merchantInfo.merchantId}
                            </div>
                          </td>
                          <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div title={item.merchantInfo.email}>
                              {item.merchantInfo.email}
                            </div>
                            <div>{item.merchantInfo.mobileNo}</div>
                          </td>
                          <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold text-right">
                            {formatCurrency(
                              item.offerInfo.disbursementDetails?.disbursedAmount ??
                              item.offerInfo.contractDetails?.disbursementAmount ??
                              item.offerInfo.contractDetails?.discountedAmount
                            )}
                          </td>
                          <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.invoiceInfo.invoiceNumber}
                          </td>
                          <td className="px-2 md:px-4 py-4 whitespace-nowrap text-center">
                            <StatusBadge
                              status={item.invoiceInfo.invoiceStatus}
                            />
                          </td>
                          <td className="px-2 md:px-4 py-4 whitespace-nowrap text-center">
                            <StatusBadge status={item.offerInfo.status} />
                          </td>
                          <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-center">
                            {item.offerInfo.invoiceContract?.signedUrl ? (
                              <button
                                onClick={() =>
                                  handleViewContractClick(
                                    item.offerInfo.invoiceContract.signedUrl
                                  )
                                }
                                className="text-[#004141] hover:text-[#208039] underline text-xs font-medium"
                              >
                                View
                              </button>
                            ) : (
                              <span className="text-xs text-gray-400">N/A</span>
                            )}
                          </td>
                          <td className="px-2 md:px-4 py-4 whitespace-nowrap text-xs text-gray-500">
                            {item.offerInfo.disbursementDetails ? (
                              <>
                                UTR:{" "}
                                {item.offerInfo.disbursementDetails.utr || "N/A"}{" "}
                                <br />
                                Date:{" "}
                                {formatDate(
                                  item.offerInfo.disbursementDetails.timestamp
                                )}{" "}
                                <br />
                                Uploaded:{" "}
                                {formatDate(
                                  item.offerInfo.disbursementDetails.uploadedOn
                                )}
                              </>
                            ) : item.offerInfo.status ===
                              "LOAN_CONTRACT_ACCEPTED" ||
                              item.offerInfo.status === "READY_FOR_DISBURSAL" ? (
                              <span className="text-yellow-600">
                                Pending Upload
                              </span>
                            ) : (
                              "-"
                            )}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan="9"
                          className="text-center py-5 text-gray-500 bg-white" // ensure background is white for no data row
                        >
                          No offers found matching disbursal criteria.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Repayment Section */}
        {activeSection === "repayment" && (
          <div className="w-full">
            <div className="flex  p-4 flex-col md:flex-row items-start md:items-center justify-between pb-4 border-b border-gray-300">
              <h2 className="text-xl font-semibold text-gray-700 flex items-center mb-3 md:mb-0">
                Repayment Tracking
                <InfoIcon tooltipText="View active loans and repayment status. Use 'Update EMIs' to view details and upload Excel with columns: offerId, emiNumber, UTR, paymentTimestamp, paidAmount." />
              </h2>

              <div className="flex flex-wrap items-center gap-3">
                <button
                  onClick={handleDownloadPendingEmis}
                  className="px-4 py-2 bg-white text-gray-800 border border-gray-200 rounded shadow hover:bg-gray-100 transition duration-150 text-sm font-medium flex items-center"
                >
                  <ArrowDownTrayIcon className="w-4 h-4 mr-2" /> Download Pending EMIs
                </button>
              </div>
            </div>

            <div className="p-3 bg-gray-200 rounded-b-lg"> {/* Added padding and gray background */}
              <div className="overflow-x-auto w-full custom-h-scrollbar">
                <table className="min-w-full divide-y divide-gray-200 border border-gray-200">
                  <thead className="bg-white">
                    <tr>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Merchant
                      </th>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Offer ID
                      </th>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Invoice#
                      </th>
                      <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Offer Status
                      </th>
                      <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Total EMIs
                      </th>
                      <th className="px-2 md:px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Next Due Date
                      </th>
                      <th className="px-2 md:px-4 py-3 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSortedRepaymentData.length > 0 ? (
                      filteredSortedRepaymentData.map((item) => {
                        const sortedEmis =
                          item.offerInfo.emiDetails?.sort(
                            (a, b) => a.emiNumber - b.emiNumber
                          ) || [];
                        const nextPendingEmi = sortedEmis.find(
                          (emi) => emi.rePaymentStatus === "PENDING"
                        );
                        return (
                          <tr key={item.clientId} className="hover:bg-blue-50">
                            <td className="px-2 md:px-4 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">
                                {item.merchantInfo.businessName ||
                                  `${item.merchantInfo.firstName} ${item.merchantInfo.lastName}`}
                              </div>
                              <div
                                className="text-xs text-gray-500"
                                title={item.merchantInfo.merchantId}
                              >
                                {item.merchantInfo.merchantId}
                              </div>
                            </td>
                            <td
                              className="px-2 md:px-4 py-4 whitespace-nowrap text-xs text-gray-500 font-mono"
                              title={item.offerInfo.offerId}
                            >
                              {item.offerInfo.offerId}
                            </td>
                            <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                              {item.invoiceInfo.invoiceNumber}
                            </td>
                            <td className="px-2 md:px-4 py-4 whitespace-nowrap text-center">
                              <StatusBadge status={item.offerInfo.status} />
                            </td>
                            <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                              {sortedEmis.length || 0}
                            </td>
                            <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                              {nextPendingEmi
                                ? formatDate(nextPendingEmi.rePaymentDate)
                                : "N/A"}
                            </td>
                            <td className="px-2 md:px-4 py-4 whitespace-nowrap text-sm text-center">
                              <button
                                onClick={() => navigateToUpdateEmiPage(item)}
                                className="px-3 py-1 bg-[#004141] text-white rounded hover:bg-[#208039] disabled:opacity-50 disabled:cursor-not-allowed transition duration-150 text-xs font-medium"
                                disabled={
                                  !item.offerInfo.emiDetails ||
                                  item.offerInfo.emiDetails.length === 0
                                }
                              >
                                Update EMIs
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td
                          colSpan="7"
                          className="text-center py-5 text-gray-500 bg-white" // ensure background is white for no data row
                        >
                          No offers found matching repayment criteria.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* EMI Update Modal */}
      {showEmiModal && selectedOfferForModal && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-75 flex justify-center items-center z-50 p-4 overflow-y-auto transition-opacity duration-300 ease-out"
          style={{ opacity: 1 }}
        >
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col transform transition-all duration-300 ease-out scale-100">
            {/* Modal Header */}
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
              <h2 className="text-lg font-semibold text-gray-800">
                Update EMI Status for Offer:{" "}
                <span className="font-mono">
                  {selectedOfferForModal.offerInfo.offerId}
                </span>
                <span className="block text-sm text-gray-500 font-normal">
                  Merchant:{" "}
                  {selectedOfferForModal.merchantInfo.businessName ||
                    `${selectedOfferForModal.merchantInfo.firstName} ${selectedOfferForModal.merchantInfo.lastName}`}{" "}
                  | Invoice: {selectedOfferForModal.invoiceInfo.invoiceNumber}
                </span>
              </h2>
              <button
                onClick={closeEmiModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Modal Body */}
            <div className="flex-grow overflow-y-auto p-6">
              <div className="flex flex-wrap lg:flex-nowrap items-center mb-4 gap-3">
                <button
                  onClick={handleDownloadRepaymentTemplate}
                  className="px-4 py-2 bg-[#004141] text-white rounded shadow hover:bg-[#208039] transition duration-150 text-sm font-medium"
                >
                  Download Template (.xlsx)
                </button>
                <div className="flex items-center ml-auto">
                  <button // Changed from label to button for semantic clarity, or keep label and adjust onClick
                    onClick={() => {
                      setCurrentUploadType('repayment');
                      setShowUploadPromptModal(true);
                      // DO NOT call repaymentFileRef.current.click() here anymore
                    }}
                    className="text-center cursor-pointer px-4 py-2 bg-[#004141] text-white rounded shadow hover:bg-[#208039] transition duration-150 text-sm font-medium"
                  >
                    Upload Repayments File
                  </button>
                  <input
                    id="repayment-upload"
                    ref={repaymentFileRef}
                    type="file"
                    accept=".xlsx, .xls, .csv"
                    className="hidden"
                    onChange={handleRepaymentUpload}
                  />
                  <InfoIcon tooltipText="Upload Excel with columns: offerId, emiNumber, UTR, paymentTimestamp, paidAmount." />
                </div>
              </div>

              {/* EMI Table */}
              <div className="overflow-x-scroll mt-4 w-full border rounded-md custom-h-scrollbar">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="px-2 md:px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        EMI #
                      </th>
                      <th className="px-2 md:px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Due Date
                      </th>
                      <th className="px-2 md:px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Amount Due
                      </th>
                      <th className="px-2 md:px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Principal
                      </th>
                      <th className="px-2 md:px-3 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Service Fee
                      </th>
                      <th className="px-2 md:px-3 py-2 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-2 md:px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Paid Date
                      </th>
                      <th className="px-2 md:px-3 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        Details (UTR)
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {Array.isArray(
                      selectedOfferForModal.offerInfo.emiDetails
                    ) &&
                      selectedOfferForModal.offerInfo.emiDetails.length > 0 ? (
                      [...selectedOfferForModal.offerInfo.emiDetails]
                        .sort((a, b) => a.emiNumber - b.emiNumber)
                        .map((emi) => (
                          <tr
                            key={emi._id || emi.emiNumber}
                            className="hover:bg-blue-50 text-xs"
                          >
                            <td className="px-2 md:px-3 py-2 whitespace-nowrap font-medium text-gray-700">
                              {emi.emiNumber}
                            </td>
                            <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600">
                              {formatDate(emi.rePaymentDate)}
                            </td>
                            <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-800 font-medium text-right">
                              {formatCurrency(emi.rePaymentAmount)}
                            </td>
                            <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600 text-right">
                              {formatCurrency(emi.principalRecovered, "")}
                            </td>
                            <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600 text-right">
                              {formatCurrency(emi.interestAmount, "")}
                            </td>
                            <td className="px-2 md:px-3 py-2 whitespace-nowrap text-center">
                              <StatusBadge
                                status={emi.rePaymentStatus || "N/A"}
                              />
                            </td>
                            <td className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-600">
                              {formatDate(emi.rePaymentActualDate)}
                            </td>
                            <td
                              className="px-2 md:px-3 py-2 whitespace-nowrap text-gray-500 text-ellipsis overflow-hidden"
                              title={emi.rePaymentReceivedSource || ""}
                            >
                              {emi.rePaymentReceivedSource}
                            </td>
                          </tr>
                        ))
                    ) : (
                      <tr>
                        <td
                          colSpan="8"
                          className="text-center py-4 text-gray-500 text-sm"
                        >
                          No EMI details available for this offer.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end sticky bottom-0">
              <button
                onClick={closeEmiModal}
                className="px-4 py-2 bg-gray-300 text-gray-800 rounded shadow hover:bg-gray-400 transition duration-150 font-medium"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      {showUploadPromptModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex justify-center items-center z-50 p-4 transition-opacity duration-300 ease-out" style={{ opacity: 1 }}>
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md transform transition-all duration-300 ease-out scale-100">
            {/* Modal Header */}
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">
                {currentUploadType === 'disbursal' ? "Upload Disbursal File" : "Upload Repayment File"}
              </h2>
              <button
                onClick={() => setShowUploadPromptModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6 text-center">
              <p className="text-sm text-gray-500 mb-6">
                Please upload the updated template
              </p>
              <div
                className="mb-6 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center gap-4 p-6 hover:border-gray-400 cursor-pointer transition-colors"
                style={{ backgroundColor: '#eef6f8' }}
                onClick={() => {
                  if (currentUploadType === 'disbursal' && disbursalFileRef.current) {
                    disbursalFileRef.current.click();
                  } else if (currentUploadType === 'repayment' && repaymentFileRef.current) {
                    repaymentFileRef.current.click();
                  }
                }}
              >
                <ArrowUpTrayIcon className="w-6 h-6 text-gray-500" />
                <div className="text-left">
                  <p className="text-base font-semibold text-gray-700">Upload</p>
                  <p className="text-xs text-gray-500">Max filesize 10MB</p>
                </div>
              </div>

              <p className="text-sm text-gray-500 mb-4">
                Please use below template file to update the{" "}
                {currentUploadType === 'disbursal' ? "disbursals" : "repayments"}.
              </p>
              <button
                onClick={() => {
                  if (currentUploadType === 'disbursal') {
                    handleDownloadDisbursalTemplate();
                  } else {
                    handleDownloadRepaymentTemplate();
                  }
                }}
                className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {currentUploadType === 'disbursal' ? "Disbursal File Template (.xlsx)" : "Repayment File Template (.xlsx)"}
                <ArrowDownTrayIcon className="ml-2 h-5 w-5 text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      )}
      {showStatusModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex justify-center items-center z-50 p-4 transition-opacity duration-300 ease-out" style={{ opacity: 1 }}>
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md text-center transform transition-all duration-300 ease-out scale-100">
            <div className="pt-5 pr-5 flex justify-end items-center"> {/* Adjusted for close button styling */}
              <button
                onClick={() => {
                  setShowStatusModal(false);
                  setFeedbackMessage({ type: "", text: "", title: "" }); // Reset feedback
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="p-6 pt-0"> {/* Adjusted padding */}
              {feedbackMessage.type === 'success' && (
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                  {/* Green Check SVG Icon */}
                  <svg className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              )}
              {feedbackMessage.type === 'error' && (
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                  {/* Red X/Error SVG Icon */}
                  <svg className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                  </svg>
                </div>
              )}
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {feedbackMessage.title || (feedbackMessage.type === 'success' ? "Success!" : "Error!")}
              </h3>
              <p className="text-sm text-gray-600 mb-6 whitespace-pre-wrap"> {/* Allow multi-line messages */}
                {feedbackMessage.text}
              </p>
              <button
                onClick={() => {
                  setShowStatusModal(false);
                  setFeedbackMessage({ type: "", text: "", title: "" }); // Reset feedback
                }}
                className="w-full sm:w-auto px-6 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-800 transition duration-150 font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

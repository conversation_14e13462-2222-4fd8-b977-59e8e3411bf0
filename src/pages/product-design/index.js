
export default function ProductDesign() {
  const sections = [
    {
      title: 'Focus Sectors',
      items: ['Manufacturing', 'Services', 'Trading', 'Agriculture']
    },
    {
      title: 'Credit Matrix',
      items: ['Credit Score Range', 'Revenue Requirements', 'Business Vintage']
    },
    {
      title: 'Rule Engine',
      items: ['FI Matching Criteria', 'Risk Assessment Rules', 'Approval Conditions']
    },
    {
      title: 'Money Flow',
      items: ['Mandate Setup', 'Disbursement Process', 'Repayment Channels']
    }
  ];

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Product Design</h1>
      <div className="grid grid-cols-2 gap-6">
        {sections.map((section) => (
          <div key={section.title} className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">{section.title}</h2>
            <ul className="space-y-2">
              {section.items.map((item) => (
                <li key={item} className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  {item}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
}